import json

from langchain_core.prompts import PromptTemplate

from app.config.basic_config import llm_groq
from app.frameworks.openperflex.prompts import search_prompt_system, relevant_prompt_system


async def get_answer(query, contexts, date_context):
    # Create a system prompt using <PERSON><PERSON><PERSON><PERSON>'s PromptTemplate
    system_prompt_search = PromptTemplate(input_variables=["date_today"], template=search_prompt_system)

    messages = [
        ("system", system_prompt_search.format(date_today=date_context)),
        ("user", f"User Question: {query}\n\nCONTEXTS:\n\n{contexts}")
    ]

    try:
        # Generate streaming response using Lang<PERSON>hain
        stream = llm_groq.astream(messages)  # Using `stream` for incremental token streaming

        async for chunk in stream:
            if chunk.content:
                yield chunk.content

    except Exception as e:
        print(f"Error during get_answer call: {e}")
        yield json.dumps({
            'type': 'error',
            'data': "We are currently experiencing some issues. Please try again later."
        }) + "\n\n"

async def get_relevant_questions(contexts, query):
    try:
        messages = [
            ("system", relevant_prompt_system),
            ("user", f"User Query: {query}\n\nContexts:\n{contexts}\n")
        ]

        # Generate response using LangChain invoke
        response = await llm_groq.ainvoke(messages)

        return response.content
    except Exception as e:
        print(f"Error during RELEVANT QUESTIONS ***************: {e}")
        return {}
