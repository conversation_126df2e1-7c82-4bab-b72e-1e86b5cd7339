from typing import Dict, Any, Optional, List

import requests
from langchain_google_community import GoogleSearchAPIWrapper

# use ENV variables
# Constants

DEFAULT_LOCATION = 'us'


async def get_sources(query: str, pro_mode: bool = False, stored_location: Optional[str] = None) -> Dict[str, Any]:
    """
    Fetch search results from Serper API.

    :param query: Search query string
    :param pro_mode: Boolean to determine the number of results
    :param stored_location: Optional location string
    :return: Dictionary containing search results
    """
    try:
        search_location = (stored_location or DEFAULT_LOCATION).lower()
        num_results = 10 if pro_mode else 20

        api_wrapper = GoogleSearchAPIWrapper(k=num_results)
        data = api_wrapper.results(query=query, num_results=num_results, search_params={"gl":search_location})

        return {
            'organic': extract_fields(data, ['title', 'link', 'snippet', 'date']),

        }

    except requests.RequestException as e:
        print(f"HTTP error while getting sources: {e}")
    except Exception as e:
        print(f"Unexpected error while getting sources: {e}")

    return {}


def extract_fields(items: List[Dict[str, Any]], fields: List[str]) -> List[Dict[str, Any]]:
    """
    Extract specified fields from a list of dictionaries.

    :param items: List of dictionaries
    :param fields: List of fields to extract
    :return: List of dictionaries with only the specified fields
    """
    return [{key: item[key] for key in fields if key in item} for item in items]
