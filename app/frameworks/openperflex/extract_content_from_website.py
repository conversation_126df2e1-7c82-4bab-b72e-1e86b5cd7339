from langchain_community.document_loaders import WebBaseLoader



# Adjust the extract_website_content function to be async to make it fully async:
async def extract_website_content(url):
    """
    Extracts and cleans the main content from a given website URL asynchronously.

    Args:
    url (str): The URL of the website from which to extract content.

    Returns:
    str: The first 4000 characters of the cleaned main content if it is sufficiently long, otherwise an empty string.
    """
    try:
        clean_text = []
        loader = WebBaseLoader(url)
        data = await loader.aload()  # Use aload for async loading

        for doc in data:
            if doc.page_content:
                clean_text.append(doc.page_content.replace("\n", ""))

        clean_text = "".join(clean_text)

        return clean_text[:4000] if len(clean_text) > 200 else ""

    except Exception as error:
        print('Error extracting main content:', error)
        return ""
