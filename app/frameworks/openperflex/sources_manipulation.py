from time import time
from typing import List, Union, Any

from langchain_community.document_loaders import WebBaseLoader
from langchain_community.document_loaders.web_base import _build_metadata
from langchain_core.documents import Document

from app.core.tracing_setup import logger


async def extract_multiple_website_contents(urls):
    """
    Extracts and cleans the main content from multiple website URLs asynchronously.

    Args:
    urls (list): A list of URLs from which to extract content.

    Returns:
    dict: A dictionary with URLs as keys and their extracted content as values.
    """
    try:
        start_time = time()
        loader = WebBaseLoader(urls)

        async def scrape_all(loader, urls: List[str], parser: Union[str, None] = None) -> List[Any]:
            """Fetch all urls, then return soups for all results."""
            from bs4 import BeautifulSoup

            results = await loader.fetch_all(urls)
            final_results = []
            for i, result in enumerate(results):
                url = urls[i]
                if parser is None:
                    if url.endswith(".xml"):
                        parser = "xml"
                    else:
                        parser = loader.default_parser
                    loader._check_parser(parser)
                final_results.append(BeautifulSoup(result, parser, **loader.bs_kwargs))

            docs = []
            for path, soup in zip(loader.web_paths, final_results):
                text = soup.get_text(**loader.bs_get_text_kwargs)
                metadata = _build_metadata(soup, path)
                docs.append(Document(page_content=text, metadata=metadata))

            return docs

        docs = await scrape_all(loader, urls)  # Load all URLs concurrently
        end_time = time()
        logger.info(f"load {len(urls)} urls and time spent loading {(end_time - start_time)} seconds")
        contents = {}

        for doc in docs:
            if doc.page_content:
                # Clean and extract up to 4000 characters
                cleaned_text = doc.page_content.replace("\n", " ")
                contents[doc.metadata['source']] = cleaned_text[:4000] if len(cleaned_text) > 200 else ""
            else:
                contents[doc.metadata['source']] = ""

        return contents

    except Exception as error:
        print('Error extracting content:', error)
        return {}


async def populate_sources(sources, num_elements):
    """
    Populate the 'html' content for each source from their URLs.

    Args:
    sources (list): A list of sources, each containing a 'link' key.
    num_elements (int): The number of elements to process.

    Returns:
    list: The updated sources with 'html' content populated.
    """
    try:
        # Extract the URLs from the sources list
        urls = [source['link'] for source in sources[:num_elements] if source]

        # Fetch content for all URLs concurrently
        contents = await extract_multiple_website_contents(urls)

        # Update each source with its corresponding extracted HTML
        for i, source in enumerate(sources[:num_elements]):
            if source and source['link'] in contents:
                source['html'] = contents[source['link']]

    except Exception as e:
        print(f"Error in populate_sources: {e}")

    return sources
