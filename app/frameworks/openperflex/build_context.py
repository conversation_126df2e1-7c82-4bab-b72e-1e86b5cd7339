import time

from app.core.tracing_setup import logger
from app.frameworks.openperflex.cohere_reranking import get_reranking_cohere
from app.frameworks.openperflex.semantic_chunking import get_chunking


async def build_context(sources_result, query, pro_mode, date_context):
    """
    Build context from search results.

    :param sources_result: Dictionary containing search results
    :param query: Search query string
    :param pro_mode: <PERSON><PERSON><PERSON> indicating whether to use pro mode (reranking)
    :param date_context: Date context string
    :return: Built context as a string
    """
    try:
        start_time = time.time()
        combined_list = []

        # Step 1: Extract organic results
        organic_results = sources_result.get('organic', [])
        logger.info(f"Step 1: Extracted organic results in {time.time() - start_time:.2f} seconds")
        step_time = time.time()

        # Step 2: Extract snippets
        snippets = [
            f"{item['link']} {item['snippet']} {item.get('date', '')}"
            for item in organic_results if 'snippet' in item  # Ensure there's always a snippet
        ]
        combined_list.extend(snippets)
        logger.info(f"Step 2: Extracted snippets in {time.time() - step_time:.2f} seconds")
        step_time = time.time()

        # Step 3: Process HTML text
        html_text = " ".join(item['html'] for item in organic_results if 'html' in item)
        if html_text is not None and len(html_text) > 200:
            combined_list.extend(await get_chunking(html_text))
        logger.info(f"Step 3: Processed HTML text in {time.time() - step_time:.2f} seconds")
        step_time = time.time()

        # Step 4: Extract top stories titles
        if sources_result.get('topStories') is not None:
            top_stories_titles = [item['title'] for item in sources_result.get('topStories') if 'title' in item]
            combined_list.extend(top_stories_titles)
        logger.info(f"Step 4: Extracted top stories in {time.time() - step_time:.2f} seconds")
        step_time = time.time()

        # Step 5: Add graph descriptions
        graph = sources_result.get('graph')
        if graph is not None:
            graph_desc = graph.get('description')
            if graph_desc:
                combined_list.append(graph_desc)
        logger.info(f"Step 5: Added graph descriptions in {time.time() - step_time:.2f} seconds")
        step_time = time.time()

        # Step 6: Add answer box details
        answer_box = sources_result.get('answerBox')
        if answer_box is not None:
            for key in ['answer', 'snippet']:
                if key in answer_box:  # Use this if you want to append regardless of the value (including None)
                    combined_list.append(answer_box[key])
        logger.info(f"Step 6: Added answer box details in {time.time() - step_time:.2f} seconds")
        step_time = time.time()

        # Step 7: Perform reranking if pro_mode is enabled
        if pro_mode:
            final_list = await  get_reranking_cohere(combined_list, query + date_context, len(combined_list))
            logger.info(f"Step 7: Performed reranking in {time.time() - step_time:.2f} seconds")
        else:
            final_list = await get_reranking_cohere(combined_list, query + date_context, len(combined_list))
            logger.info(f"Step 7: Performed reranking in {time.time() - step_time:.2f} seconds")
            # final_list = combined_list
            # logger.info(f"Step 7: Skipped reranking in {time.time() - step_time:.2f} seconds")

        search_contexts = "\n\n".join(final_list)
        logger.info(f"Total time taken for build_context: {time.time() - start_time:.2f} seconds")
        return search_contexts

    except Exception as e:
        logger.exception(f"An error occurred while building context: {e}")
        return ""
