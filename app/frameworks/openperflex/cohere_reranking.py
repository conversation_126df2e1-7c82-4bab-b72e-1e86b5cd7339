import os

from cohere import AsyncClient
from langchain.retrievers.contextual_compression import ContextualCompressionRetriever
from langchain_cohere import CohereRerank
from langchain_community.embeddings import CohereEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_core.documents import Document

# Set up your API key from environment variables
COHERE_API_KEY = os.getenv("COHERE_API_KEY")
MODEL = "rerank-multilingual-v3.0"

# Initialize the Cohere AsyncClient for any other uses
co = AsyncClient(api_key=COHERE_API_KEY)

# Helper function for printing documents
def pretty_print_docs(docs):
    print(
        f"\n{'-' * 100}\n".join(
            [f"Document {i+1}:\n\n" + d.page_content for i, d in enumerate(docs)]
        )
    )

# Load and split documents (this will replace your document retrieval)
def setup_retriever(documents):

    # Initialize the retriever using FAISS and Cohere embeddings
    retriever = FAISS.from_documents(
        documents, CohereEmbeddings(model="embed-english-v3.0", user_agent="diogenesaichatbot")
    ).as_retriever(search_kwargs={"k": len(documents)})

    return retriever

# Reranking using Langchain with CohereRerank, maintaining the same interface
async def get_reranking_cohere(docs, query, top_res):
    """
    Re-ranks a list of documents based on a query using Langchain's Cohere rerank API.

    Args:
    docs (list of str): List of documents to be re-ranked.
    query (str): Query string to rank the documents against.
    top_res (int): Number of top results to return.

    Returns:
    list of str: Top re-ranked documents based on the query.
    """
    try:
        # Setup the retriever and reranker
        documents = [Document(page_content=doc) for doc in docs]
        retriever = setup_retriever(documents)
        compressor = CohereRerank(model=MODEL,top_n=top_res, user_agent="diogenesaichatbot")

        # Initialize the compression retriever with reranker
        compression_retriever = ContextualCompressionRetriever(
            base_compressor=compressor, base_retriever=retriever
        )

        # Use the compression retriever to get the reranked documents
        compressed_docs = await compression_retriever.ainvoke(query)

        # Pretty print the reranked documents
        pretty_print_docs(compressed_docs)

        # Extract and return the top results
        return [doc.page_content for doc in compressed_docs]

    except Exception as e:
        print(f"An error occurred in get_reranking_cohere : {e}")
        return []
