"""
Celery application configuration module.

This module sets up the Celery application for asynchronous task processing.
It configures the Celery instance with <PERSON><PERSON> as the backend and broker.
"""
import os

from celery import Celery

from app.config.config import setup_envs
from app.core.tracing_setup import logger

import os

# class Config:
#     # Determine the worker state db path based on environment
#     if os.getenv('DOCKER_ENV'):
#         # Path for Docker
#         CELERY_WORKER_STATE_DB = '/app/data/celery_worker_state.db'  # Inside the container
#     else:
#         # Path for local development
#         CELERY_WORKER_STATE_DB = './celery_worker_state.db'  # Relative path

# Ensure the current directory is created
current_dir = os.path.dirname(os.path.abspath(__file__))
data_dir = os.path.join(current_dir, 'data')
if not os.path.exists(data_dir):
    os.makedirs(data_dir)

# Ensure the database path is valid
db_path = os.path.join(data_dir, 'celery_worker_state.db')


# Set up environment variables
setup_envs()

def create_celery_app() -> Celery:
    """
    Create and configure the Celery application.

    Returns:
        Celery: Configured Celery application instance.
    """
    try:
        app = Celery(
            __name__,
            backend="redis://localhost:6379/0",
            broker="redis://localhost:6379/0"
        )
        # Set the worker state db path from the configuration
        # app.conf.worker_state_db = f'sqlite:///{db_path}'

        # Import the module containing the task configurations
        app.config_from_object('app.queue.celeryconfig')

        logger.info("Celery application created and configured successfully.")
        return app
    except Exception as e:
        logger.error(f"Failed to create Celery application: {str(e)}")
        raise

# Create the Celery application instance
celery = create_celery_app()

# Additional Celery configurations can be added here if needed
