import asyncio
import json
from datetime import datetime
from typing import Dict

from crewai import Agent, Task, Crew, Process
from crewai.memory import LongTermMemory, ShortTermMemory, EntityMemory
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
from crewai.memory.storage.rag_storage import RAGStorage
from crewai_tools import DOCXSearchTool
from crewai_tools import SerperDevTool
from crewai_tools.tools.csv_search_tool.csv_search_tool import CSVSearchTool
# from crewai_tools.tools.firecrawl_scrape_website_tool.firecrawl_scrape_website_tool import FirecrawlScrapeWebsiteTool
from crewai_tools.tools.json_search_tool.json_search_tool import JSONSearchTool
from crewai_tools.tools.pdf_search_tool.pdf_search_tool import PDFSearchTool
from crewai_tools.tools.scrape_website_tool.scrape_website_tool import ScrapeWebsiteTool
from crewai_tools.tools.selenium_scraping_tool.selenium_scraping_tool import SeleniumScrapingTool
from crewai_tools.tools.txt_search_tool.txt_search_tool import TXTSearchTool
from crewai_tools.tools.website_search.website_search_tool import WebsiteSearchTool
from crewai_tools.tools.xml_search_tool.xml_search_tool import XMLSearchTool
from crewai_tools.tools.youtube_video_search_tool.youtube_video_search_tool import YoutubeVideoSearchTool
# from firecrawl import FirecrawlApp
from google.cloud.firestore_v1 import AsyncClient
from google.cloud.storage import Client
from promptflow.tracing import start_trace

from app.config.config import chatgpt_basic
from app.core.db_config import get_storage, get_db
from app.core.tracing_setup import tracer, logger
from app.queue.celery_app import celery
# Import shared functions
from app.utils.firebase_utils import update_progress, upload_to_firebase
from app.utils.util_markdown import extract_outline

# from traceloop.sdk.decorators import workflow

search_tool = SerperDevTool()# code_tool = CodeInterpreterTool()
scrape_tool = ScrapeWebsiteTool()
selenium_tool = SeleniumScrapingTool()
web_tool = WebsiteSearchTool(config={"vectordb": {
    "provider": "qdrant",
    "config":
        {"collection_name": "crewai_qdrant_website_search_index"}}})

# rag
csv_tool = CSVSearchTool(config={"vectordb": {
    "provider": "qdrant",
    "config": {"collection_name": "crewai_qdrant_csv_search_index"}}})

docx_tool = DOCXSearchTool(config={"vectordb": {
    "provider": "qdrant",
    "config": {"collection_name": "crewai_qdrant_docx_search_index"}}})

json_tool = JSONSearchTool(config={"vectordb": {
    "provider": "qdrant",
    "config": {"collection_name": "crewai_qdrant_json_search_index"}}})

pdf_tool = PDFSearchTool(config={"vectordb": {
    "provider": "qdrant",
    "config": {"collection_name": "crewai_qdrant_pdf_search_index"}}})

txt_tool = TXTSearchTool(config={"vectordb": {
    "provider": "qdrant",
    "config": {"collection_name": "crewai_qdrant_txt_search_index"}}})

xml_tool = XMLSearchTool(config={"vectordb": {
    "provider": "qdrant",
    "config": {"collection_name": "crewai_qdrant_xml_search_index"}}})

youtube_tool = YoutubeVideoSearchTool(config={"vectordb": {
    "provider": "qdrant",
    "config": {"collection_name": "crewai_qdrant_youtube_search_index"}}})
# vision_tool = VisionTool()
# dalle_tool = DallETool()
# firecrawl_app = FirecrawlApp(
#     api_key=os.environ["FIRECRAWL_API_KEY"], api_url=os.environ["FIRECRAWL_API_URL"])
# crawl_tool = FirecrawlScrapeWebsiteTool(
#     firecrwal=firecrawl_app, api_key=os.environ["FIRECRAWL_API_KEY"])
# langchain tools
# wikipedia = WikipediaQueryRun(api_wrapper=WikipediaAPIWrapper())
# tools = load_tools(
#     ["arxiv"],
# )
tools = [search_tool, web_tool, scrape_tool,
         selenium_tool, csv_tool, json_tool,docx_tool,pdf_tool,txt_tool, xml_tool,youtube_tool]

# TODO: 1. use pipeline 2. use custom tools 3. add LangGraph 4. use yaml file 5.if there is no agent & task defined, generate it first 6. add RAG

llm = chatgpt_basic


# Apply timing decorator to async_kickoff_crew function
@celery.task(bind=True)
def async_kickoff_crew(self, agent_data: Dict, session_id: str, ):
    loop = asyncio.get_event_loop()
    start_trace(collection="crewai-groupchat-" +
                           agent_data["user_id"] + "-" + session_id)
    final_output = loop.run_until_complete(
        run_crew(self, agent_data, session_id))
    return serialize_crew_output(final_output)


# @workflow(name="crewai")
async def run_crew(celery_task, agent_data: Dict, session_id: str, ):
    ultimate_goal = agent_data.get('ultimate_goal')
    user_id = agent_data.get('user_id')
    final_output = None
    storage: Client = get_storage()
    db: AsyncClient = get_db()
    logger.info("Task registered successfully")
    # Extract agent data
    agents = []
    for agent_info in agent_data['agents']:
        agent = Agent(
            role=agent_info['role'],
            goal=agent_info['goal'],
            backstory=agent_info['backstory'],
            verbose=True,
            allow_delegation=agent_info.get('allow_delegation', True),
            tools=tools,
            llm=llm,
            max_iter=10,  # Limit to 10 iterations
            max_execution_time=60,  # Allow 60 seconds to complete the task
        )
        agents.append(agent)

    # Extract tasks
    tasks = []
    for task_info in agent_data['tasks']:
        agent = next((a for a in agents if a.role ==
                      task_info['agent_role']), None)
        if agent:
            task = Task(
                description=task_info['description'],
                expected_output=task_info['expected_output'],
                agent=agent
            )
            tasks.append(task)
    with tracer.start_as_current_span("crewai-" + user_id + "-" + session_id) as span:
        span.add_event(
            "promptflow.function.inputs", {
                "payload": json.dumps(dict(message=agent_data))}
        )
        try:

            # Define the manager agent
            manager = Agent(
                role="Project Manager",
                goal="Efficiently manage the crew and ensure high-quality task completion",
                backstory="You're an experienced project manager, skilled in overseeing complex projects and guiding teams to success. Your role is to coordinate the efforts of the crew members, ensuring that each task is completed on time and to the highest standard.",
                allow_delegation=True,
                llm=llm,
                max_iter=10,  # Limit to 10 iterations
                max_execution_time=60,  # Allow 60 seconds to complete the task
            )

            # Step and task callbacks for streaming output

            def step_callback(step_output):
                task_id = celery_task.request.id
                if task_id:
                    celery_task.update_state(state='PROGRESS', meta={
                        'output': step_output})
                    span.add_event(
                        "promptflow.function.output", {
                            "payload": json.dumps(dict(message=step_output))}
                    )
                else:
                    print("Task ID is not available")

            def task_callback(task_output):
                task_id = celery_task.request.id
                if task_id:
                    celery_task.update_state(state='PROGRESS', meta={
                        'output': task_output})
                    span.add_event(
                        "promptflow.function.output", {
                            "payload": json.dumps(dict(message=task_output))}
                    )
                else:
                    print("Task ID is not available")

            # Instantiate the crew
            crew = Crew(
                agents=agents,
                tasks=tasks,
                verbose=True,
                memory=True,
                cache=True,
                manager_agent=manager,
                process=Process.hierarchical,
                step_callback=step_callback,
                task_callback=task_callback,
                planning=True,
            )
            progress_ref = db.collection("progress").document(
                agent_data['user_id']).collection("sessions").document(session_id)
            progress_data = {
                "status": "started",
                "user_id": agent_data['user_id'],
                "topic": ultimate_goal,
                "session_id": session_id,
                "approach": "crewai",
                "timestamp": str(datetime.utcnow()),
            }

            await update_progress(progress_ref, progress_data)
            # Start the crew asynchronously
            final_output = await crew.kickoff_async()

            # Update progress and upload final output
            progress_data = {
                "status": "completed",
                "output": final_output if isinstance(final_output, str) else final_output["raw"] if isinstance(
                    final_output, dict) else final_output.raw,
                "user_id": agent_data['user_id'],
                "topic": ultimate_goal,
                "session_id": session_id,
                "approach": "crewai",
                "timestamp": str(datetime.utcnow()),
            }

            await update_progress(progress_ref, progress_data)
            article = final_output if isinstance(final_output, str) else final_output["raw"] if isinstance(final_output,
                                                                                                           dict) else final_output.raw
            span.add_event(
                "promptflow.function.output", {
                    "payload": json.dumps(dict(message=article))}
            )

            output_file_path = f"./results/crewai_output/{agent_data['user_id']}/{session_id}/storm_gen_article.txt"
            await upload_to_firebase(output_file_path, str(article),
                                     f"{agent_data['user_id']}/writing/{session_id}/storm_gen_article.txt", storage)

            outline = extract_outline(article)
            output_file_path = f"./results/crewai_output/{agent_data['user_id']}/{session_id}/storm_gen_outline.txt"
            await upload_to_firebase(output_file_path, str(outline),
                                     f"{agent_data['user_id']}/writing/{session_id}/storm_gen_outline.txt", storage)

            logger.info("user_id: " + agent_data['user_id'] + " session_id: " + session_id + "task  " + tasks[
                0].expected_output + " completed successfully")
        except Exception as e:
            logger.error(
                "An error occurred during the writing process: %s", str(e))
            progress_data = {
                "status": "failed",
                "user_id": agent_data['user_id'],
                "topic": ultimate_goal,
                "session_id": session_id,
                "approach": "crewai",
                "timestamp": str(datetime.utcnow()),
                "error": str(e),
            }

        return final_output


def serialize_crew_output(crew_output):
    """
    Convert CrewOutput to a JSON-serializable format, like a dictionary.
    """
    if isinstance(crew_output, dict):
        return crew_output
    elif hasattr(crew_output, 'dict'):
        return crew_output.dict()
    elif hasattr(crew_output, 'json'):
        return crew_output.json()
    else:
        # Handle cases where crew_output might be a coroutine
        try:
            # Await coroutine if it's a coroutine object
            if asyncio.iscoroutine(crew_output):
                return asyncio.run(crew_output)
            return str(crew_output)
        except Exception as e:
            return str(e)
