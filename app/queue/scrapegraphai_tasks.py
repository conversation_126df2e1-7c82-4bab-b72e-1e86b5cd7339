# TODO: save scrapegraphai celery tasks data
from promptflow.tracing import start_trace
from scrapegraphai.graphs import SearchGraph, ScriptCreatorGraph, SpeechGraph, SmartScraperGraph

from app.queue.celery_app import celery
from app.utils.firebase_utils import update_session_progress


# Initialize Firestore

@celery.task(bind=True)
async def run_smart_scraper_graph(self, user_id, session_id, prompt, source, config, schema=None):
    # Start tracing the task
    start_trace(collection="scrapergraph-" + user_id + "-" + session_id)

    # Update session to started
    await update_session_progress(user_id, session_id, prompt, "started")

    try:
        graph = SmartScraperGraph(prompt=prompt, source=source, config=config, schema=schema)
        result = graph.run()

        await update_session_progress(user_id, session_id, prompt, "completed")
        return result
    except Exception as e:
        await update_session_progress(user_id, session_id, prompt, "failed")
        self.retry(exc=e)
        raise e

@celery.task(bind=True)
async def run_speech_graph(self, user_id, session_id, prompt, source, config, schema=None):
    start_trace(collection="speechgraph-" + user_id + "-" + session_id)
    await update_session_progress(user_id, session_id, prompt, "started")

    try:
        graph = SpeechGraph(prompt=prompt, source=source, config=config, schema=schema)
        result = graph.run()

        await update_session_progress(user_id, session_id, prompt, "completed")
        return result
    except Exception as e:
        await update_session_progress(user_id, session_id, prompt, "failed")
        self.retry(exc=e)
        raise e


@celery.task(bind=True)
async def run_script_creator_graph(self, user_id, session_id, prompt, source, config, schema=None):
    # Start tracing the task
    start_trace(collection="scriptcreatorgraph-" + user_id + "-" + session_id)

    # Update session to started
    await update_session_progress(user_id, session_id, prompt, "started")

    try:
        graph = ScriptCreatorGraph(prompt=prompt, source=source, config=config, schema=schema)
        result = graph.run()

        # Update session to completed
        await update_session_progress(user_id, session_id, prompt, "completed")
        return result
    except Exception as e:
        # Update session to failed and retry
        await update_session_progress(user_id, session_id, prompt, "failed")
        self.retry(exc=e)
        raise e


@celery.task(bind=True)
async def run_search_graph(self, user_id, session_id, prompt, config, schema=None):
    # Start tracing the task
    start_trace(collection="searchgraph-" + user_id + "-" + session_id)

    # Update session to started
    await update_session_progress(user_id, session_id, prompt, "started")

    try:
        graph = SearchGraph(prompt=prompt, config=config, schema=schema)
        result = graph.run()

        # Update session to completed
        await update_session_progress(user_id, session_id, prompt, "completed")
        return result
    except Exception as e:
        # Update session to failed and retry
        await update_session_progress(user_id, session_id, prompt, "failed")
        self.retry(exc=e)
        raise e


from scrapegraphai.graphs import OmniScraperGraph

@celery.task(bind=True)
async def run_omni_scraper_graph(self, user_id, session_id, prompt, source, config, schema=None):
    # Start tracing the task
    start_trace(collection="omniscrapergraph-" + user_id + "-" + session_id)

    # Update session to started
    await update_session_progress(user_id, session_id, prompt, "started")

    try:
        omni_scraper_graph = OmniScraperGraph(
            prompt=prompt,
            source=source,
            config=config,
            schema=schema
        )
        result = omni_scraper_graph.run()

        # Update session to completed
        await update_session_progress(user_id, session_id, prompt, "completed")
        return result
    except Exception as e:
        # Update session to failed and retry
        await update_session_progress(user_id, session_id, prompt, "failed")
        self.retry(exc=e)
        raise e


from scrapegraphai.graphs import OmniSearchGraph

@celery.task(bind=True)
async def run_omni_search_graph(self, user_id, session_id, prompt, config, schema=None):
    # Start tracing the task
    start_trace(collection="omnisearchgraph-" + user_id + "-" + session_id)

    # Update session to started
    await update_session_progress(user_id, session_id, prompt, "started")

    try:
        omni_search_graph = OmniSearchGraph(
            prompt=prompt,
            config=config,
            schema=schema
        )
        result = omni_search_graph.run()

        # Update session to completed
        await update_session_progress(user_id, session_id, prompt, "completed")
        return result
    except Exception as e:
        # Update session to failed and retry
        await update_session_progress(user_id, session_id, prompt, "failed")
        self.retry(exc=e)
        raise e
