import os
import base64
import os
import re
import time
import zipfile
from datetime import datetime
from pathlib import Path

from fastapi import APIRouter
from google.cloud.firestore_v1 import AsyncClient
from google.cloud.storage import Blob
from loguru import logger
from magic_pdf.libs.hash_utils import compute_sha256
from magic_pdf.rw.AbsReaderWriter import AbsReaderWriter
from magic_pdf.rw.DiskReaderWriter import DiskReaderWriter
from magic_pdf.tools.common import do_parse, prepare_env

from app.config.basic_config import BUCKET_NAME
from app.core.db_config import get_storage, get_db
from app.queue.celery_app import celery


# Add a progress reference at the beginning of your function
async def update_progress(progress_ref, progress_data):
    await progress_ref.set(progress_data)


router = APIRouter()
storage = get_storage()


def read_fn(path):
    disk_rw = DiskReaderWriter(os.path.dirname(path))
    return disk_rw.read(os.path.basename(path), AbsReaderWriter.MODE_BIN)


def parse_pdf(doc_path, output_dir, end_page_id):
    os.makedirs(output_dir, exist_ok=True)
    try:
        file_name = f"{str(Path(doc_path).stem)}_{time.time()}"
        pdf_data = read_fn(doc_path)
        parse_method = "auto"
        local_image_dir, local_md_dir = prepare_env(output_dir, file_name, parse_method)
        do_parse(
            output_dir,
            file_name,
            pdf_data,
            [],
            parse_method,
            False,
            end_page_id=end_page_id,
        )
        return local_md_dir, file_name
    except Exception as e:
        logger.exception("Error parsing PDF: {}", e)
        return None, None


def compress_directory_to_zip(directory_path, output_zip_path):
    try:
        with zipfile.ZipFile(output_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, directory_path)
                    zipf.write(file_path, arcname)
        return 0
    except Exception as e:
        logger.exception("Error compressing directory to zip: {}", e)
        return -1


def image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def replace_image_with_base64(markdown_text, image_dir_path):
    pattern = r'\!\[(?:[^\]]*)\]\(([^)]+)\)'

    def replace(match):
        relative_path = match.group(1)
        full_path = os.path.join(image_dir_path, relative_path)
        base64_image = image_to_base64(full_path)
        return f"![{relative_path}](data:image/jpeg;base64,{base64_image})"

    return re.sub(pattern, replace, markdown_text)


def to_markdown(file_path, end_pages, output_dir):
    local_md_dir, file_name = parse_pdf(file_path, output_dir, end_pages - 1)
    if local_md_dir is None:
        return None, "Error parsing PDF"

    archive_zip_path = os.path.join(output_dir, compute_sha256(local_md_dir) + ".zip")
    zip_archive_success = compress_directory_to_zip(local_md_dir, archive_zip_path)

    if zip_archive_success != 0:
        logger.error("Compression failed")
        return None, "Error compressing output"

    md_path = os.path.join(local_md_dir, file_name + ".md")
    with open(md_path, 'r', encoding='utf-8') as f:
        txt_content = f.read()
    md_content_replaced = replace_image_with_base64(txt_content, local_md_dir)

    return md_content_replaced, txt_content, archive_zip_path


async def upload_to_firebase_storage(file_path, storage_path):
    bucket = storage.bucket(BUCKET_NAME)
    blob:Blob = bucket.blob(storage_path)
    blob.upload_from_filename(file_path)

async def upload_to_firebase_storage_from_string(string_content, storage_path):
    bucket = storage.bucket(BUCKET_NAME)
    blob:Blob = bucket.blob(storage_path)
    blob.upload_from_string(string_content)


def generate_firebase_link(storage_path):
    bucket = storage.bucket(BUCKET_NAME)
    blob = bucket.blob(storage_path)
    return blob.public_url  # Ensure your bucket allows public access to the generated files


import asyncio


@celery.task(bind=True)
def async_kickoff_process_pdf_task(self, file_path, end_pages, user_id, session_id):
    # Create a new event loop for this task
    loop = asyncio.get_event_loop()
    # asyncio.set_event_loop(loop)

    final_output = None  # Initialize final_output to ensure it's defined

    try:
        # Run the async function in the newly created event loop
        # final_output = asyncio.run(process_pdf_task(file_path, end_pages, user_id, session_id))
        final_output = loop.run_until_complete(process_pdf_task(self, file_path, end_pages, user_id, session_id))
    except Exception as e:
        logger.exception("Error in async task: %s", e)
        self.retry(exc=e)  # Optionally retry the task on error
    finally:
        # Close the event loop
        loop.close()

    return final_output


async def process_pdf_task(celery_task, file_path, end_pages, user_id, session_id):
    start_time = time.time()
    # Initialize Firestore client
    db: AsyncClient = get_db()
    progress_ref = db.collection("progress").document(user_id).collection("sessions").document(session_id)

    # Update progress to "started"
    progress_data = {
        "status": "started",
        "user_id": user_id,
        "session_id": session_id,
        "approach": "pdf2markdown",
        "timestamp": str(datetime.utcnow()),
    }
    await update_progress(progress_ref, progress_data)

    OUTPUT_DIR = f'./output/pdf2markdown/{user_id}/{session_id}'
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    try:
        md_content,md_content_no_base64, zip_path = to_markdown(file_path, end_pages, OUTPUT_DIR)

        if md_content is None:
            raise Exception("Error parsing PDF.")

        # Save Markdown content to a file
        md_file_path = os.path.join(OUTPUT_DIR, f"{Path(file_path).stem}.md")
        with open(md_file_path, 'w', encoding='utf-8') as md_file:
            md_file.write(md_content)
        md_file_path = os.path.join(OUTPUT_DIR, f"{Path(file_path).stem}_no_base64.md")
        with open(md_file_path, 'w', encoding='utf-8') as md_file:
            md_file.write(md_content_no_base64)

        # Upload original PDF, generated zip, and Markdown file to Firebase Storage
        original_pdf_path = file_path
        await upload_to_firebase_storage(original_pdf_path,
                                         f"documents/{user_id}/pdf2markdown/{session_id}/original.pdf")
        await upload_to_firebase_storage(zip_path, f"documents/{user_id}/pdf2markdown/{session_id}/output.zip")
        await upload_to_firebase_storage_from_string(md_content, f"documents/{user_id}/pdf2markdown/{session_id}/output.md")
        await upload_to_firebase_storage_from_string(md_content_no_base64, f"documents/{user_id}/pdf2markdown/"
                                                                           f"{session_id}/output_no_base64.md")

        # Generate download links
        original_pdf_link = generate_firebase_link(f"documents/{user_id}/pdf2markdown/{session_id}/original.pdf")
        output_zip_link = generate_firebase_link(f"documents/{user_id}/pdf2markdown/{session_id}/output.zip")
        md_link = generate_firebase_link(f"documents/{user_id}/pdf2markdown/{session_id}/output.md")
        md_no_base64_link = generate_firebase_link(f"documents/{user_id}/pdf2markdown/{session_id}/output_no_base64.md")
        output = {
            "markdown": md_content,
            "original_pdf_link": original_pdf_link,
            "output_zip_link": output_zip_link,
            "md_link": md_link,  # Include the link to the Markdown file
            "md_no_base64_link": md_no_base64_link  # Include the link to the Markdown file
        }
        # Update progress to "completed"
        progress_data = {
            "status": "completed",
            "user_id": user_id,
            "session_id": session_id,
            "approach": "pdf2markdown",
            "timestamp": str(datetime.now()),
            # "markdown": md_content,
            "original_pdf_link": original_pdf_link,
            "output_zip_link": output_zip_link,
            "md_link": md_link,  # Include the link to the Markdown file
            "md_no_base64_link": md_no_base64_link  # Include the link to the Markdown file
        }
        # logger.debug("Output being saved to Firestore: {}", output)
        await update_progress(progress_ref, progress_data)
        end_time = time.time()
        elapsed_time = end_time - start_time
        logger.info(f"Successfully finished conversion for {file_path} in {elapsed_time:.2f} seconds")

        return output

    except Exception as e:
        logger.error("An error occurred during PDF processing: {}", str(e))

        # Update progress to "failed"
        progress_data = {
            "status": "failed",
            "user_id": user_id,
            "session_id": session_id,
            "approach": "pdf2markdown",
            "timestamp": str(datetime.now()),
            "error": str(e),
        }
        await update_progress(progress_ref, progress_data)

        # Raise the exception to be handled by the Celery worker
        raise e
