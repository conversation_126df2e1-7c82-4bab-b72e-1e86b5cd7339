"""
Autogen tasks module for handling asynchronous autogen-related tasks.

This module provides Celery tasks for running autogen workflows and managing agent interactions.
"""

import asyncio
import os
from typing import Dict, List, Any

import autogen
from promptflow.tracing import start_trace

from app.config.basic_config import default_model
from app.core.tracing_setup import tracer, logger
from app.queue.celery_app import celery

default_llm_config = {
    'temperature': 0,
    "max_tokens": 16000,
    "config_list": [{
        'model': default_model,
        'api_key': os.environ.get('OPENAI_API_KEY'),
    }]
}

builder = AgentBuilder(builder_model=default_model, agent_model=default_model)

# TODO: 1. register tool 2. auto generate tools 3. add RAG 4. use Teachability to add long term memory
search_tool = "Your search tool configuration or setup"

@celery.task(bind=True)
def async_kickoff_autogen(self, agent_data: Dict[str, Any], session_id: str, use_autobuild: bool = True) -> Dict[str, str]:
    """
    Asynchronously start an autogen task.

    Args:
        self: The Celery task instance.
        agent_data (Dict[str, Any]): Data containing agent and task information.
        session_id (str): Unique identifier for the session.
        use_autobuild (bool): Whether to use autobuild or not. Defaults to True.

    Returns:
        Dict[str, str]: Serialized output of the autogen task.
    """
    try:
        start_trace(collection=f"autogen-groupchat-{agent_data['user_id']}-{session_id}")
        
        if 'agents' in agent_data and 'tasks' in agent_data:
            final_output = asyncio.run(run_autogen(self, agent_data, session_id))
        else:
            final_output = asyncio.run(run_autogen_autobuild(self, agent_data, session_id))
        
        return serialize_autogen_output(final_output)
    except Exception as e:
        logger.error(f"Error in async_kickoff_autogen: {str(e)}")
        raise

async def run_autogen_autobuild(celery_task: Any, agent_data: Dict[str, Any], session_id: str) -> str:
    """
    Run autogen task using autobuild.

    Args:
        celery_task (Any): The Celery task instance.
        agent_data (Dict[str, Any]): Data containing agent and task information.
        session_id (str): Unique identifier for the session.

    Returns:
        str: The response from the autogen task.

    Raises:
        ValueError: If required data is missing.
    """
    user_id = agent_data.get('user_id')
    building_task = agent_data.get("building_task")

    if not building_task:
        raise ValueError("Missing required data: 'building_task'")

    try:
        agent_list, agent_configs = builder.build(building_task, default_llm_config, coding=False)
        coding = agent_configs["coding"]

        initial_message = [{
            "content": f"You're an experienced project manager, skilled in overseeing complex projects and guiding teams to success. Your role is to coordinate the efforts of the crew members, ensuring that each task is completed on time and to the highest standard. The goal is {building_task}",
            "role": "system"
        }]

        group_chat = GroupChat(
            agents=agent_list,
            messages=[initial_message],
            max_round=12,
            allow_repeat_speaker=agent_list[:-1] if coding else agent_list
        )
        manager = GroupChatManager(groupchat=group_chat, llm_config=default_llm_config)

        with tracer.start_as_current_span(f"autogen-{user_id}-{session_id}") as span:
            chat_results = await agent_list[0].a_initiate_chat(manager, message=building_task)
            span.add_event("promptflow.function.inputs", {"payload": str(chat_results)})

            response = await manager.a_run_chat()
            span.add_event("promptflow.function.output", {"payload": str(response)})

        return response
    except Exception as e:
        logger.error(f"Error in run_autogen_autobuild: {str(e)}")
        raise

async def run_autogen(celery_task: Any, agent_data: Dict[str, Any], session_id: str) -> str:
    """
    Run autogen task with predefined agents and tasks.

    Args:
        celery_task (Any): The Celery task instance.
        agent_data (Dict[str, Any]): Data containing agent and task information.
        session_id (str): Unique identifier for the session.

    Returns:
        str: The final output from the autogen task.

    Raises:
        ValueError: If required data is missing.
    """
    user_id = agent_data.get('user_id')
    agents = create_agents(agent_data.get('agents', []))
    tasks = extract_tasks(agent_data.get('tasks', []))
    building_task = agent_data.get("building_task")

    if not building_task:
        raise ValueError("Missing required data: 'building_task'")

    try:
        user_proxy = create_user_proxy()
        initial_chats = setup_initial_chats(tasks, agents)

        with tracer.start_as_current_span(f"autogen-{user_id}-{session_id}") as span:
            group_chat = autogen.GroupChat(
                agents=list(agents.values()),
                messages=[],
                max_round=12,
                allow_repeat_speaker=list(agents.values())
            )
            manager = autogen.GroupChatManager(groupchat=group_chat, llm_config=default_llm_config)

            chat_results = await user_proxy.a_initiate_chats(initial_chats)
            celery_task.update_state(state='PROGRESS', meta={'output': str(chat_results)})
            span.add_event("promptflow.function.inputs", {"payload": str(chat_results)})

            final_output = await user_proxy.a_initiate_chat(manager, message=building_task)
            span.add_event("promptflow.function.output", {"payload": str(final_output)})

        return final_output
    except Exception as e:
        logger.error(f"Error in run_autogen: {str(e)}")
        raise

def create_agents(agent_info_list: List[Dict[str, str]]) -> Dict[str, autogen.AssistantAgent]:
    """Create AssistantAgent instances from agent information."""
    return {
        agent_info['role']: autogen.AssistantAgent(
            name=agent_info['role'],
            llm_config=default_llm_config,
            system_message=f"Your role is : {agent_info.get('role', '')}; your goal: {agent_info.get('goal', '')}; your backstory: {agent_info.get('backstory', '')}",
        ) for agent_info in agent_info_list
    }

def extract_tasks(task_info_list: List[Dict[str, str]]) -> List[Dict[str, str]]:
    """Extract tasks from task information."""
    return [
        {
            "description": task_info.get('description'),
            "expected_output": task_info.get('expected_output'),
            "agent_role": task_info.get('agent_role')
        } for task_info in task_info_list
    ]

def create_user_proxy() -> autogen.UserProxyAgent:
    """Create a UserProxyAgent instance."""
    return autogen.UserProxyAgent(
        name="User_proxy",
        system_message="A human admin.",
        code_execution_config=False,
        max_consecutive_auto_reply=3,
        human_input_mode="NEVER",
        default_auto_reply=None,
    )

def setup_initial_chats(tasks: List[Dict[str, str]], agents: Dict[str, autogen.AssistantAgent]) -> List[Dict[str, Any]]:
    """Set up initial chats for each task."""
    return [
        {
            "chat_id": i + 1,
            "recipient": agents.get(task['agent_role']),
            "message": task['description'],
            "silent": False,
            "summary_method": "reflection_with_llm",
        } for i, task in enumerate(tasks)
    ]

def serialize_autogen_output(autogen_output: Any) -> Dict[str, str]:
    """
    Convert Autogen output to a JSON-serializable format.

    Args:
        autogen_output (Any): The output from the autogen task.

    Returns:
        Dict[str, str]: A dictionary with the serialized output.
    """
    return {"output": str(autogen_output)}
