# app/queue/writing_tasks.py
import asyncio
import os
from datetime import datetime

from google.cloud.firestore_v1 import Async<PERSON>lient
from google.cloud.storage import Client

from app.core.db_config import get_storage, get_db
from app.core.tracing_setup import logger
from app.models.model import WritingRequestData
from app.models.writing import ResearchState
from app.queue.celery_app import celery
from app.services.write_service import awrite
from app.utils.firebase_utils import update_progress, upload_to_firebase  # Import shared functions

# from knowledge_storm import STORMWikiLMConfigs, STORMWikiRunnerArguments, STORMWikiRunner
# from knowledge_storm.lm import OpenAIModel
# from knowledge_storm.rm import VectorRM
# from knowledge_storm.utils import QdrantVectorStoreManager

USE_LangGraph = True
# lm_configs = STORMWikiLMConfigs()
# openai_kwargs = {
#     'api_key': os.getenv("OPENAI_API_KEY"),
#     'temperature': 1.0,
#     'top_p': 0.9,
# }
# gpt = OpenAIModel(model=default_model, max_tokens=6000, **openai_kwargs)
vector_db_mode = "online"
online_vector_db_url = os.getenv("QDRANT_ENDPOINT")


# STORM is a LM system so different components can be powered by different models to reach a good balance between cost and quality.
# For a good practice, choose a cheaper/faster model for `conv_simulator_lm` which is used to split queries, synthesize answers in the conversation.
# Choose a more powerful model for `article_gen_lm` to generate verifiable text with citations.
# lm_configs.set_conv_simulator_lm(gpt)
# lm_configs.set_question_asker_lm(gpt)
# lm_configs.set_outline_gen_lm(gpt)
# lm_configs.set_article_gen_lm(gpt)
# lm_configs.set_article_polish_lm(gpt)


# Check out the STORMWikiRunnerArguments class for more configurations.

@celery.task
def process_writing_task(writing_request_data_dict: dict, session_id: str,
                         ):
    logger.info("Task started")

    # Wrap the entire async logic inside one asyncio.run() call
    asyncio.run(async_process_writing_task(writing_request_data_dict, session_id))


async def async_process_writing_task(writing_request_data_dict: dict, session_id: str,
                                     ):
    logger.info("Task registered successfully")
    storage: Client = get_storage()
    db: AsyncClient = get_db()
    writing_request_data: WritingRequestData = WritingRequestData.model_validate(writing_request_data_dict)
    # Update progress status in Firestore
    logger.info("Writing process started successfully.")
    progress_data = {
        "user_id": writing_request_data.user_id,
        "topic": writing_request_data.topic,
        "session_id": session_id,
        "status": "started",
        "approach":"langgraph",
        "timestamp": str(datetime.utcnow()),
    }
    progress_ref = db.collection("progress").document(writing_request_data.user_id).collection("sessions").document(
        session_id)

    await update_progress(progress_ref, progress_data)  # Create or update the document
    try:

        if USE_LangGraph:
            await write_with_langgraph(session_id, storage, writing_request_data)

        # else:
        #     await write_without_langgraph(session_id, storage, writing_request_data)

    except Exception as e:
        logger.error("An error occurred during the writing process: %s", str(e))
        # Update progress status in Firestore
        logger.info("Updating progress status in Firestore.")
        progress_data = {
            "user_id": writing_request_data.user_id,
            "topic": writing_request_data.topic,
            "session_id": session_id,
            "status": "failed",
            "timestamp": str(datetime.utcnow()),
            "approach":"langgraph",
            "error": str(e),
        }
        await update_progress(progress_ref, progress_data)  # Update the document with the completed status
        return {"session_id": session_id, "error": str(e)}
    progress_data = {
        "user_id": writing_request_data.user_id,
        "topic": writing_request_data.topic,
        "session_id": session_id,
        "status": "completed",
        "approach":"langgraph",
        "timestamp": str(datetime.utcnow()),
    }
    await progress_ref.set(progress_data)  # Update the document with the new status


# async def write_without_langgraph(session_id, storage: Client, writing_request_data):
#     # Use STORMWikiRunner directly without LangChain
#     embedding_model = "BAAI/bge-m3"
#     device = "cpu"
#     csv_file_path = "./app/assets/topic.csv"
#     embed_batch_size = 64
#     collection_name = "diogenes_writing_" + writing_request_data.user_id
#     offline_vector_db_dir = "qdrant_diogenes_writing_" + writing_request_data.user_id
#     output_dir = f"./results/gpt_retrival/{writing_request_data.user_id}/{session_id}/"
#     engine_args = STORMWikiRunnerArguments(
#         output_dir=output_dir,
#     )
#     kwargs = {
#         'file_path': csv_file_path,
#         'content_column': 'content',
#         'title_column': 'title',
#         'url_column': 'url',
#         'desc_column': 'description',
#         'batch_size': embed_batch_size,
#         'vector_db_mode': vector_db_mode,
#         'collection_name': collection_name,
#         'embedding_model': embedding_model,
#         'device': device,
#     }
#     if vector_db_mode == 'offline':
#         QdrantVectorStoreManager.create_or_update_vector_store(
#             vector_store_path=offline_vector_db_dir,
#             **kwargs
#         )
#     elif vector_db_mode == 'online':
#         QdrantVectorStoreManager.create_or_update_vector_store(
#             url=online_vector_db_url,
#             qdrant_api_key=os.getenv('QDRANT_API_KEY'),
#             **kwargs
#         )
#     # Setup VectorRM to retrieve information from your own data
#     rm = VectorRM(collection_name=collection_name, embedding_model=embedding_model, device=device,
#                   k=engine_args.search_top_k)
#     # initialize the vector store, either online (store the db on Qdrant server) or offline (store the db locally):
#     if vector_db_mode == 'offline':
#         rm.init_offline_vector_db(vector_store_path=offline_vector_db_dir)
#     elif vector_db_mode == 'online':
#         rm.init_online_vector_db(url=online_vector_db_url, api_key=os.getenv('QDRANT_API_KEY'))
#     runner = STORMWikiRunner(engine_args, lm_configs, rm)
#     topic = writing_request_data.topic
#     logger.info("Starting the runner process.")
#     runner.run(
#         topic=topic,
#         do_research=True,
#         do_generate_outline=True,
#         do_generate_article=True,
#         do_polish_article=True,
#     )
#     runner.post_run()
#     # Upload final results to Firebase Storage
#     # Get the first folder under output_dir
#     first_folder = next(os.walk(output_dir))[1][0]
#     file_dir = os.path.join(output_dir, first_folder)
#     # Upload article, outline, and polished article
#     for file_name in ["storm_gen_article.txt", "storm_gen_outline.txt", "storm_gen_article_polished.txt"]:
#         file_path = os.path.join(file_dir, file_name)
#         blob_name = f"{writing_request_data.user_id}/writing/{session_id}/{file_name}"
#         await upload_to_firebase(file_path, None, blob_name, storage)
#     logger.info("Runner process completed successfully.")
#     runner.summary()


async def write_with_langgraph(session_id, storage: Client, writing_request_data):
    logger.info("Starting awrite function.")
    research_state: ResearchState = await awrite(user_topic=writing_request_data.topic,
                                                 user_id=writing_request_data.user_id,
                                                 session_id=session_id, )

    logger.info("Awrite function completed successfully.")
    # Upload files to Firebase Storage
    outline_file_path = f"{writing_request_data.user_id}/writing/{session_id}/storm_gen_outline.txt"
    logger.info("Uploading outline file to Firebase Storage.")
    await upload_to_firebase(outline_file_path, research_state["outline"].as_str, outline_file_path, storage)
    logger.info("Outline file uploaded successfully.")
    article: str = research_state['article']
    article_file_path = f"{writing_request_data.user_id}/writing/{session_id}/storm_gen_article.txt"
    logger.info("Uploading article file to Firebase Storage.")
    await upload_to_firebase(article_file_path, article, article_file_path, storage)
    logger.info("Article file uploaded successfully.")
