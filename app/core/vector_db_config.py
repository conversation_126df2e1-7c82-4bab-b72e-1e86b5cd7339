import os
import time

import weaviate
from haystack import Pipeline
from haystack.components.embedders import OpenAIDocumentEmbedder
from haystack.utils import Secret
from haystack_integrations.components.retrievers.qdrant import QdrantEmbeddingRetriever
from haystack_integrations.document_stores.qdrant import QdrantDocumentStore
from langchain_community.vectorstores import OpenSearchVectorSearch, Weaviate
from langchain_core.vectorstores import VectorStoreRetriever, VectorStore
from langchain_qdrant import QdrantVectorStore
from opensearchpy import OpenSearch
from qdrant_client import QdrantClient, AsyncQdrantClient
from qdrant_client.http import models
from weaviate.auth import Auth
from weaviate.client import WeaviateClient, Client
from weaviate.config import Timeout, Config, AdditionalConfig

from app.config.basic_config import embed_func
from app.core.tracing_setup import logger
from app.db.haystack_qdrant_crud import HaystackRetriever
from app.db.langchain_vectorstore_qdrant_crud import LangchainQdrantVectorDatabaseCRUD
from app.db.opensearch_crud import OpenSearchVectorDatabaseCRUD
from app.db.qdrant_crud import QdrantVectorDatabaseCRUD
from app.db.vector_database_crud import VectorDatabaseCRUD
from app.db.weaviate_crud import WeaviateVectorDatabaseCRUD
from app.utils.util import get_encoded_id

# Note: for Haystack 1.x use the following import instead:
# from qdrant_haystack import QdrantDocumentStore


# could be weaviate, milvus, or qdrant, opensearch, langchain_qdrant
vector_store_name = "weaviate"  # "langchain_qdrant"

opensearch_host = os.environ.get("OPENSEARCH_HOST", "localhost")
opensearch_port = os.environ.get("OPENSEARCH_PORT", "9200")
opensearchUrl = "http://" + opensearch_host + ":" + opensearch_port
use_ssl = False
verify_certs = False
opensearch_auth = ("admin", os.environ.get("AWS_OPENSEARCH_ADMIN_PASSWORD"))
os_client = OpenSearch(
    hosts=[{"host": opensearch_host, "port": str(opensearch_port)}],
    http_compress=True,  # enables gzip compression for request bodies
    http_auth=opensearch_auth,
    use_ssl=use_ssl,
    verify_certs=verify_certs,
)

# TODO: use async client for Weaviate(https://weaviate.io/developers/weaviate/client-libraries/python/async) and Qdrant

# Create the client with SSL/TLS enabled, but hostname verification disabled.
url = os.environ.get("WEAVIATE_URL")
api_key = os.environ.get("WEAVIATE_API_KEY")
we_client: WeaviateClient = weaviate.connect_to_weaviate_cloud(
    cluster_url=url,
    auth_credentials=Auth.api_key(api_key),
    headers={
        "X-OpenAI-Api-Key": os.getenv("OPENAI_API_KEY")
    },
    additional_config=AdditionalConfig(timeout=Timeout(init=30))
)

auth = weaviate.auth.AuthApiKey(api_key=api_key) if api_key else None
we_client_v3: Client = weaviate.Client(url=url, auth_client_secret=auth, additional_headers={
    "X-OpenAI-Api-Key": os.getenv("OPENAI_API_KEY")
})

# we_client: WeaviateClient = weaviate.connect_to_local(
#     # version="1.24.22",  # e.g. version="1.23.10"
#     port=6666,
#     headers={
#         "X-OpenAI-Api-Key": os.getenv("OPENAI_API_KEY")  # Replace with your API key
#     },
# )


# we_client: WeaviateClient = weaviate.connect_to_embedded(
#     version="1.25.10",  # e.g. version="1.23.10"
#     port=8079,
#     headers={
#         # Replace with your API key
#         "X-OpenAI-Api-Key": os.getenv("OPENAI_API_KEY")
#     },
#     environment_variables={
#         "ENABLE_MODULES": "text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai",
#         "X-OpenAI-Api-Key": os.getenv("OPENAI_API_KEY"),
#     }
# )
#
#
# we_client_v3 = weaviate.Client(
#     timeout_config=(20,60),
#     embedded_options=EmbeddedOptions(
#         additional_env_vars={
#             "ENABLE_MODULES": "text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai",
#             "X-OpenAI-Api-Key": os.getenv("OPENAI_API_KEY"),
#         }
#     )
# )

# TODO: fix this. disable for now since it is not able to open due to conflict
# milvus_client = MilvusClient("milvus.db")

qdrant_client = QdrantClient(
    os.getenv("QDRANT_ENDPOINT"),
    api_key=os.getenv("QDRANT_API_KEY"),
)
async_qdrant_client = AsyncQdrantClient(os.getenv("QDRANT_ENDPOINT"),
                                        api_key=os.getenv("QDRANT_API_KEY"), )

openai_embedder = OpenAIDocumentEmbedder(
    api_key=Secret.from_token(os.getenv("OPENAI_API_KEY")))


def get_vector_db_crud():
    if vector_store_name == "weaviate":
        return WeaviateVectorDatabaseCRUD(we_client)
    # elif vector_store_name == "milvus":
    #     return MilvusVectorDatabaseCRUD(milvus_client)
    elif vector_store_name == "opensearch":
        return OpenSearchVectorDatabaseCRUD(os_client, embed_func)
    elif vector_store_name == "qdrant":
        # return QdrantVectorDatabaseCRUD(os_client, embed_func)
        return QdrantVectorDatabaseCRUD(qdrant_client, embed_func)
    elif vector_store_name == "langchain_qdrant":
        return LangchainQdrantVectorDatabaseCRUD(qdrant_client, embed_func)
    else:
        raise ValueError("Unsupported vector store name")


async def prepare_retriever(
        query: str, bot_id: str, user_id: str,
) -> VectorStoreRetriever:
    """
    The prepare_retriever function takes in a query, bot_id and user_id.
    It then creates an OpenSearchVectorSearch object with the index name of users_{user_id}
    and embedding function as OpenAIEmbeddings(). It also sets the opensearch url, http compress to True,
    http auth to opensearch auth and use ssl and verify certs both to False.
    The filter is created using create filter function which takes in bot id, user id and query. The docs are retrieved by calling similarity search on docsearch object with parameters: query (query), search type as script scoring (

    :param query: Search for documents in the database
    :param bot_id: Filter the documents that are returned
    :param user_id: Create a unique index for each user
    :return: A list of documents

    """
    start_time = time.time()
    diogenes_search: VectorDatabaseCRUD = get_vector_db_crud_static()
    docsearch: VectorStore = None
    if vector_store_name == "weaviate":
        docsearch = Weaviate(
            client=we_client_v3,
            index_name="Userdocument",
            text_key="page_content",
            attributes=[
                "document_number",
                "page_content",
                "document_firebaseurl",
                "document_name",
            ], )

    elif vector_store_name == "qdrant" or vector_store_name == "langchain_qdrant":
        docsearch = QdrantVectorStore(
            client=qdrant_client,
            collection_name="Userdocument_users_" + user_id,
            embedding=embed_func,
        )

    else:
        docsearch = OpenSearchVectorSearch(
            index_name="users_" + get_encoded_id(user_id),
            embedding_function=embed_func,
            opensearch_url=opensearchUrl,
            http_compress=True,
            http_auth=opensearch_auth,
            use_ssl=use_ssl,
            verify_certs=verify_certs,
        )

    if vector_store_name == "weaviate":
        end_time = time.time()
        where_filter = {
            "path": ["bot_ids"],
            "operator": "ContainsAny",
            "valueText": [bot_id],
        }
        logger.info(f"process query in {end_time - start_time} seconds")
        return docsearch.as_retriever(
            search_kwargs={
                "where_filter": where_filter,
                "tenant": diogenes_search.get_index_name_by_prefix("users", user_id),
                "sort": [
                    {"path": ["document_firebaseurl"], "order": "asc"},
                    {"path": ["document_number"], "order": "asc"},
                ],
                "k": 1000,
            }
        )
    elif vector_store_name == "qdrant" or vector_store_name == "langchain_qdrant":
        retriever = docsearch.as_retriever(search_kwargs={"query_filter": {"should": {
            "key": "metadata.bot_ids",
            "match": {
                "value": bot_id}
        }}})
        end_time = time.time()
        logger.info(f"process query in {end_time - start_time} seconds")
        return retriever
    elif vector_store_name == "haystack_qdrant":
        qdrant_haystack_document_store = QdrantDocumentStore(
            url=os.getenv("QDRANT_ENDPOINT"),
            index="Userdocument_" + "users_" + user_id,
            api_key=Secret.from_env_var("QDRANT_API_KEY"),
            embedding_dim=3072,
            recreate_index=True,
        )

        query_pipeline = Pipeline()
        query_pipeline.add_component("text_embedder", openai_embedder)
        query_pipeline.add_component("retriever",
                                     QdrantEmbeddingRetriever(document_store=qdrant_haystack_document_store,
                                                              filters=models.Filter(
                                                                  must=[
                                                                      models.FieldCondition(
                                                                          key="metadata.bot_ids",
                                                                          match=models.MatchValue(
                                                                              value=bot_id)
                                                                      )]
                                                              )))
        query_pipeline.connect("text_embedder.embedding",
                               "retriever.query_embedding")

        qdrant_haystack_retriever = HaystackRetriever(query_pipeline)
        return qdrant_haystack_retriever
    else:

        vector_store_filter = diogenes_search.create_filter(
            bot_id, user_id, query)

        retriever = docsearch.as_retriever(
            search_kwargs={
                "search_type": "script_scoring",
                "space_type": "cosinesimil",
                "pre_filter": vector_store_filter,
                "vector_field": "vector_field",
                "text_field": "page_content",
            }
        )

        end_time = time.time()
        logger.info(f"process query in {end_time - start_time} seconds")

        return retriever


diogenes_search: VectorDatabaseCRUD = get_vector_db_crud()


def get_vector_db_crud_static():
    return diogenes_search
