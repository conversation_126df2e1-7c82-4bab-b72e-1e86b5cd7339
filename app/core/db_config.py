"""
Database configuration module for Firebase and Google Cloud services.

This module initializes and provides access to Firebase Admin SDK,
Google Cloud Firestore, and Google Cloud Storage clients.
"""

import json
import os
from typing import Dict, Any

import firebase_admin
from firebase_admin import credentials as firebase_admin_credentials
from google.cloud import firestore
from google.cloud import storage
from google.cloud.firestore import Async<PERSON>lient
from google.cloud.storage import Client
from google.oauth2 import service_account

firebase_service_account_key: dict | None = None


def initialize_firebase_app() -> None:
    """
    Initialize the Firebase Admin SDK if it hasn't been initialized yet.
    """
    global firebase_service_account_key
    if firebase_service_account_key is None:
        key_str = os.environ.get("FIREBASE_SERVICE_ACCOUNT_KEY", "{}")
        firebase_service_account_key = json.loads(key_str)
    cred = firebase_admin_credentials.Certificate(firebase_service_account_key)
    try:
        firebase_admin.get_app()
    except ValueError:
        firebase_admin.initialize_app(cred)


def get_firestore() -> AsyncClient:
    """
    Get an asynchronous Firestore client.

    Returns:
        AsyncClient: An asynchronous Firestore client.
    """
    global firebase_service_account_key
    if firebase_service_account_key is None:
        initialize_firebase_app()
    credentials_google_auth2 = service_account.Credentials.from_service_account_info(
        firebase_service_account_key
    )
    return firestore.AsyncClient(credentials=credentials_google_auth2)


def get_firebase_storage() -> Client:
    """
    Get a Firebase Storage client.

    Returns:
        Client: A Firebase Storage client.
    """
    global firebase_service_account_key
    if firebase_service_account_key is None:
        initialize_firebase_app()
    return storage.Client.from_service_account_info(firebase_service_account_key)


# Initialize global clients lazily
db: AsyncClient | None = None
firebase_storage: Client | None = None


def get_db() -> AsyncClient:
    """
    Get the global Firestore client.

    Returns:
        AsyncClient: The global asynchronous Firestore client.
    """
    global db
    if db is None:
        db = get_firestore()
    return db


def get_storage() -> Client:
    """
    Get the global Firebase Storage client.

    Returns:
        Client: The global Firebase Storage client.
    """
    global firebase_storage
    if firebase_storage is None:
        firebase_storage = get_firebase_storage()
    return firebase_storage
