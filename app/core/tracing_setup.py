import logging
import sys

from haystack import tracing
from opentelemetry import trace

# from opentelemetry.exporter.zipkin.json import ZipkinExporter
from opentelemetry.instrumentation.celery import CeleryInstrumentor
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
import haystack.tracing

# Explicitly tell Haystack to use your tracer
from haystack.tracing import OpenTelemetryTracer

# from opentelemetry.exporter.cloud_trace import CloudTraceSpanExporter
# enable langchain instrumentation
from opentelemetry.instrumentation.langchain import LangchainInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor

RedisInstrumentor().instrument()

resource = Resource.create({"service.name": "diogenesaichatbot"})
# Set up OpenTelemetry tracing
trace.set_tracer_provider(TracerProvider(resource=resource))
tracer_provider = trace.get_tracer_provider()

# span_processor = SimpleSpanProcessor(ConsoleSpanExporter())
# tracer_provider.add_span_processor(span_processor)

# Configure the OTLP exporter
# otlp_exporter = OTLPSpanExporter(endpoint="localhost:4317", insecure=True)
# span_processor = BatchSpanProcessor(otlp_exporter)
# tracer_provider.add_span_processor(span_processor)

# # Configure the Zipkin exporter
# zipkin_exporter = ZipkinExporter(
#     endpoint="http://localhost:9411/api/v2/spans"
# )
# span_processor = BatchSpanProcessor(zipkin_exporter)
# tracer_provider.add_span_processor(span_processor)

# cloud_trace_exporter = CloudTraceSpanExporter()
# tracer_provider.add_span_processor(
#     BatchSpanProcessor(cloud_trace_exporter)
# )

# Instrument FastAPI and Logging
# FastAPIInstrumentor().instrument()
# LoggingInstrumentor().instrument()
CeleryInstrumentor().instrument()


instrumentor = LangchainInstrumentor()
if not instrumentor.is_instrumented_by_opentelemetry:
    instrumentor.instrument()

logging.info("OpenTelemetry tracing is set up.")

tracer = tracer_provider.get_tracer(__name__)

haystack.tracing.auto_enable_tracing()


tracing.enable_tracing(OpenTelemetryTracer(tracer))


class TraceIDFilter(logging.Filter):
    def filter(self, record):
        current_span = trace.get_current_span()
        record.trace_id = (
            current_span.get_span_context().trace_id if current_span else "no-trace-id"
        )
        return True


def setup_logger():
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    logger.addFilter(TraceIDFilter())

    handler = logging.StreamHandler(sys.stderr)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(trace_id)s - %(message)s"
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)

    return logger


logger = setup_logger()
