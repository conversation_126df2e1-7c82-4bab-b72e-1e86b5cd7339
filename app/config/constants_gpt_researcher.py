import os
import random
import re

# List of real browser User-Agent strings
web_user_agents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.96 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Firefox/89.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.210 Mobile Safari/537.36",
    "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1"
]


def sanitize_filename(filename):
    return re.sub(r"[^\w\s-]", "", filename).strip()

# Define a function to retrieve environment variables with defaults
def get_env_var(var_name, default_value):
    return os.getenv(var_name, default_value)



# Function to randomly pick a User-Agent from the list
def get_random_user_agent():
    return random.choice(web_user_agents)

# Retrieving each configuration with a default fallback
gpt_researcher_config = {
    "RETRIEVER": get_env_var("RETRIEVER", "google,arxiv,duckduckgo"),
    "EMBEDDING_PROVIDER": get_env_var("EMBEDDING_PROVIDER", "openai"),
    "LLM_PROVIDER": get_env_var("LLM_PROVIDER", "openai"),
    "FAST_LLM_MODEL": get_env_var("FAST_LLM_MODEL", "gpt-4o-mini"),
    "SMART_LLM_MODEL": get_env_var("SMART_LLM_MODEL", "gpt-4o-mini"),
    "FAST_TOKEN_LIMIT": int(get_env_var("FAST_TOKEN_LIMIT", 8000)),
    "SMART_TOKEN_LIMIT": int(get_env_var("SMART_TOKEN_LIMIT", 8000)),
    "BROWSE_CHUNK_MAX_LENGTH": int(get_env_var("BROWSE_CHUNK_MAX_LENGTH", 8192)),
    "SUMMARY_TOKEN_LIMIT": int(get_env_var("SUMMARY_TOKEN_LIMIT", 2000)),
    "TEMPERATURE": float(get_env_var("TEMPERATURE", 0.55)),
    "TOTAL_WORDS": int(get_env_var("TOTAL_WORDS", 16000)),
    "MAX_ITERATIONS": int(get_env_var("MAX_ITERATIONS", 10)),
    "USER_AGENT":get_random_user_agent() if get_env_var("USER_AGENT", None) is None else get_env_var("USER_AGENT", None),  # Random User-Agent if not provided
    "MAX_SUBTOPICS": int(get_env_var("MAX_SUBTOPICS", 5)),
    "SCRAPER": get_env_var("SCRAPER", "bs"),
    "REPORT_FORMAT": get_env_var("REPORT_FORMAT", "APA"), # Consider formats like MLA, CMS, Harvard style, IEEE, Wikipedia, etc.

    "AGENT_ROLE": get_env_var("AGENT_ROLE", None),  # No default
 # Empty string as default
    "MEMORY_BACKEND": get_env_var("MEMORY_BACKEND", "local")
}

