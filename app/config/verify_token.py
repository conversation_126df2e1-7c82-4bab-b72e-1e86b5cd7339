from fastapi import Depends, HTTPException, status, Header, Query
from firebase_admin import auth

from app.core.tracing_setup import logger


def get_authorization_header(authorization: str = Head<PERSON>(None)):
    """
    Get the authorization header from the request.

    Args:
        authorization: The authorization header from the request

    Returns:
        str: The authorization header
    """
    if not authorization:
        logger.error("Authorization header missing")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header missing",
        )
    return authorization


async def verify_firebase_token(authorization: str = Depends(get_authorization_header)):
    """
    Verify a Firebase token from the authorization header.

    Args:
        authorization: The authorization header containing the token

    Returns:
        dict: The decoded token with user information
    """
    if not authorization.startswith("Bearer "):
        logger.error("Invalid or missing token")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing token",
        )
    token = authorization.split(" ")[1]
    try:
        decoded_token = auth.verify_id_token(token)
        return decoded_token  # This will return the user's info
    except Exception as exc:
        logger.error("Invalid token")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
        ) from exc


async def verify_firebase_token_ws(token: str = Query(...)):
    """
    Verify a Firebase token for WebSocket connections.

    Args:
        token: The token from query parameters

    Returns:
        dict: The decoded token with user information
    """
    if not token:  # Just check if the token is provided
        logger.error("Invalid or missing token")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing token",
        )
    try:
        decoded_token = auth.verify_id_token(token)
        return decoded_token
    except Exception as exc:
        logger.error("Invalid token")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
        ) from exc
