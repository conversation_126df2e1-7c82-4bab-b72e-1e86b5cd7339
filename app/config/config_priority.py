# must be before llm imports

# Must precede any llm module imports
#
# # AttributeError: module 'weaviate.collections.collections' has no attribute '_Collections'
# # sys:1: ResourceWarning: unclosed file <_io.TextIOWrapper name=0 mode='r' encoding='UTF-8'>
# from langtrace_python_sdk import langtrace
import os

from traceloop.sdk import Traceloop


# langtrace.init(api_key=os.environ.get("LANGTRACE_API_KEY"))


Traceloop.init(
    app_name="DiogenesAIChatbot",
    disable_batch=True,
    api_key=os.getenv("TRACELOOP_API_KEY"),
    api_endpoint=os.getenv("MW_TARGET"),
    headers={
        "Authorization": os.getenv("MW_API_KEY"),
        "X-Trace-Source": "traceloop",
    },
)


# import openlit

# openlit.init(

#     otlp_endpoint=os.getenv("MW_TARGET"),
#     application_name="Diogenes AI Chatbot",
#     otlp_headers={
#         "Authorization": os.getenv("MW_API_KEY"),
#         "X-Trace-Source": "openlit",
#     },
# )


import sentry_sdk

sentry_sdk.init(
    dsn=os.getenv("SENTRY_DSN"),
    # Set traces_sample_rate to 1.0 to capture 100%
    # of transactions for tracing.
    traces_sample_rate=1.0,
    _experiments={
        # Set continuous_profiling_auto_start to True
        # to automatically start the profiler on when
        # possible.
        "continuous_profiling_auto_start": True,
    },
)
