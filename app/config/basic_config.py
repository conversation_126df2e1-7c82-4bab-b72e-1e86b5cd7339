import os

from langchain_groq import ChatGroq
from langchain_openai import OpenAIEmbeddings

BUCKET_NAME = "diogenesaichatbot.appspot.com"

default_model = "gpt-4o-mini"
embed_func = OpenAIEmbeddings(model="text-embedding-3-large")  # vector length 3072



# use ENV variables
MODEL = "llama-3.1-8b-instant"  # You can change this to the appropriate Groq model if needed
api_key_groq = os.getenv("GROQ_API_KEY")

# Initialize <PERSON><PERSON><PERSON><PERSON>'s ChatGroq model
llm_groq = ChatGroq(
    model=MODEL,
    temperature=0.7,  # Adjust temperature or other params as per your need
    max_tokens=8000,
    timeout=60,  # Set a timeout if necessary
    max_retries=2,  # Retry logic
)

