"""Configuration utilities for language models and environment setup."""

import os
from langchain_core.language_models import BaseChatModel
from langchain_openai import ChatOpenAI

from app.config.basic_config import default_model, llm_groq

DEFAULT_MAX_TOKENS = 16000


def get_chatgpt_basic(
    streaming: bool = True,
    verbose: bool = True,
    temperature: int = 0,
    max_tokens: int = DEFAULT_MAX_TOKENS,
) -> ChatOpenAI:
    """Return a preconfigured ``ChatOpenAI`` instance."""
    return ChatOpenAI(
        model=default_model,
        streaming=streaming,
        verbose=verbose,
        temperature=temperature,
        max_tokens=max_tokens,
        stream_usage=True,
    )


chatgpt_basic: ChatOpenAI = get_chatgpt_basic()


def setup_envs() -> None:
    """Initialise required environment variables for the application."""
    os.environ.setdefault("OPENAI_API_KEY", os.environ.get("OPENAI_API_KEY", ""))
    os.environ.setdefault("ANTHROPIC_API_KEY", os.environ.get("ANTHROPIC_API_KEY", ""))
    os.environ.setdefault("OPENAI_APIKEY", os.environ.get("OPENAI_API_KEY", ""))
    os.environ.setdefault("ALPHAVANTAGE_API_KEY", os.environ.get("ALPHAVANTAGE_API_KEY", ""))
    os.environ.setdefault(
        "AWS_OPENSEARCH_ADMIN_PASSWORD", os.environ.get("AWS_OPENSEARCH_ADMIN_PASSWORD", "")
    )
    os.environ.setdefault("KMP_DUPLICATE_LIB_OK", "TRUE")
    os.environ.setdefault(
        "FIREBASE_SERVICE_ACCOUNT_KEY", os.environ.get("FIREBASE_SERVICE_ACCOUNT_KEY", "")
    )
    os.environ.setdefault("GOOGLE_CSE_ID", os.environ.get("GOOGLE_CSE_ID", ""))
    os.environ.setdefault("GOOGLE_API_KEY", os.environ.get("GOOGLE_API_KEY", ""))
    os.environ.setdefault("GPLACES_API_KEY", os.environ.get("GOOGLE_API_KEY", ""))
    os.environ.setdefault("REPLICATE_API_TOKEN", os.environ.get("REPLICATE_TOKEN", ""))

    # Tracing setup
    os.environ.setdefault("LANGCHAIN_TRACING_V2", "true")
    os.environ.setdefault("LANGCHAIN_ENDPOINT", "https://api.smith.langchain.com")
    os.environ.setdefault("LANGCHAIN_API_KEY", os.environ.get("LANGCHAIN_API_KEY", ""))
    os.environ.setdefault("LANGCHAIN_PROJECT", "diogenesaichatbot")

    os.environ.setdefault("OPENAI_MODEL_NAME", default_model)
    os.environ.setdefault("OPENAI_API_BASE", os.environ.get("OPENAI_API_BASE", "https://api.openai.com/v1"))
    os.environ.setdefault("FIRECRAWL_API_KEY", os.environ.get("FIRECRAWL_API_KEY", "test"))
    os.environ.setdefault("FIRECRAWL_API_URL", os.environ.get("FIRECRAWL_API_URL", "http://localhost:3002"))
    os.environ.setdefault("QDRANT_URL", os.environ.get("QDRANT_ENDPOINT", ""))
    os.environ.setdefault("WEAVIATE_ENDPOINT", os.environ.get("WEAVIATE_URL", ""))


llm_fast: BaseChatModel = llm_groq
llm_writing: BaseChatModel = llm_fast
