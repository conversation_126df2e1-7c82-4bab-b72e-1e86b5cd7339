from typing import List

from fastapi import Body, Query, HTTPException, APIRouter, Depends
from starlette.responses import JSONResponse

from app.config.basic_config import embed_func
from app.config.verify_token import verify_firebase_token
from app.core.tracing_setup import logger
from app.core.vector_db_config import get_vector_db_crud_static
from app.models.model import (
    DocumentBotRequestData,
    DocumentContentRequestData,
    DocumentRequestData,
)
from app.utils.document_embedding import DocumentEmbedding

router = APIRouter()


@router.post("/create_document")
async def create_document(
    document_data: dict = Body(...),
    original_user_id: str = Body(...),
    diogenes_search=Depends(get_vector_db_crud_static),
    decoded_token=Depends(verify_firebase_token),
):
    try:
        diogenes_search.create_document(document_data, original_user_id)
        logger.info(f"Document created successfully for user {original_user_id}")
        return JSONResponse({"success": True})
    except Exception as e:
        logger.error(f"Error creating document for user {original_user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error creating document")


@router.put("/update_document")
async def update_document(
    document_id: str = Body(...),
    updated_data: dict = Body(...),
    original_user_id: str = Body(...),
    diogenes_search=Depends(get_vector_db_crud_static),
    decoded_token=Depends(verify_firebase_token),
):
    try:
        diogenes_search.update_document(document_id, updated_data, original_user_id)
        logger.info(
            f"Document {document_id} updated successfully for user {original_user_id}"
        )
        return {"success": True}
    except Exception as e:
        logger.error(
            f"Error updating document {document_id} for user {original_user_id}: {str(e)}"
        )
        raise HTTPException(status_code=500, detail="Error updating document")


@router.delete("/delete_document")
async def delete_document(
    document_id: str = Body(...),
    original_user_id: str = Body(...),
    diogenes_search=Depends(get_vector_db_crud_static),
    decoded_token=Depends(verify_firebase_token),
):
    try:
        diogenes_search.delete_documents([document_id], original_user_id)
        logger.info(
            f"Document {document_id} deleted successfully for user {original_user_id}"
        )
        return {"success": True}
    except Exception as e:
        logger.error(
            f"Error deleting document {document_id} for user {original_user_id}: {str(e)}"
        )
        raise HTTPException(status_code=500, detail="Error deleting document")


@router.post("/delete_documents_by_firebase_urls")
async def delete_documents_by_firebase_urls(
    firebase_urls: List[str] = Body(...),
    original_user_id: str = Body(...),
    diogenes_search=Depends(get_vector_db_crud_static),
    decoded_token=Depends(verify_firebase_token),
):
    try:
        diogenes_search.delete_documents_by_firebase_urls(
            firebase_urls, original_user_id
        )
        logger.info(f"Documents deleted successfully for user {original_user_id}")
        return {"status": "success", "message": "Documents deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting documents for user {original_user_id}: {str(e)}")
        return JSONResponse(status_code=500, content={"error": str(e)})


@router.get("/search_document")
async def search_document(
    query: str = Query(...),
    original_user_id: str = Query(...),
    decoded_token=Depends(verify_firebase_token),
    diogenes_search=Depends(get_vector_db_crud_static),
):
    try:
        response = diogenes_search.search_document(query, original_user_id)
        logger.info(f"Document search successful for user {original_user_id}")
        return response
    except Exception as e:
        logger.error(f"Error searching document for user {original_user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error searching document")


@router.get("/document_exists")
async def document_exists(
    document_id: str = Query(...),
    original_user_id: str = Query(...),
    diogenes_search=Depends(get_vector_db_crud_static),
    decoded_token=Depends(verify_firebase_token),
):
    try:
        exists = diogenes_search.document_exists(document_id, original_user_id)
        logger.info(f"Document existence check completed for user {original_user_id}")
        return {"exists": exists}
    except Exception as e:
        logger.error(
            f"Error checking document existence for user {original_user_id}: {str(e)}"
        )
        raise HTTPException(status_code=500, detail="Error checking document existence")


@router.get("/search_document_by_embedding")
async def search_document_by_embedding(
    embedding_vector: List[float] = Query(...),
    original_user_id: str = Query(...),
    original_bot_id: str = Query(...),
    decoded_token=Depends(verify_firebase_token),
    diogenes_search=Depends(get_vector_db_crud_static),
):
    try:
        response = diogenes_search.search_document_by_embedding(
            embedding_vector, original_user_id, original_bot_id
        )
        logger.info(
            f"Document search by embedding successful for user {original_user_id} and bot {original_bot_id}"
        )
        return response
    except Exception as e:
        logger.error(
            f"Error searching document by embedding for user {original_user_id} and bot {original_bot_id}: {str(e)}"
        )
        raise HTTPException(
            status_code=500, detail="Error searching document by embedding"
        )


@router.post("/embed_document")
async def embed_document(
    firebase_urls: List[str] = Body(...),
    user_id: str = Body(...),
    bool_updateOnExist: bool = Body(...),
    optional_predefined_content_tofill: List[str] = Body(...),
    diogenes_search=Depends(get_vector_db_crud_static),
    decoded_token=Depends(verify_firebase_token),
):
    if not firebase_urls or not user_id:
        logger.error(f"Missing required parameters for user {user_id}")
        raise HTTPException(status_code=400, detail="Missing required parameters")

    try:
        vector_database_embedding = DocumentEmbedding(diogenes_search, embed_func)
        await vector_database_embedding.embed_document_from_file_url(
            firebase_urls,
            user_id,
            bool_updateOnExist,
            optional_predefined_content_tofill,
        )
        logger.info(f"Document embedded successfully for user {user_id}")
        return {"status": "success"}
    except Exception as e:
        logger.error(f"Error embedding document for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/assign_documents_to_bot")
async def assign_documents_to_bot(
    request_data: DocumentBotRequestData = Body(...),
    decoded_token=Depends(verify_firebase_token),
    diogenes_search=Depends(get_vector_db_crud_static),
):
    try:
        original_bot_id = request_data.original_bot_id
        document_firebase_urls = request_data.document_firebase_urls
        original_user_id = request_data.original_user_id

        diogenes_search.assign_documents_to_bot_by_firebase_url(
            original_bot_id, document_firebase_urls, original_user_id
        )
        logger.info(
            f"Documents assigned to bot {original_bot_id} for user {original_user_id}"
        )
        return {
            "status": "success",
            "message": "Documents assigned to bot successfully",
        }
    except Exception as e:
        logger.error(
            f"Error assigning documents to bot {original_bot_id} for user {original_user_id}: {str(e)}"
        )
        raise HTTPException(status_code=500, detail="Error assigning documents to bot")


@router.post("/remove_documents_from_bot")
async def remove_documents_from_bot(
    request_data: DocumentBotRequestData = Body(...),
    decoded_token=Depends(verify_firebase_token),
    diogenes_search=Depends(get_vector_db_crud_static),
):
    try:
        original_bot_id = request_data.original_bot_id
        document_firebase_urls = request_data.document_firebase_urls
        original_user_id = request_data.original_user_id

        diogenes_search.remove_documents_from_bot_by_firebase_url(
            original_bot_id, document_firebase_urls, original_user_id
        )
        logger.info(
            f"Documents removed from bot {original_bot_id} for user {original_user_id}"
        )
        return {
            "status": "success",
            "message": "Documents removed from bot successfully",
        }
    except Exception as e:
        logger.error(
            f"Error removing documents from bot {original_bot_id} for user {original_user_id}: {str(e)}"
        )
        raise HTTPException(status_code=500, detail="Error removing documents from bot")


@router.post("/get_document_content")
async def get_document_content(
    request_data: DocumentContentRequestData = Body(...),
    decoded_token=Depends(verify_firebase_token),
    diogenes_search=Depends(get_vector_db_crud_static),
):
    try:
        firebase_urls = request_data.firebase_urls
        original_user_id = request_data.original_user_id

        document_contents = diogenes_search.get_document_content_by_firebase_urls(
            firebase_urls, original_user_id
        )
        logger.info(
            f"Document content retrieved successfully for user {original_user_id}"
        )
        return document_contents
    except Exception as e:
        logger.error(
            f"Error getting document content for user {original_user_id}: {str(e)}"
        )
        raise HTTPException(status_code=500, detail="Error getting document content")


@router.post("/get_document_ids_by_firebase_urls")
async def get_document_ids_by_firebase_urls(
    request_data: DocumentRequestData = Body(...),
    decoded_token=Depends(verify_firebase_token),
    diogenes_search=Depends(get_vector_db_crud_static),
):
    try:
        original_user_id = request_data.original_user_id
        document_firebase_urls = request_data.document_firebase_urls

        document_ids = diogenes_search.get_document_ids_by_firebase_urls(
            document_firebase_urls, original_user_id
        )
        logger.info(f"Document IDs retrieved successfully for user {original_user_id}")
        return {"document_ids": document_ids}
    except Exception as e:
        logger.error(
            f"Error getting document IDs for user {original_user_id}: {str(e)}"
        )
        raise HTTPException(status_code=500, detail="Error getting document IDs")


@router.get("/get_bot_documents")
async def get_bot_documents(
    original_bot_id: str = Body(...),
    original_user_id: str = Body(...),
    diogenes_search=Depends(get_vector_db_crud_static),
    decoded_token=Depends(verify_firebase_token),
):
    try:
        documents = diogenes_search.get_bot_documents(original_bot_id, original_user_id)
        logger.info(
            f"Bot documents retrieved successfully for bot {original_bot_id} and user {original_user_id}"
        )
        return JSONResponse(documents)
    except Exception as e:
        logger.error(
            f"Error getting bot documents for bot {original_bot_id} and user {original_user_id}: {str(e)}"
        )
        raise HTTPException(status_code=500, detail="Error getting bot documents")


@router.get("/get_documents")
async def get_documents(
    original_user_id: str = Query(...),
    diogenes_search=Depends(get_vector_db_crud_static),
    decoded_token=Depends(verify_firebase_token),
):
    """
    Get all documents for a user.

    Args:
        original_user_id: The user ID to get documents for
        diogenes_search: Vector database CRUD instance
        decoded_token: Firebase token for authentication

    Returns:
        List of documents for the user
    """
    try:
        # TODO: Implement get_documents method in vector database CRUD
        # For now, return empty list as placeholder
        documents = []
        logger.info(f"Documents retrieved successfully for user {original_user_id}")
        return documents
    except Exception as e:
        logger.error(f"Error getting documents for user {original_user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error getting documents")
