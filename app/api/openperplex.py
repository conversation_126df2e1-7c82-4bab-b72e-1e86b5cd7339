import json
import time
import uuid
from datetime import datetime

from fastapi import Query
from fastapi import WebSocket, WebSocketDisconnect, APIRouter
from starlette.websockets import WebSocketState

from app.config.verify_token import verify_firebase_token_ws
from app.core.tracing_setup import logger
from app.frameworks.openperflex.build_context import build_context
from app.frameworks.openperflex.groq_api import get_answer, get_relevant_questions
from app.frameworks.openperflex.sources_manipulation import populate_sources
from app.frameworks.openperflex.sources_searcher import get_sources

# load_dotenv()

router = APIRouter()




@router.websocket("/ws/openperplex_search")
async def websocket_openperplex_search(websocket: WebSocket, token: str = Query(...)):
    await websocket.accept()

    # Verify the Firebase token
    decoded_token = await verify_firebase_token_ws(token)
    session_id: str = str(uuid.uuid4())
    # Send the session ID to the client
    await websocket.send_json({"type": "session_id", "session_id": session_id})

    try:
        while True:
            try:
                request = await websocket.receive_json()
            except WebSocketDisconnect:
                logger.info("Client disconnected")
                break

            user_id = request.get("user_id")
            if not user_id:
                await websocket.send_json({'type': 'finished', 'data': "user_id is required"})
                continue

            query = request.get("query")
            if not query:
                await websocket.send_json({'type': 'finished', 'data': "query is required"})
                continue

            initial_time = time.time()
            pro_mode: str = request.get("pro_mode", True)
            stored_location: str = request.get("stored_location", "us")
            date_context: str = request.get("date_context", datetime.today().strftime("%Y-%m-%d"))

            # Step 1: Get sources result
            start_time = time.time()
            sources_result = await get_sources(query, pro_mode, stored_location)
            end_time = time.time()
            logger.info(f"Step 1 (Get sources): {end_time - start_time:.4f} seconds")
            await websocket.send_json({'type': 'sources', 'data': sources_result})

            if sources_result.get('organic') is not None and pro_mode is True:
                sources_result['organic'] = await populate_sources(sources_result['organic'], len(sources_result['organic']))

            # Step 2: Build context for LLM
            start_time = time.time()
            search_contexts = await build_context(sources_result, query, pro_mode, date_context)
            end_time = time.time()
            logger.info(f"Step 2 (Build context): {end_time - start_time:.4f} seconds")

            # Step 3: Get answers from LLM
            start_time = time.time()
            async for chunk in get_answer(query, search_contexts, date_context):
                await websocket.send_json({'type': 'llm', 'text': chunk})
            end_time = time.time()
            logger.info(f"Step 3 (Get answers): {end_time - start_time:.4f} seconds")

            # Step 4: Get relevant questions
            start_time = time.time()
            try:
                relevant_questions = await get_relevant_questions(search_contexts, query)
                relevant_json = json.loads(relevant_questions)
                await websocket.send_json({'type': 'relevant', 'data': relevant_json})
            except Exception as e:
                logger.info(f"Error in relevant questions: {e}")
                await websocket.send_json({'type': 'relevant', 'data': []})
            end_time = time.time()
            logger.info(f"Step 4 (Get relevant questions): {end_time - start_time:.4f} seconds")

            # Step 5: Notify the client that the process is finished
            start_time = time.time()
            await websocket.send_json({'type': 'finished', 'data': ""})
            end_time = time.time()
            logger.info(f"Step 5 (Notify finished): {end_time - start_time:.4f} seconds")
            logger.info(f"total time: {end_time - initial_time:.4f} seconds")

    except WebSocketDisconnect:
        logger.info("WebSocket disconnected")
    except Exception as e:
        logger.info(f"Error in WebSocket endpoint: {e}")
        try:
            await websocket.send_json({'type': 'error', 'message': "We are currently experiencing some issues. Please try again later."})
        except RuntimeError:
            logger.info("Cannot send message, WebSocket is closed")
    finally:
        if websocket.client_state == WebSocketState.CONNECTED:  # Ensure WebSocket is still connected
            await websocket.close()
            logger.info("WebSocket closed successfully")

