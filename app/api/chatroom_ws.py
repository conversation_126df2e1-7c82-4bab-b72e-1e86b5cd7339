import os

from firebase_admin import auth
import json

from fastapi import WebSocket, WebSocketDisconnect, APIRouter, Depends, HTTPException
from firebase_admin import auth
from starlette.responses import JSONResponse
from websockets.exceptions import ConnectionClosedError
from livekit import api

from app.config.verify_token import verify_firebase_token, verify_firebase_token_ws
from app.core.tracing_setup import logger

router = APIRouter()
chatrooms = {}


def add_user_to_chatroom(user_id: str, chatroom_id: str, websocket: WebSocket):
    if chatroom_id not in chatrooms:
        chatrooms[chatroom_id] = {}

    chatrooms[chatroom_id][user_id] = websocket


def remove_user_from_chatroom(user_id: str, chatroom_id: str):
    if chatroom_id in chatrooms and user_id in chatrooms[chatroom_id]:
        del chatrooms[chatroom_id][user_id]

        if len(chatrooms[chatroom_id]) == 0:
            del chatrooms[chatroom_id]


async def broadcast_to_chatroom(chatroom_id: str, message: str, sender_user_id: str):
    if chatroom_id in chatrooms:
        users_in_chatroom = chatrooms[chatroom_id]
        for user_id, websocket in users_in_chatroom.items():
            if user_id != sender_user_id:
                try:
                    await websocket.send_text(message)
                except ConnectionClosedError:
                    pass  # Handle case where the connection is closed


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()

    params = dict(websocket.query_params)
    token = params.get("token")
    chatroom_id = params.get("chatroomId")

    decoded_token = verify_firebase_token_ws(token)

    if not decoded_token:
        await websocket.close()
        return

    user_id = decoded_token["uid"]

    add_user_to_chatroom(user_id, chatroom_id, websocket)

    logger.i(f"User {user_id} connected to chatroom {chatroom_id}")

    try:
        while True:
            data = await websocket.receive_text()
            try:
                parsed_message = json.loads(data)
                logger.i(
                    f"Received from {user_id} in chatroom {chatroom_id}: {parsed_message}"
                )
                await broadcast_to_chatroom(
                    chatroom_id, json.dumps(parsed_message), user_id
                )
            except json.JSONDecodeError:
                if data == "ping":
                    logger.i("Received ping message")
                else:
                    logger.i(
                        f"Received plain text message from {user_id} in chatroom {chatroom_id}: {data}"
                    )

    except WebSocketDisconnect:
        logger.i(f"User {user_id} disconnected from chatroom {chatroom_id}")
        remove_user_from_chatroom(user_id, chatroom_id)
    except Exception as e:
        logger.error(f"Error: {e}")


@router.get("/getToken")
async def get_token(
    name: str,
    room_id: str,
    decoded_token=Depends(verify_firebase_token),
):
    try:
        user_id: str = decoded_token["uid"]
        email: str = decoded_token["email"]
        token = (
            api.AccessToken(
                os.getenv("LIVEKIT_API_KEY"), os.getenv("LIVEKIT_API_SECRET")
            )
            .with_identity(" email: " + email)
            .with_name(name)
            .with_grants(
                api.VideoGrants(
                    room_join=True,
                    room=room_id,
                )
            )
        )
        return JSONResponse(content={"token": token.to_jwt()})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
