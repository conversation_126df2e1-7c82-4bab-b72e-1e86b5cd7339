import os
import uuid

from fastapi import FastAPI, HTTPException, APIRouter, Depends
from pydantic import BaseModel
import requests
import base64

from app.config.verify_token import verify_firebase_token

router = APIRouter()

# Define the request model
class ImageSegmentationRequest(BaseModel):
    prompt: str
    image: str  # Base64 encoded image
    confidence_threshold: float = 0.5

class ImageSegmentationResponse(BaseModel):
    predictions: list

@router.post("/segment-image", response_model=ImageSegmentationResponse)
async def segment_image(request: ImageSegmentationRequest, decoded_token=Depends(verify_firebase_token)):
    PROJECT_ID = "diogenesaichatbot"  # Replace with your project ID
    url = f"https://us-central1-aiplatform.googleapis.com/v1/projects/{PROJECT_ID}/locations/us-central1/publishers/google/models/image-segmentation-001:predict"
    user_id = decoded_token["user_id"]
    session_id:str = str(uuid.uuid4())

    # Prepare the payload
    payload = {
        "instances": [
            {
                "prompt": request.prompt,
                "image": {
                    "bytesBase64Encoded": request.image
                }
            }
        ],
        "parameters": {
            "mode": "prompt",
            "confidenceThreshold": request.confidence_threshold
        }
    }

    headers = {
        "Authorization": f"Bearer {get_access_token()}",  # Function to get the access token
        "Content-Type": "application/json; charset=utf-8"
    }

    # Send the request to the Vertex AI API
    response = requests.post(url, json=payload, headers=headers)

    if response.status_code != 200:
        raise HTTPException(status_code=response.status_code, detail=response.json())

    return ImageSegmentationResponse(predictions=response.json().get('predictions', []))

def get_access_token():
    """Fetches access token for Google Cloud API."""
    # This function should implement the logic to obtain a valid access token
    # For example, using Google Cloud SDK or a service account
    return os.environ.get("FIREBASE_SERVICE_ACCOUNT_KEY") # Replace with the logic to obtain the token