import concurrent.futures as cf
import glob
import io
import os
import time
import uuid
from pathlib import Path
from tempfile import Named<PERSON>emporaryF<PERSON>
from typing import List, Literal, Optional

from fastapi import HTTPException, APIRouter, Form, UploadFile, Depends
from firebase_admin import storage
from langchain.output_parsers import RetryOutputParser
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import PromptTemplate
from loguru import logger
from openai import OpenAI
from pydantic import BaseModel
from pypdf import PdfReader

from app.config.basic_config import BUCKET_NAME
from app.config.config import chatgpt_basic
from app.config.verify_token import verify_firebase_token

router = APIRouter()


async def upload_audio_to_firebase(audio_bytes: bytes, user_id: str, session_id: str) -> str:
    try:
        logger.info("Starting upload of audio to Firebase.")
        bucket = storage.bucket(BUCKET_NAME)

        # Create a unique file name using user_id and session_id
        file_name = f"audios/{user_id}/{int(time.time())}.mp3"
        blob = bucket.blob(file_name)

        # Upload the audio bytes
        blob.upload_from_string(audio_bytes, content_type='audio/mpeg')

        # Make the file publicly accessible and get the download URL
        blob.make_public()
        logger.info("Audio uploaded successfully.")
        return blob.public_url

    except Exception as e:
        logger.error(f"Error uploading audio to Firebase: {str(e)}")
        raise


class DialogueItem(BaseModel):
    text: str
    speaker: Literal["speaker-1", "speaker-2"]


class Dialogue(BaseModel):
    # scratchpad: str
    dialogue: List[DialogueItem]


def get_mp3(text: str, voice: str, audio_model: str, api_key: str = None) -> bytes:
    client = OpenAI(
        api_key=api_key or os.getenv("OPENAI_API_KEY"),
    )

    try:
        logger.info(f"Generating MP3 for text: {text[:30]}...")  # Log only first 30 chars for brevity
        start_ = time.time()
        with client.audio.speech.with_streaming_response.create(
                model=audio_model,
                voice=voice,
                input=text,
        ) as response:
            with io.BytesIO() as file:
                for chunk in response.iter_bytes():
                    file.write(chunk)
                logger.info(f"generating mp3 takes time {time.time() - start_:.2f} seconds. ")
                return file.getvalue()

    except Exception as e:
        logger.error(f"Error generating MP3: {str(e)}")
        raise


async def generate_audio(
        files: list,
        text_model: str = "gpt-4o-mini",
        audio_model: str = "tts-1",
        speaker_1_voice: str = "alloy",
        speaker_2_voice: str = "echo",
        intro_instructions: str = '',
        text_instructions: str = '',
        scratch_pad_instructions: str = '',
        prelude_dialog: str = '',
        podcast_dialog_instructions: str = '',
        edited_transcript: str = None,
        user_feedback: str = None,
        original_text: str = None,
        debug=False,
        user_id: str = "",
        session_id: str = "",
) -> tuple:
    api_base: str = "https://api.openai.com/v1"
    openai_api_key: str = os.environ.get('OPENAI_API_KEY')
    combined_text = original_text or ""

    # If there's no original text, extract it from the uploaded files
    if not combined_text:
        for file in files:
            with Path(file).open("rb") as f:
                reader = PdfReader(f)
                text = "\n\n".join([page.extract_text()
                                    for page in reader.pages if page.extract_text()])
                combined_text += text + "\n\n"

    async def generate_dialogue(text: str, intro_instructions: str, text_instructions: str,
                                scratch_pad_instructions: str, prelude_dialog: str,
                                podcast_dialog_instructions: str, edited_transcript: str = None,
                                user_feedback: str = None) -> Dialogue:
        """
        remember the speaker should be one of ["speaker-1", "speaker-2"]

        {intro_instructions}

        Here is the original input text:

        <input_text>
        {text}
        </input_text>

        {text_instructions}

        <scratchpad>
        {scratch_pad_instructions}
        </scratchpad>

        {prelude_dialog}

        <podcast_dialogue>
        {podcast_dialog_instructions}
        </podcast_dialogue>
        {edited_transcript}{user_feedback}
        """
        user_input: str = f"""
        {intro_instructions}

        Here is the original input text:

        <input_text>
        {text}
        </input_text>

        {text_instructions}

        <scratchpad>
        {scratch_pad_instructions}
        </scratchpad>

        {prelude_dialog}

        <podcast_dialogue>
        {podcast_dialog_instructions}
        </podcast_dialogue>
        {edited_transcript}{user_feedback}
        """
        llm_ = chatgpt_basic
        parser = PydanticOutputParser(pydantic_object=Dialogue)
        prompt = PromptTemplate(
            template="Answer the user query.\n{format_instructions}\n{query}\n",
            input_variables=["query"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )
        prompt_value = prompt.format_prompt(query=user_input)
        retry_parser = RetryOutputParser.from_llm(parser=parser, llm=llm_)

        completion_chain = prompt | llm_ | parser

        try:
            logger.info("Generating dialogue...")
            response = await completion_chain.ainvoke({"query": user_input})

            return response
        except Exception as e:
            logger.error(f"Error generating dialogue: {str(e)}")
            raise

    instruction_improve = 'Based on the original text, please generate an improved version of the dialogue by incorporating the edits, comments and feedback.'
    edited_transcript_processed = "\nPreviously generated edited transcript, with specific edits and comments that I want you to carefully address:\n" + \
                                  "<edited_transcript>\n" + edited_transcript + \
                                  "</edited_transcript>" if edited_transcript else ""
    user_feedback_processed = "\nOverall user feedback:\n\n" + \
                              user_feedback if user_feedback else ""

    if edited_transcript_processed.strip() or user_feedback_processed.strip():
        user_feedback_processed = "<requested_improvements>" + user_feedback_processed + \
                                  "\n\n" + instruction_improve + "</requested_improvements>"

    if debug:
        logger.info(edited_transcript_processed)
        logger.info(user_feedback_processed)

    start_time = time.time()
    logger.info("start generate dialogue:")
    try:
        llm_output = await generate_dialogue(
            combined_text,
            intro_instructions=intro_instructions,
            text_instructions=text_instructions,
            scratch_pad_instructions=scratch_pad_instructions,
            prelude_dialog=prelude_dialog,
            podcast_dialog_instructions=podcast_dialog_instructions,
            edited_transcript=edited_transcript_processed,
            user_feedback=user_feedback_processed
        )
    except Exception as e:
        logger.error(f"Error generating dialogue: {str(e)}")
        raise

    logger.info(f"Dialogue generation completed in {time.time() - start_time:.2f} seconds.")

    # Generate audio from the transcript
    audio = b""
    transcript = ""
    characters = 0

    with cf.ThreadPoolExecutor() as executor:
        futures = []
        for line in llm_output.dialogue:
            transcript_line = f"{line.speaker}: {line.text}"
            voice = speaker_1_voice if line.speaker == "speaker-1" else speaker_2_voice
            future = executor.submit(
                get_mp3, line.text, voice, audio_model, openai_api_key)
            futures.append((future, transcript_line))
            characters += len(line.text)

        for future, transcript_line in futures:
            try:
                audio_chunk = future.result()
                audio += audio_chunk
                transcript += transcript_line + "\n\n"
            except Exception as e:
                logger.error(f"Error retrieving audio chunk: {str(e)}")
                continue

    logger.info(f"Generated {characters} characters of audio")

    temporary_directory = f"./tmp/{user_id}/{session_id}"
    os.makedirs(temporary_directory, exist_ok=True)

    temporary_file = NamedTemporaryFile(
        dir=temporary_directory,
        delete=False,
        suffix=".mp3",
    )
    temporary_file.write(audio)
    temporary_file.close()

    # Delete any files in the temp directory that end with .mp3 and are over a day old
    for file in glob.glob(f"{temporary_directory}*.mp3"):
        if os.path.isfile(file) and time.time() - os.path.getmtime(file) > 24 * 60 * 60:
            os.remove(file)

    return temporary_file.name, audio, transcript, combined_text


async def validate_and_generate_audio(*args):
    files = args[0]
    if not files:
        return None, None, None, "Please upload at least one PDF file before generating audio."
    try:
        audio_file, audio, transcript, original_text = await generate_audio(*args)
        return audio_file, audio, transcript, original_text, None
    except Exception as e:
        logger.error(f"Error in validate_and_generate_audio: {str(e)}")
        return None, None, None, None, str(e)


async def edit_and_regenerate(edited_transcript, user_feedback, *args):
    new_args = list(args)
    new_args[-2] = edited_transcript  # Update edited transcript
    new_args[-1] = user_feedback  # Update user feedback
    return await validate_and_generate_audio(*new_args)


async def process_feedback_and_regenerate(feedback, *args):
    new_args = list(args)
    new_args.append(feedback)  # Add user feedback as a new argument
    return await validate_and_generate_audio(*new_args)


@router.post("/generate_audio")
async def generate_audio_api(
        files: List[UploadFile],
        text_model: str = Form("gpt-4o-mini"),
        audio_model: str = Form("tts-1"),
        speaker_1_voice: str = Form("alloy"),
        speaker_2_voice: str = Form("echo"),
        intro_instructions: Optional[str] = Form(''),
        text_instructions: Optional[str] = Form(''),
        scratch_pad_instructions: Optional[str] = Form(''),
        prelude_dialog: Optional[str] = Form(''),
        podcast_dialog_instructions: Optional[str] = Form(''),
        edited_transcript: Optional[str] = Form(None),
        user_feedback: Optional[str] = Form(None),
        decoded_token=Depends(verify_firebase_token),
):
    start_time = time.time()
    try:
        logger.info("Starting audio generation API endpoint.")
        session_id = str(uuid.uuid4())
        user_id = decoded_token["user_id"]

        # Define a unique temporary directory for the user session
        temp_dir = Path(f"temp/{user_id}/{session_id}")
        temp_dir.mkdir(parents=True, exist_ok=True)

        # Save the uploaded files temporarily with a sanitized file path
        file_paths = []
        for file in files:
            file_name = os.path.basename(file.filename)
            file_location = temp_dir / file_name

            with open(file_location, "wb+") as file_object:
                file_object.write(file.file.read())
            file_paths.append(str(file_location))

        # Call the generate_audio function
        audio_file, audio, transcript, combined_text = await generate_audio(
            file_paths,
            text_model=text_model,
            audio_model=audio_model,
            speaker_1_voice=speaker_1_voice,
            speaker_2_voice=speaker_2_voice,
            intro_instructions=intro_instructions,
            text_instructions=text_instructions,
            scratch_pad_instructions=scratch_pad_instructions,
            prelude_dialog=prelude_dialog,
            podcast_dialog_instructions=podcast_dialog_instructions,
            edited_transcript=edited_transcript,
            user_feedback=user_feedback,
            debug=True,
            user_id=user_id,
            session_id=session_id,
        )

        # Upload the audio to Firebase
        audio_url = await upload_audio_to_firebase(audio, user_id, session_id)

        logger.info(f"Audio generation completed in {time.time() - start_time:.2f} seconds.")

        return {
            "audio_url": audio_url,
            "transcript": transcript,
            "combined_text": combined_text,
        }

    except Exception as e:
        logger.error(f"Error generating audio: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")
