import asyncio
import os
import uuid
from typing import Dict, List, Any

import autogen
from autogen import GroupChat, GroupChatManager, ConversableAgent
from autogen.agentchat.contrib.agent_builder import AgentBuilder
from autogen.io import IOStream, IOWebsockets
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, Depends, Query
from pydantic import BaseModel

from app.config.basic_config import default_model
from app.config.verify_token import verify_firebase_token, verify_firebase_token_ws
from app.core.tracing_setup import tracer, logger

router = APIRouter()

default_llm_config = {
    'temperature': 0,
    "max_tokens": 16000,
    "config_list": [{
        'model': default_model,
        'api_key': os.environ.get('OPENAI_API_KEY'),
    }]
}

builder = AgentBuilder(builder_model=default_model, agent_model=default_model)

workflow_data: Dict[str, List[str]] = {}
workflow_managers: Dict[str, autogen.GroupChatManager] = {}
workflow_sockets: Dict[str, WebSocket] = {}


class HumanInput(BaseModel):
    message: str


def add_workflow_data(workflow_id: str, data: str):
    workflow_data.setdefault(workflow_id, []).append(data)


@router.websocket("/ws/stream")
async def websocket_endpoint(websocket: WebSocket, token: str = Query(...)):

    workflow_id = None
    try:
        await websocket.accept()
        logger.info("WebSocket connection accepted")

        # Verify the Firebase token
        decoded_token = await verify_firebase_token_ws(token)

        workflow_id = await websocket.receive_text()
        logger.info(f"Received workflow_id: {workflow_id}")

        workflow_sockets[workflow_id] = websocket
        await stream_workflow_data(websocket, workflow_id)
    except WebSocketDisconnect:
        handle_websocket_disconnect(workflow_id)
    except Exception as e:
        logger.error(f"Error in WebSocket connection: {e}")


async def stream_workflow_data(websocket: WebSocket, workflow_id: str):
    while True:
        if workflow_id not in workflow_data:
            await asyncio.sleep(1)
            continue

        while workflow_data[workflow_id]:
            data = workflow_data[workflow_id].pop(0)
            await websocket.send_text(data)
        await asyncio.sleep(1)


def handle_websocket_disconnect(workflow_id: str):
    if workflow_id:
        logger.info(f"WebSocket disconnected for workflow {workflow_id}")
        workflow_sockets.pop(workflow_id, None)
    else:
        logger.info("WebSocket disconnected for workflow")


@router.post("/send-human-input")
async def send_human_input(workflow_id: str,
                           input: HumanInput,
                           decoded_token=Depends(verify_firebase_token),):
    manager = workflow_managers.get(workflow_id)
    if not manager:
        raise HTTPException(status_code=404, detail="Workflow not found")

    io_stream : IOWebsockets = IOStream.get_default()

    await manager.a_send(input.message, manager.last_speaker)
    return {"status": "Input received"}


@router.post("/start-workflow")
async def start_workflow(agent_data: Dict,
                         decoded_token=Depends(verify_firebase_token),):
    workflow_id = str(uuid.uuid4())
    auto_build = 'agents' not in agent_data or 'tasks' not in agent_data
    user_proxy, initial_chats, manager = await initialize_manager_or_autobuild(agent_data, workflow_id, auto_build)

    workflow_managers[workflow_id] = manager
    asyncio.create_task(run_autogen_workflow(manager, workflow_id, agent_data, auto_build, user_proxy, initial_chats))

    return {"workflow_id": workflow_id}


async def initialize_manager_or_autobuild(agent_data: Dict, workflow_id: str, auto_build: bool):
    if auto_build:
        return await initialize_autogen_manager_autobuild(agent_data, workflow_id)
    else:
        return await initialize_manager(agent_data, workflow_id)


async def run_autogen_workflow(manager: autogen.GroupChatManager, workflow_id: str, agent_data: Dict, auto_build: bool,
                               user_proxy: ConversableAgent, initial_chats: List[Any]):
    try:
        building_task = agent_data["building_task"]
        if auto_build:
            await handle_auto_build(manager, workflow_id, building_task)
        else:
            await handle_user_interactions(manager, workflow_id, user_proxy, initial_chats, building_task)

        await asyncio.sleep(1)
    except Exception as e:
        logger.error(f"Error in workflow {workflow_id}: {str(e)}")
        add_workflow_data(workflow_id, f"Error in workflow {workflow_id}: {str(e)}")


async def handle_auto_build(manager: autogen.GroupChatManager, workflow_id: str, building_task: str):
    chat_results = await manager.groupchat.agents[0].a_initiate_chat(manager, message=building_task)
    add_workflow_data(workflow_id, f"Intermediate result: {chat_results}")
    response = await manager.a_run_chat()
    add_workflow_data(workflow_id, f"Intermediate result: {response}")

    if manager.a_check_termination_and_human_reply():
        final_output = manager.get_chat_results()
        add_workflow_data(workflow_id, f"Final output: {final_output}\n<END_OF_AGENT>")


async def handle_user_interactions(manager: autogen.GroupChatManager, workflow_id: str, user_proxy: ConversableAgent,
                                   initial_chats: List[Any], building_task: str):
    if user_proxy:
        chat_results = await user_proxy.a_initiate_chats(initial_chats)
        add_workflow_data(workflow_id, f"Intermediate result 1: {chat_results}")
        chat_results = await user_proxy.a_initiate_chat(manager, message=building_task)
        add_workflow_data(workflow_id, f"Intermediate result 2: {chat_results.chat_history}")

        if manager.a_check_termination_and_human_reply():
            final_output = user_proxy.get_chat_results()
            add_workflow_data(workflow_id, f"Final output: {final_output}\n<END_OF_AGENT>")
    else:
        raise ValueError("Missing required data: 'user_proxy'")


async def initialize_manager(agent_data: Dict, workflow_id: str) -> (
ConversableAgent, List[Dict], autogen.GroupChatManager):
    user_id = agent_data.get('user_id')
    agents = {agent_info.get('role'): create_agent(agent_info) for agent_info in agent_data.get('agents', [])}
    tasks = extract_tasks(agent_data)
    building_task = agent_data.get("building_task", "")

    if not building_task:
        raise ValueError("Missing required data: 'building_task' ")

    user_proxy = create_user_proxy()
    initial_chats = setup_initial_chats(tasks, agents, building_task)

    with tracer.start_as_current_span(f"autogen-{user_id}-{workflow_id}") as span:
        speaker_selection_method = "round_robin" if len(agents) <= 2 else "auto"
        group_chat = autogen.GroupChat(agents=list(agents.values()), messages=[], max_round=12,
                                       speaker_selection_method=speaker_selection_method,
                                       allow_repeat_speaker=list(agents.values()))
        manager = autogen.GroupChatManager(groupchat=group_chat, llm_config=default_llm_config)
        initial_chats.append({
            "chat_id": len(tasks) + 1,
            "recipient": manager,
            "message": building_task,
            "silent": False,
        })
        add_workflow_data(workflow_id, "Workflow initialized")
        return user_proxy, initial_chats, manager


def create_agent(agent_info: Dict) -> autogen.AssistantAgent:
    return autogen.AssistantAgent(
        name=agent_info.get('role'),
        llm_config=default_llm_config,
        system_message=f"Your role is: {agent_info.get('role', '')}; your goal: {agent_info.get('goal', '')}; your backstory: {agent_info.get('backstory', '')}",
    )


def extract_tasks(agent_data: Dict) -> List[Dict]:
    return [{
        "description": task_info.get('description'),
        "expected_output": task_info.get('expected_output'),
        "agent_role": task_info.get('agent_role')
    } for task_info in agent_data.get('tasks', [])]


def create_user_proxy() -> autogen.UserProxyAgent:
    return autogen.UserProxyAgent(
        name="User_proxy",
        system_message="A human admin.",
        code_execution_config=False,
        max_consecutive_auto_reply=3,
        human_input_mode="NEVER",
        default_auto_reply=None,
    )


def setup_initial_chats(tasks: List[Dict], agents: Dict[str, autogen.AssistantAgent], building_task: str) -> List[Dict]:
    return [{
        "chat_id": i + 1,
        "recipient": agents.get(task['agent_role']),
        "message": task['description'],
        "silent": False,
        "summary_method": "reflection_with_llm",
    } for i, task in enumerate(tasks)]


async def initialize_autogen_manager_autobuild(agent_data: Dict, workflow_id: str) -> autogen.GroupChatManager:
    building_task = agent_data.get("building_task", "")
    agent_list, agent_configs = builder.build(building_task, default_llm_config, coding=False)
    coding = agent_configs["coding"]

    initial_message = [{
        "content": f"You're an experienced project manager, skilled in overseeing complex projects and guiding teams to success. Your role is to coordinate the efforts of the crew members, ensuring that each task is completed on time and to the highest standard. The goal is {building_task}",
        "role": "system"
    }]

    group_chat = GroupChat(agents=agent_list, messages=[initial_message], max_round=12,
                           allow_repeat_speaker=agent_list[:-1] if coding else agent_list)
    manager = GroupChatManager(groupchat=group_chat, llm_config=default_llm_config)

    add_workflow_data(workflow_id, "Workflow initialized with autobuild")
    return None, None, manager
