"""
Autogen API module for handling autogen-related tasks.

This module provides an API endpoint for starting autogen tasks asynchronously.
"""

import os
import json
import uuid
from typing import Dict, Any

from fastapi.websockets import WebSocketState
from app.config.basic_config import default_model
from fastapi import (
    APIRouter,
    HTTPException,
    WebSocket,
    WebSocketDisconnect,
    Depends,
    Query,
)
from pydantic import BaseModel, ValidationError
from starlette.requests import Request

from app.config.verify_token import verify_firebase_token, verify_firebase_token_ws
from app.core.tracing_setup import logger

# from app.queue.autogen_tasks import async_kickoff_autogen

from autogen_core.base import CancellationToken
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models import OpenAIChatCompletionClient
from autogen_agentchat.base import Response

router = APIRouter()

# Create an agent that uses the OpenAI GPT-4o model.
model_client = OpenAIChatCompletionClient(
    model=default_model,
    api_key=os.environ.get("OPENAI_API_KEY"),
)


class AutogenResponse(BaseModel):
    task_id: str
    session_id: str


@router.websocket("/ws/ask_autogen")
async def ask_autogen_websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(...),
):
    """
    Handle AutoGen chat requests via WebSocket and stream the results back to the user.
    """
    await websocket.accept()
    session_id: str = str(uuid.uuid4())

    # Verify the Firebase token
    decoded_token = await verify_firebase_token_ws(token)
    user_id = decoded_token["user_id"]
    try:
        raw_data = await websocket.receive_text()

        # Decode the JSON string
        chat_request = json.loads(raw_data)
        logger.info(f"Parsed chat request: {chat_request}")

        # Initialize the AutoGen Assistant agent
        agent = AssistantAgent(
            name="assistant",
            model_client=model_client,  # Initialize your model client here
            tools=[],  # Add any tools you need
            system_message="You are helpful assistant",
        )

        # Prepare the message for the agent
        query = chat_request.get("query", "")
        if not query:
            await websocket.send_json({"event": "error", "data": "Query not provided"})
            return

        message = TextMessage(content=query, source="user")

        # Stream the agent's response
        async for event in agent.on_messages_stream(
            [message],
            cancellation_token=CancellationToken(),
        ):

            if isinstance(event, Response):
                # Send each message as it's generated
                await websocket.send_json(
                    {
                        "event": "autogent_stream",
                        "data": {
                            "content": event.chat_message.content,
                            "source": event.chat_message.source,
                        },
                    }
                )
            elif isinstance(event, TextMessage):
                # Send each message as it's generated
                await websocket.send_json(
                    {
                        "event": "autogent_stream",
                        "data": {
                            "content": event.content,
                            "source": event.source,
                            "inner_messages": str(event.inner_messages),
                        },
                    }
                )
            else:
                await websocket.send_json(
                    {
                        "event": "autogent_stream",
                        "data": str(event),
                    }
                )

    except WebSocketDisconnect:
        logger.error("Client disconnected")
        return
    except json.JSONDecodeError:
        logger.error("Received data is not valid JSON")
        await websocket.send_json({"event": "error", "data": "Invalid JSON format"})
    except Exception as e:
        logger.error(f"Error in WebSocket connection: {str(e)}")
        await websocket.send_json(
            {"event": "error", "data": "Error processing request"}
        )
    finally:
        try:
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.close()
                logger.info("WebSocket closed successfully")
        except Exception as close_error:
            logger.error(f"Error closing WebSocket: {str(close_error)}")


# @router.post("/start_autogen", response_model=AutogenResponse)
# async def start_task_autogen(
#     request: Request, decoded_token=Depends(verify_firebase_token)
# ) -> AutogenResponse:
#     """
#     Start an autogen task asynchronously.

#     Args:
#         request (Request): The incoming HTTP request containing the agent data.

#     Returns:
#         AutogenResponse: A response containing the task_id and session_id.

#     Raises:
#         HTTPException: If there's an error in processing the request or starting the task.
#     """
#     try:
#         agent_data_json: Dict[str, Any] = await request.json()
#         session_id: str = str(uuid.uuid4())

#         logger.info(f"Starting autogen task with session_id: {session_id}")

#         # Trigger the Celery task
#         task = async_kickoff_autogen.delay(agent_data_json, session_id)

#         logger.info(f"Autogen task started with task_id: {task.id}")

#         # Return the task id for tracking the task progress
#         return AutogenResponse(task_id=task.id, session_id=session_id)

#     except ValidationError as e:
#         logger.error(f"Validation error in start_task_autogen: {e.errors()}")
#         raise HTTPException(status_code=400, detail=str(e.errors()))
#     except Exception as e:
#         logger.error(f"Unexpected error in start_task_autogen: {str(e)}")
#         raise HTTPException(status_code=500, detail="Internal server error")
