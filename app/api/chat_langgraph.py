import uuid
from fastapi import <PERSON>Socket, APIRouter, WebSocketDisconnect, Query
from langchain_core.messages import HumanMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, MessagesState, START, END

from app.config.config import chatgpt_basic
from app.config.verify_token import verify_firebase_token_ws
from app.core.tracing_setup import logger

# Initialize LangGraph and Model
model = chatgpt_basic  # Replace with your model
memory = MemorySaver()

# Define the StateGraph for conversation
workflow = StateGraph(MessagesState)

# Define nodes for various user commands
def handle_greeting(state):
    return {"messages": [{"role": "assistant", "content": "Hello! How can I assist you today? I can tell you a joke about any topic!"}]}

def handle_joke_query(state):
    state["messages"].append({"role": "system", "content": "new_round"})
    return {"messages": [{"role": "assistant", "content": "What topic would you like a joke about?"}]}

def handle_topic(state):
    # Get the last human message directly from the state
    user_topic = state["messages"][-1].content if state["messages"] and state["messages"][-1].type == "human" else None
    if user_topic:
        return {"messages": [{"role": "assistant", "content": f"Let me come up with a joke about {user_topic}..."}]}
    else:
        return {"messages": [{"role": "assistant", "content": "Please provide a topic for the joke!"}]}

def handle_goodbye(state):
    return {"messages": [{"role": "assistant", "content": "Goodbye! Have a great day!"}]}


async def call_model(state):
    user_topic = state["messages"][-1].content if state["messages"] and state["messages"][-1].type == "human" else None

    if user_topic:
        # Generate a joke based on the user topic
        response = await model.ainvoke([
            {"role": "user", "content": f"Tell me a joke about {user_topic}."}
        ])

        joke = response['choices'][0]['message']['content'] if response[
            'choices'] else "I couldn't come up with a joke."
    else:
        joke = "I can't think of a joke without a topic!"

    return {"messages": [{"role": "assistant", "content": joke}]}

# Build the graph
workflow.add_node("greeting", handle_greeting)
workflow.add_node("joke_query", handle_joke_query)
workflow.add_node("topic", handle_topic)
workflow.add_node("goodbye", handle_goodbye)
workflow.add_node("agent", call_model)

# Define transitions
workflow.add_edge(START, "greeting")

def should_continue(state):
    user_input = state["messages"][-1].content.lower() if state["messages"] and state["messages"][-1].type == "human" else ""
    if "joke" in user_input:
        return "joke_query"
    elif "bye" in user_input:
        return "goodbye"
    else:
        return "topic"

# Define conditional edges
workflow.add_conditional_edges(
    "greeting",
    should_continue,
    {
        "joke_query": "joke_query",
        "goodbye": "goodbye",
        "topic": "topic",
    },
)

workflow.add_edge("joke_query", "topic")
workflow.add_edge("topic", "agent")
workflow.add_edge("agent", "greeting")
workflow.add_edge("goodbye", END)

# Compile the workflow
app = workflow.compile(checkpointer=memory)

router = APIRouter()

@router.websocket("/ws/chat_langgraph")
async def chat(websocket: WebSocket, token: str = Query(...)):
    session_id: str = str(uuid.uuid4())
    await websocket.accept()
    logger.info("WebSocket connection established.")
    decoded_token = await verify_firebase_token_ws(token)

    # Use a dictionary for MessagesState
    state = MessagesState()
    state['websocket'] = websocket  # Attach the WebSocket to the state

    try:
        while True:
            data = await state['websocket'].receive_text()
            logger.info(f"Received message: {data}")
            input_message = HumanMessage(content=data)

            if "messages" in state:
                state["messages"].append(input_message)
            else:
                state["messages"] = [input_message]

            thread = {"configurable": {"thread_id": str(uuid.uuid4())}}
            for event in app.stream(state, thread, stream_mode="values"):
                last_message = event["messages"][-1]

                if last_message.type == "ai":
                    await state['websocket'].send_text(last_message.content)
                    if "Please provide" in last_message.content:
                        while True:
                            user_input = await state['websocket'].receive_text()
                            logger.info(f"User provided input: {user_input}")
                            state["messages"].append(HumanMessage(content=user_input))
                            break
                        continue

    except WebSocketDisconnect:
        logger.info("WebSocket connection closed.")
    except Exception as e:
        logger.error(f"Error in WebSocket: {str(e)}")
        await state['websocket'].send_text("An error occurred. Please try again.")