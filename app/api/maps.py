import json
import math
import os
import re
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional

import googlemaps
from fastapi import APIRouter, Body, HTTPException, Depends
from googlemaps.directions import directions
from googlemaps.geocoding import geocode, reverse_geocode
from googlemaps.places import places_nearby, place
from langchain.chains.llm import LL<PERSON>hain
from langchain_core.prompts import PromptTemplate

from app.config.config import chatgpt_basic
from app.config.verify_token import verify_firebase_token
from app.core.tracing_setup import logger

gmaps = googlemaps.Client(key=os.getenv('GOOGLE_MAPS_API_KEY'))
router = APIRouter()
llm = chatgpt_basic
# llm= llm_fast
# Haversine formula to calculate distance between two coordinates in kilometers


def calculate_distance(coord1, coord2):
    try:
        logger.info(
            f"Calculating distance between coord1: {coord1} and coord2: {coord2}")
        lat1, lon1 = float(coord1[0]), float(coord1[1])
        lat2, lon2 = float(coord2[0]), float(coord2[1])
        R = 6371  # Radius of Earth in kilometers
        dlat = math.radians(lat2 - lat1)
        dlon = math.radians(lon2 - lon1)
        a = math.sin(dlat / 2) ** 2 + math.cos(math.radians(lat1)) * \
            math.cos(math.radians(lat2)) * math.sin(dlon / 2) ** 2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        distance = R * c
        logger.info(f"Calculated distance: {distance} km")
        return distance
    except Exception as e:
        logger.error(
            f"Error calculating distance with coord1: {coord1}, coord2: {coord2} - {e}")
        raise HTTPException(
            status_code=400, detail=f"Error calculating distance. Invalid coordinates: {coord1} and {coord2}. Error: {e}")

# Function to extract JSON from the LLM response


def extract_json(response_text):
    try:
        # Updated regex to match either a JSON object or a JSON array
        json_match = re.search(r'(\{.*\}|\[.*\])', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(1).replace('\n', '').replace('\\', '')
            return json.loads(json_str)
        return {}
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"Error parsing JSON: {e}")
        return {}

# Function to get coordinates from a place name


def get_coordinates_from_place(place_name: str) -> Optional[tuple]:
    try:
        geocode_result = geocode(gmaps, place_name)
        if geocode_result:
            location = geocode_result[0]['geometry']['location']
            return (location['lat'], location['lng'])
        logger.error(f"Geocode API returned no results for: {place_name}")
    except Exception as e:
        logger.error(f"Error fetching coordinates for {place_name}: {e}")
    return None

# Function to get city name from coordinates


def get_city_from_coordinates(latitude: float, longitude: float) -> Optional[str]:
    try:
        reverse_geocode_result = reverse_geocode(gmaps, (latitude, longitude))
        if reverse_geocode_result:
            for component in reverse_geocode_result[0]['address_components']:
                if 'locality' in component['types']:
                    return component['long_name']
            return reverse_geocode_result[0]['formatted_address']
        logger.error(
            f"Reverse geocoding API returned no results for coordinates: {latitude}, {longitude}")
    except Exception as e:
        logger.error(
            f"Error fetching city for coordinates ({latitude}, {longitude}): {e}")
    return None

# Function to get place details


def get_place_details(place_id: str, fields: List[str] = None) -> dict:
    try:
        place_details = place(gmaps, place_id=place_id, fields=fields)
        return place_details.get('result', {})
    except Exception as e:
        logger.error(f"Error fetching place details for {place_id}: {e}")
        return {}


# Updated function to assign exact start and end days to each city
def calculate_city_dates(city_days_json, start_date):
    city_dates = []
    current_date = datetime.fromisoformat(start_date)

    for city_day in city_days_json:
        city_start_date = current_date
        city_end_date = city_start_date + timedelta(days=city_day['days'] - 1)
        city_day['start_date'] = city_start_date.isoformat()
        city_day['end_date'] = city_end_date.isoformat()
        city_dates.append(city_day)
        # Update current date to the day after the city's end date
        current_date = city_end_date + timedelta(days=1)

    return city_dates

# Function to get directions between two locations


def get_directions_between_places(origin: str, destination: str, mode: str = 'driving'):
    return directions(gmaps, origin=origin, destination=destination, mode=mode)


@router.post("/itinerary/")
async def create_itinerary(
        interests: str = Body(...),
        start_location: str = Body(...),
        end_location: str = Body(...),
        places: list[str] = Body([]),
        order_matters: bool = Body(False),
        start_date: str = Body(None),
        end_date: str = Body(None),
        rent_car: bool = Body(True),
        travel_style: str = Body(...),
        travel_focus: list[str] = Body([]),
        decoded_token=Depends(verify_firebase_token),
):
    itinerary = await multi_step_itinerary_planner(interests, start_location, end_location, places, order_matters,
                                                   start_date, end_date, rent_car, travel_style, travel_focus)
    return {"itinerary": itinerary}


def determine_travel_mode(distance, rent_car):
    if distance < 2:
        return 'walking'
    elif 2 <= distance < 300:
        return 'driving' if rent_car else 'transit'
    else:
        return 'flight'


async def get_places_details(places, start_coords):
    search_locations = places if places else [start_coords]
    central_area_radius = 15000  # Adjust radius as needed
    all_places = []
    for loc in search_locations:
        loc_coords = get_coordinates_from_place(
            loc) if isinstance(loc, str) else loc
        if loc_coords:
            nearby_places = places_nearby(
                gmaps, location=loc_coords, radius=central_area_radius, type="tourist_attraction")
            all_places.extend(nearby_places['results'])
    return all_places


async def multi_step_itinerary_planner(interests, start_location, end_location, places, order_matters, start_date,
                                       end_date, rent_car, travel_style, travel_focus):
    try:
        start_coords = get_coordinates_from_place(start_location)
        if not start_coords:
            raise HTTPException(
                status_code=400, detail=f"Invalid start location: {start_location}")

        total_days = calculate_total_days(start_date, end_date)

        city_days_json = await distribute_days_to_cities(interests, total_days, start_date, end_date, places, order_matters)

        city_days = calculate_city_dates(city_days_json, start_date)
        recommended_places = await recommend_places_for_cities(city_days, interests, travel_style, travel_focus)

        full_itinerary = await build_full_itinerary(recommended_places, start_coords, start_location, end_location, rent_car, start_date, end_date)

        return full_itinerary
    except Exception as e:
        logger.error(f"Error in multi_step_itinerary_planner: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def calculate_total_days(start_date: str, end_date: str) -> int:
    try:
        logger.info(
            f"Calculating total days between start_date: {start_date} and end_date: {end_date}")
        start_date_obj = datetime.strptime(start_date, "%Y-%m-%dT%H:%M:%S.%f")
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%dT%H:%M:%S.%f")
        # Inclusive of  start
        total_days = (end_date_obj - start_date_obj).days
        logger.info(f"Total days calculated: {total_days}")
        return total_days
    except Exception as e:
        logger.error(
            f"Error calculating total days with start_date: {start_date}, end_date: {end_date} - {e}")
        raise HTTPException(
            status_code=400, detail=f"Invalid date format or values for start_date: {start_date} and end_date: {end_date}. Error: {e}")


async def distribute_days_to_cities(interests, total_days, start_date, end_date, places, order_matters):
    order_str = "The order to visit these cities does matter. You can not change the order of the cities." if order_matters else "The order to visit these cities does not matter. You can change the order of the cities."
    days_prompt_template = PromptTemplate.from_template(
        """
        Given the total trip duration of {total_days} days from {start_date} to {end_date} and the cities: {places}, 
        {order_str}
        {end_date} is not included, so do not assign days to this date.
        suggest a distribution of days to spend in each city based on the interests: {interests}.
        Avoid 0 days stay in each city,
        Provide the output in JSON format ONLY, with no extra text or symbols:
        [
            {{
                "city": "<city name>",
                "days": <number of days to stay in this city>
            }},
            ...
        ]
        """
    )

    days_chain = LLMChain(prompt=days_prompt_template, llm=llm)

    total_days_input = {
        'total_days': total_days,
        'start_date': start_date,
        'end_date': end_date,
        'places': places,
        'interests': interests,
        'order_str': order_str
    }
    days_result = await days_chain.ainvoke(total_days_input)
    return extract_json(days_result["text"])


async def recommend_places_for_cities(city_days_json, interests, travel_style, travel_focus):
    recommended_places = []
    place_chain = LLMChain(prompt=build_place_recommendation_template(
    ), llm=llm)

    for city_day in city_days_json:
        city_places = await get_places_details([city_day['city']], None)
        city_input = {
            'city': city_day['city'],
            'interests': interests,
            'days': city_day['days'],
            'places': city_places,
            'travel_style': travel_style,
            'travel_focus': travel_focus,
            'start_date': city_day['start_date'],
            'end_date': city_day['end_date'],
        }
        places_result = await place_chain.ainvoke(city_input)
        city_places_json = extract_json(places_result['text'])
        for place in city_places_json:
            latitude = float(place['latitude'])
            longitude = float(place['longitude'])
            city_name = get_city_from_coordinates(latitude, longitude)
            place['city'] = city_name if city_name else "Unknown City"

        recommended_places.append({
            'city': city_day['city'],
            'days': city_day['days'],
            'places': city_places_json,
            'start_date': city_day['start_date'],
            'end_date': city_day['end_date'],
        })
    return recommended_places


def build_place_recommendation_template():
    return PromptTemplate.from_template(
        """
        You are a travel expert.
        For the city: {city} and the interests: {interests}, 
        suggest the best places to visit within {days} days from these places {places}.
        this travel style is {travel_style}.
        this travel focus these types of places: {travel_focus}.
        one hotel per day. 3 meals at most per day at meal time
        time spend in this city is between {start_date} and {end_date}.
        Make sure there are enough activities to cover the trip time and also include main local point of interests.
        Distribute the activities evenly among the dys of visit.
        Include estimated visit duration and whether the place is open at the estimated visit time.
        Provide the output in JSON format ONLY, with no extra text or symbols:
        [
            {{
                "name": "<place name>",
                "description": "<description>",
                "latitude": "<latitude>",
                "longitude": "<longitude>",
                "estimated_visit_duration": "<duration in minutes>",
                "place_id": "<place_id of this place>",
                "photos": "<a list of photo_reference in photos of this place>",
            }},
            ...
        ]
        """
    )


async def build_full_itinerary(recommended_places, start_coords, start_location, end_location, rent_car, start_date,
                               end_date):
    full_itinerary = []
    current_time = datetime.fromisoformat(start_date)

    # Start location
    start_location_coordinates = get_coordinates_from_place(start_location)
    full_itinerary.append({
        "name": "Start Location",
        "description": f"Start of the itinerary at {start_location}",
        "latitude": start_location_coordinates[0],
        "longitude": start_location_coordinates[1],
        "visit_time": current_time.isoformat(),
        "start_date": start_date,
        "end_date": start_date,
        "duration": 0
    })

    # Add travel information from start location to the first city
    if recommended_places:
        await add_travel_to_next_city(full_itinerary, {"city": start_location, "end_date": start_date},
                                      recommended_places[0], rent_car, current_time)

    # Add cities and places
    for i, city_info in enumerate(recommended_places):
        full_itinerary.append(city_info)
        await add_places_to_itinerary(full_itinerary, city_info, current_time, rent_car)

        if i < len(recommended_places) - 1:
            await add_travel_to_next_city(full_itinerary, city_info, recommended_places[i + 1], rent_car, current_time)

    # Add travel information from the last city to the end location
    if recommended_places:
        await add_travel_to_next_city(full_itinerary, recommended_places[-1],
                                      {"city": end_location, "start_date": end_date}, rent_car, current_time)

    # End location
    end_location_coordinates = get_coordinates_from_place(end_location)
    full_itinerary.append({
        "name": "End Location",
        "description": f"End of the itinerary at {end_location}",
        "latitude": end_location_coordinates[0],
        "longitude": end_location_coordinates[1],
        "visit_time": current_time.isoformat(),
        "start_date": end_date,
        "end_date": end_date,
        "duration": 0
    })

    logger.info("full itinerary: " + str(full_itinerary))
    return full_itinerary


def nearest_neighbor_search(places, start_coords):
    """
    Perform nearest neighbor search to optimize the order of places visited.
    Args:
        places (list): A list of places with their coordinates.
        start_coords (tuple): The starting coordinates (latitude, longitude).
    Returns:
        list: The optimized order of places to visit.
    """
    unvisited_places = places[:]
    current_coords = start_coords
    visit_order = []

    while unvisited_places:
        # Find the nearest place
        nearest_place = min(
            unvisited_places,
            key=lambda place: calculate_distance(
                current_coords, (place['latitude'], place['longitude']))
        )
        visit_order.append(nearest_place)
        unvisited_places.remove(nearest_place)
        current_coords = (
            nearest_place['latitude'], nearest_place['longitude'])

    return visit_order


async def add_places_to_itinerary(full_itinerary, city_info, current_time, rent_car):
    """
    Add places to the itinerary using optimized order and LLM to set visit time.
    Args:
        full_itinerary (list): The itinerary to be updated.
        city_info (dict): Information about the city including places to visit.
        current_time (datetime): The current time in the itinerary.
        rent_car (bool): Whether the user has rented a car.
    """
    # Optimize the order of places to visit using nearest neighbor search
    start_coords = get_coordinates_from_place(city_info['city'])
    optimized_places = nearest_neighbor_search(
        city_info['places'], start_coords)

    # Prepare input for LLM to process all places at once
    places_list = []
    for place in optimized_places:
        visit_duration = timedelta(minutes=int(
            place['estimated_visit_duration']))
        places_list.append({
            'place_name': place['name'],
            'duration': visit_duration.total_seconds() // 60
        })

    # Use LLM to assign start_datetime and end_datetime for all places
    time_chain = LLMChain(prompt=build_time_allocation_template(), llm=llm)
    time_input = {
        'city': city_info['city'],
        'places': places_list,
        'start_time': current_time.isoformat(),
        'start_date': city_info['start_date'],
        'end_date': city_info['end_date']
    }
    time_result = await time_chain.ainvoke(time_input)
    # Extract list of start/end times
    time_data = extract_json(time_result['text'])

    # Iterate over the places and update the itinerary
    for j, place in enumerate(optimized_places):
        # Assign the start and end times from the LLM result
        place['start_datetime'] = time_data[j]['start_datetime']
        place['end_datetime'] = time_data[j]['end_datetime']

        # Add additional place details
        visit_duration = timedelta(minutes=int(
            place['estimated_visit_duration']))
        place['visit_time'] = current_time.isoformat()
        place['formatted_address'] = get_place_details(place['place_id'], ["formatted_address"]).get(
            'formatted_address', "")
        place['duration'] = visit_duration.total_seconds() // 60
        full_itinerary.append(place)

        # # Add directions to the next place
        # if j < len(optimized_places) - 1:
        #     await add_directions_to_next_place(full_itinerary, place, optimized_places[j + 1], rent_car)

        current_time += visit_duration


def build_time_allocation_template():
    """
    Build the template for LLM to assign start_datetime and end_datetime for a list of places.
    Returns:
        PromptTemplate: The prompt template for time allocation.
    """
    return PromptTemplate.from_template(
        """
        You are an expert in travel planning.
        For the city: {city}, suggest the start and end times for visiting the following places.
        The user will visit each place for the duration provided.
        Please suggest start time and end time so that visits are evenly distributed among the days.
        All times must be between {start_date} and {end_date} and in time order, with no overlaps.
        Return the result as a JSON list in this format:
        [
            {{
                "place_name": "<place_name>",
                "start_datetime": "<start time in ISO format>",
                "end_datetime": "<end time in ISO format>"
            }},
            ...
        ]
        Place data: {places}
        """
    )


async def check_place_opening_hours(place):
    place_details = get_place_details(
        place['place_id'], ["opening_hours"]).get('opening_hours', {})
    is_open = place_details.get('open_now', True)
    if not is_open:
        logger.info(
            f"Place {place['name']} is not open at the estimated visit time.")
    return is_open


async def add_directions_to_next_place(full_itinerary, current_place, next_place, rent_car):
    origin_coords = (float(current_place['latitude']), float(
        current_place['longitude']))
    destination_coords = (
        float(next_place['latitude']), float(next_place['longitude']))
    distance = calculate_distance(origin_coords, destination_coords)

    travel_mode = determine_travel_mode(distance, rent_car)

    if travel_mode != 'flight':
        origin = f"{current_place['latitude']},{current_place['longitude']}"
        destination = f"{next_place['latitude']},{next_place['longitude']}"
        directions_result = get_directions_between_places(
            origin, destination, mode=travel_mode)
        current_place['next_direction'] = {
            "mode": travel_mode,
            "steps": directions_result[0]['legs'][0]['steps'] if directions_result else []
        }
    else:
        current_place['next_direction'] = {
            "mode": "flight",
            "details": f"Flight required from {current_place['name']} to {next_place['name']}"
        }


async def add_travel_to_next_city(full_itinerary, current_city_info, next_city_info, rent_car, current_time):
    origin_coords = get_coordinates_from_place(current_city_info['city'])
    destination_coords = get_coordinates_from_place(next_city_info['city'])

    if origin_coords and destination_coords:
        distance = calculate_distance(origin_coords, destination_coords)
        travel_mode = determine_travel_mode(distance, rent_car)

        if travel_mode == 'flight':
            # Calculate flight time: distance / flight_speed + 4 hours for airport processes
            flight_speed = 900  # Typical speed of commercial flights in km/h
            travel_time = timedelta(hours=distance / flight_speed + 4)
        else:
            # Driving or transit time estimation
            # Assuming 50 km/h for driving
            travel_time = timedelta(minutes=distance / 50 * 60)

        travel_info = {
            "mode": travel_mode,
            "from": current_city_info['city'],
            "to": next_city_info['city'],
            "latitude": destination_coords[0],
            "longitude": destination_coords[1],
            "duration": travel_time.total_seconds() // 60,  # Travel time in minutes
            "start_datetime": current_time.isoformat(),
            "start_date": current_city_info["end_date"],
            "end_date": next_city_info["start_date"],
        }
        full_itinerary.append({"travel": travel_info})
        current_time += travel_time
