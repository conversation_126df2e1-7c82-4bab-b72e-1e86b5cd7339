import asyncio
import json
from copy import deepcopy
from dataclasses import asdict
from typing import Dict, List, Union

import janus
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
from lagent.schema import AgentStatusCode
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse
from starlette.websockets import WebSocketState

from app.config.verify_token import verify_firebase_token_ws
from app.core.tracing_setup import logger
from app.frameworks.mindsearch.agent import init_agent

router = APIRouter()


class GenerationParams(BaseModel):
    inputs: Union[str, List[Dict]]
    agent_cfg: Dict = dict()
    lang: str = 'en'  # 'cn'
    model_format: str = "gpt4"  # 'internlm_server'


async def shared_generate_logic(agent, inputs):
    try:
        queue = janus.Queue()

        def sync_generator_wrapper():
            try:
                for response in agent.stream_chat(inputs):
                    queue.sync_q.put(response)
            except Exception as e:
                logger.exception(f'Exception in sync_generator_wrapper: {e}')
            finally:
                queue.sync_q.put(None)

        async def async_generator_wrapper():
            loop = asyncio.get_event_loop()
            loop.run_in_executor(None, sync_generator_wrapper)
            while True:
                response = await queue.async_q.get()
                if response is None:
                    break
                yield response
                if not isinstance(response, tuple) and response.state == AgentStatusCode.END:
                    break

        async for response in async_generator_wrapper():
            if isinstance(response, tuple):
                agent_return, node_name = response
            else:
                agent_return = response
                node_name = None

            origin_adj = deepcopy(agent_return.adjacency_list)
            adjacency_list = convert_adjacency_to_tree(
                agent_return.adjacency_list, 'root')
            assert adjacency_list['name'] == 'root' and 'children' in adjacency_list
            agent_return.adjacency_list = adjacency_list['children']
            agent_return = asdict(agent_return)
            agent_return['adj'] = origin_adj
            response_json = json.dumps(
                dict(response=agent_return, current_node=node_name), ensure_ascii=False)
            yield response_json
    except Exception as exc:
        msg = 'An error occurred while generating the response.'
        logger.exception(msg)
        response_json = json.dumps(
            dict(error=dict(msg=msg, details=str(exc))),
            ensure_ascii=False)
        yield {'data': response_json}
    finally:
        queue.close()
        await queue.wait_closed()


def convert_adjacency_to_tree(adjacency_input, root_name):
    def build_tree(node_name):
        node = {'name': node_name, 'children': []}
        if node_name in adjacency_input:
            for child in adjacency_input[node_name]:
                child_node = build_tree(child['name'])
                child_node['state'] = child['state']
                child_node['id'] = child['id']
                node['children'].append(child_node)
        return node

    return build_tree(root_name)


@router.post('/mindsearch/solve')
async def run(request: GenerationParams):
    agent = init_agent(lang=request.lang, model_format=request.model_format)
    inputs = request.inputs

    async def sse_generator():
        chunk_last = None
        chunk_count = 0

        async for response_json in shared_generate_logic(agent, inputs):
            chunk_last=response_json
            chunk_count += 1

            # Send batch of 10 chunks
            if chunk_count == 10:
                yield {"data": response_json}
                chunk_last = None
                chunk_count = 0

        # Send any remaining chunks
        if chunk_last:
            yield {"data": chunk_last}

        yield {"data": "<END_OF_AGENT>"}

    return EventSourceResponse(sse_generator())


@router.websocket("/ws/mindsearch/solve")
async def websocket_endpoint(websocket: WebSocket, token: str = Query(...)):
    await websocket.accept()

    # Verify the Firebase token
    decoded_token = await verify_firebase_token_ws(token)
    try:
        data = await websocket.receive_text()
        request = GenerationParams(**json.loads(data))
        agent = init_agent(lang=request.lang,
                           model_format=request.model_format)
        inputs = request.inputs

        chunk_last = None
        chunk_count = 0

        async for response_json in shared_generate_logic(agent, inputs):
            chunk_last=response_json
            chunk_count += 1

            # Send batch of 10 chunks
            if chunk_count == 10:
                await websocket.send_text(response_json)
                chunk_last = None
                chunk_count = 0

        # Send any remaining chunks
        if chunk_last:
            await websocket.send_text(chunk_last)

        #  break the json response
        # await websocket.send_text("<END_OF_AGENT>")

    except WebSocketDisconnect:
        logger.info("WebSocket disconnected")
    except Exception as exc:
        logger.exception("An error occurred during WebSocket communication.")
        if websocket.client_state == WebSocketState.CONNECTED:
            try:
                await websocket.send_text(json.dumps({"error": str(exc)}))
            except RuntimeError:
                logger.error("Failed to send error message; WebSocket already closed.")
    finally:
        if websocket.client_state != WebSocketState.DISCONNECTED:
            await websocket.close()