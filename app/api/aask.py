import json
from datetime import datetime
import uuid
from traceloop.sdk.decorators import workflow
from fastapi import Depends, APIRouter, WebSocket, Query
from langchain.chains.base import Chain
from langchain.memory import ConversationBufferMemory
from langchain_chroma import Chroma
from langchain_core.chat_history import BaseChatMessageHistory
from langchain_core.memory import BaseMemory
from langchain_openai import ChatOpenAI
from starlette.websockets import WebSocketDisconnect

from app.config.basic_config import default_model, embed_func
from app.config.verify_token import verify_firebase_token_ws
from app.core.db_config import get_db
from app.core.tracing_setup import logger
from app.core.vector_db_config import prepare_retriever, get_vector_db_crud_static
from app.models import bot_chat_message
from app.models.model import AaskRequestData
from app.utils import util
from app.utils.util import (
    get_conversation_history,
    get_bot_property,
    find_last_final_answer,
)
from app.utils.util_chain import make_agent

router = APIRouter()


@workflow(name="aask")
@router.websocket("/ws/aask")
async def aask_websocket_endpoint(
    websocket: WebSocket,
    db=Depends(get_db),
    diogenes_search=Depends(get_vector_db_crud_static),
    token: str = Query(...),
):
    await websocket.accept()

    # Verify the Firebase token
    decoded_token = await verify_firebase_token_ws(token)
    session_id: str = str(uuid.uuid4())
    user_id = decoded_token["user_id"]
    try:
        # Receive data from the WebSocket connection
        # Receive and log raw data to see what’s coming in
        raw_data = await websocket.receive_text()

        # Attempt to parse it as JSON
        data = json.loads(raw_data)
        logger.info(f"Parsed JSON data: {data}")

        aask_request_data: AaskRequestData = AaskRequestData.model_validate_json(
            raw_data
        )

        # Ensure the user index exists
        diogenes_search.create_user_index(aask_request_data.user_id)
        # Retrieve bot's information from Firestore
        bot_ref = db.collection("bots").document(aask_request_data.bot_id)

        bot_snapshot = await bot_ref.get()
        bot = bot_snapshot.to_dict()
        if bot is None:
            logger.error("bot is None, please check")
            await websocket.send_json({"error": "bot is None"})
            await websocket.close()
            return

        bot_chat_history: BaseChatMessageHistory = await get_conversation_history(
            db,
            get_bot_property(bot, "agentName", "Agent"),
            aask_request_data.conversation_id,
        )

        retriever = await prepare_retriever(
            aask_request_data.query, aask_request_data.bot_id, aask_request_data.user_id
        )

        chromavectorstore = Chroma(
            embedding_function=embed_func,
            persist_directory="./chromadb/chromadb_"
            + aask_request_data.user_id
            + "_"
            + aask_request_data.conversation_id,
        )

        chatgpt_no_web_response: ChatOpenAI = ChatOpenAI(
            model=default_model,
            streaming=True,
            callbacks=[],
            verbose=True,
            temperature=0,
        )

        memory: BaseMemory = ConversationBufferMemory(
            llm=chatgpt_no_web_response,
            memory_key="chat_history",
            input_key="question",
            output_key="answer",
            chat_memory=bot_chat_history,
            return_messages=True,
        )

        mrkl: Chain = (
            await make_agent(retriever, chromavectorstore, memory)
        ).with_config(
            {"run_name": "Agent_aask", "session_id": session_id, "user_id": user_id}
        )

        total_response = ""

        async for event in mrkl.astream_events(
            {
                "input": aask_request_data.query,
                "question": aask_request_data.query,
            },
            version="v2",
            chat_history=bot_chat_history.messages,
        ):
            kind = event["event"]
            if kind == "on_chain_start":
                if (
                    event["name"] == "Agent_aask"
                ):  # Was assigned when creating the agent with `.with_config({"run_name": "Agent"})`
                    logger.info(
                        f"Starting agent: {event['name']} with input: {event['data'].get('input')}"
                    )
            elif kind == "on_chain_end":
                if (
                    event["name"] == "Agent_aask"
                ):  # Was assigned when creating the agent with `.with_config({"run_name": "Agent"})`
                    logger.info("--")
                    logger.info(
                        f"Done agent: {event['name']} with output: {event['data'].get('output')['answer']}"
                    )
            elif kind == "on_chat_model_stream":
                token = event["data"]["chunk"].content
                if token:
                    total_response += token
                    await websocket.send_json({"event": "aask", "data": token})
            elif kind == "on_tool_start":
                logger.info("--")
                logger.info(
                    f"Starting tool: {event['name']} with inputs: {event['data'].get('input')}"
                )
            elif kind == "on_tool_end":
                logger.info(f"Done tool: {event['name']}")
                logger.info(f"Tool output was: {event['data'].get('output')}")
                logger.info("--")

        await websocket.send_json({"event": "aask", "data": "<END_OF_AGENT>"})

        total_response = find_last_final_answer(total_response).strip()
        await util.add_message_to_conversation(
            db,
            aask_request_data.conversation_id,
            bot_chat_message.BotChatMessage(
                text="" if total_response is None else total_response,
                sender_id=aask_request_data.bot_id,
                timestamp=datetime.now(),
                is_bot=True,
            ).to_dict(),
        )

    except WebSocketDisconnect:
        logger.error("Client disconnected")
    except json.JSONDecodeError:
        logger.error("Received data is not valid JSON")
        await websocket.send_text("Invalid JSON format")

    except Exception as e:
        logger.error(f"Error in WebSocket connection: {e}")
        await websocket.send_text("Error processing request")
        await websocket.close()
