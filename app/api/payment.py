import os
import time
import uuid
from fastapi import APIRouter, Depends, HTTPException, Request
from loguru import logger
from pydantic import BaseModel
import stripe
import asyncio
from google.cloud import firestore
from google.api_core.exceptions import GoogleAPICallError, RetryError
from app.config.verify_token import verify_firebase_token
from app.core.db_config import get_db
from google.cloud.firestore import AsyncClient
from stripe import Webhook
from stripe.error import SignatureVerificationError
import firebase_admin
from firebase_admin import credentials, auth
import traceback

# Initialize Stripe with your secret key
stripe.api_key = os.getenv("STRIPE_SECRET")

router = APIRouter()


class BalanceRequest(BaseModel):
    type: str  # 'get_balance' or 'create_payment_intent'
    user_id: str  # User identifier
    currency: str = "usd"  # Currency, default is 'usd'
    amount: float = None  # Amount for payment intent (optional)
    payment_method_types: list[str] = [
        "card"
    ]  # List of payment method types, default is ['card']


class BalanceResponse(BaseModel):
    balance: float = None  # User's balance
    client_secret: str = None  # Client secret for payment intent


# Utility function to create a Stripe customer if needed
async def get_or_create_stripe_customer(
    user_id: str,
    user_email: str,
    db: firestore.AsyncClient,
    max_retries: int = 3,
    delay: float = 2.0,
    production: bool = os.getenv("PRODUCTION", "True").lower() == "true",
):
    """Fetch or create a Stripe customer using user_id as doc_id in Firestore and adding user name to Stripe."""
    retry_count = 0

    logger.info(
        f"Attempting to get or create Stripe customer for user {user_id} with email {user_email}."
    )

    while retry_count < max_retries:
        try:
            logger.info(
                f"Retry count {retry_count + 1}/{max_retries}. Checking if customer exists in Firestore."
            )

            # First, check Firestore to see if a customer with the same email exists
            customers_ref = db.collection("customers")
            query = customers_ref.where(
                field_path="email", op_string="==", value=user_email
            ).limit(1)

            # Use async for to consume the stream
            async for doc in query.stream():
                stripe_id = doc.to_dict().get("stripeId")
                if stripe_id:
                    # Check if the customer exists in live mode in Stripe
                    try:
                        # try to fix grpc asyncio threading issue by run on one thread: https://stackoverflow.com/questions/65945944/multi-thread-support-for-python-asyncio-grpc-clients
                        # Use asyncio.to_thread to run blocking I/O (Stripe API) in a separate thread
                        stripe_customer = await asyncio.to_thread(
                            stripe.Customer.retrieve, stripe_id
                        )
                        if getattr(stripe_customer, "deleted", False):
                            logger.info(
                                f"Stripe customer {stripe_id} is marked as deleted. Creating a new customer."
                            )
                            stripe_id = None
                        else:
                            logger.info(f"Found existing Stripe customer: {stripe_id}.")
                            # Check if the mode matches the production flag
                            if (production and not stripe_customer.livemode) or (
                                not production and stripe_customer.livemode
                            ):
                                logger.warning(
                                    f"Found Stripe customer {stripe_id} with email {user_email}, but the mode does not match: "
                                    f"production={production}, livemode={stripe_customer.livemode}. Treating as not existing."
                                )
                                stripe_id = None
                            else:
                                return stripe_id
                    except stripe.error.InvalidRequestError as e:
                        logger.warning(
                            f"Stripe customer {stripe_id} not found or invalid: {e}"
                        )
                        stripe_id = None
                    except Exception as e:
                        logger.error(f"Error checking customer on Stripe: {str(e)}")
                        raise HTTPException(
                            status_code=500, detail="Error checking Stripe customer"
                        )

            logger.info(
                f"No existing Stripe customer found in Firestore for email {user_email}. Checking Stripe directly."
            )

            # If no customer exists in Firestore, check Stripe for an existing customer with the same email
            stripe_customers = await asyncio.to_thread(
                stripe.Customer.list, email=user_email
            )
            matched_customer = None

            # Loop through all stripe customers and find the one with the correct mode
            for customer in stripe_customers.data:
                if (production and customer.livemode) or (
                    not production and not customer.livemode
                ):
                    matched_customer = customer
                    break

            if matched_customer:
                stripe_id = matched_customer.id
                logger.info(
                    f"Found existing Stripe customer {stripe_id} by email lookup. Saving to Firestore."
                )
                # Save this to Firestore for future reference
                new_customer_data = {
                    "email": user_email,
                    "stripeId": stripe_id,
                    "stripeLink": f"https://dashboard.stripe.com/customers/{stripe_id}",
                }
                # Add the customer to the 'customers' collection with user_id as the doc_id
                await customers_ref.document(user_id).set(new_customer_data)
                return stripe_id

            logger.info(
                f"No suitable customer found on Stripe for email {user_email}. Creating a new customer."
            )

            # If no suitable customer exists on Stripe, create a new Stripe customer
            stripe_customer = await asyncio.to_thread(
                stripe.Customer.create, email=user_email
            )

            logger.info(
                f"Created new Stripe customer: {stripe_customer.id}. Saving to Firestore."
            )

            # Save the new Stripe customer to Firestore using user_id as doc_id
            new_customer_data = {
                "email": user_email,
                "stripeId": stripe_customer.id,
                "stripeLink": f"https://dashboard.stripe.com/customers/{stripe_customer.id}",
            }

            # Add the customer to the 'customers' collection with user_id as the doc_id
            await customers_ref.document(user_id).set(new_customer_data)

            return stripe_customer.id

        except (GoogleAPICallError, RetryError) as e:
            retry_count += 1
            # Log the error and wait before retrying
            logger.error(
                f"Error occurred during Stripe customer creation: {str(e)}. Retrying... ({retry_count}/{max_retries})"
            )
            if retry_count >= max_retries:
                logger.error("Max retries reached. Failed to create Stripe customer.")
                raise HTTPException(
                    status_code=500,
                    detail="Failed to create Stripe customer after multiple retries.",
                )
            await asyncio.sleep(delay)  # Wait before retrying

    logger.error("Unknown error occurred when fetching or creating Stripe customer.")
    raise HTTPException(
        status_code=500,
        detail="Unknown error occurred when fetching or creating Stripe customer.",
    )


# Set your Stripe webhook secret here
WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_ENCRIPTION_SECRET")


@router.post("/webhooks")
async def stripe_webhook(request: Request, db=Depends(get_db)):
    payload = await request.body()
    sig_header = request.headers.get("Stripe-Signature")
    event = None

    try:
        # Verify webhook signature
        event = Webhook.construct_event(payload, sig_header, WEBHOOK_SECRET)
    except ValueError as e:
        # Invalid payload
        logger.error("Invalid payload")
        raise HTTPException(status_code=400, detail="Invalid payload")
    except SignatureVerificationError as e:
        # Invalid signature
        logger.error("Signature verification failed")
        raise HTTPException(status_code=400, detail="Invalid signature")

    # Handle the event
    if event["type"] == "payment_intent.succeeded":
        payment_intent = event["data"]["object"]
        user_id = payment_intent["metadata"]["user_id"]
        amount_received = (
            payment_intent["amount_received"] / 100
        )  # Convert cents to dollars

        # Get the user's email from Firebase Authentication if not available in payment_intent
        user_email = payment_intent.get("receipt_email")
        if not user_email:
            try:
                # Retrieve user info from Firebase Authentication using user_id
                user_record = auth.get_user(user_id)
                user_email = user_record.email
            except auth.UserNotFoundError:
                logger.warning(f"User {user_id} not found in Firebase Authentication.")
                user_email = None  # If no user found, set email to None

        # Update user's balance in Firestore using AsyncClient
        user_doc_ref = db.collection("users").document(user_id)
        await user_doc_ref.update(
            {
                "balance": firestore.Increment(
                    amount_received
                )  # Correct usage of Increment
            }
        )

        # * not invoice needed for now, receipt email is good enough
        # # Save the invoice to Firestore if email exists
        # if user_email:
        #     invoice_id = str(uuid.uuid4())
        #     await save_invoice_to_firestore(
        #         db,
        #         user_id,
        #         user_email,
        #         amount_received,
        #         payment_intent["currency"],
        #         invoice_id,
        #     )
        # else:
        #     logger.warning(f"User email not found for user {user_id}.")

        logger.info(f"Payment for user {user_id} succeeded. Balance updated.")

    elif event["type"] == "payment_intent.payment_failed":
        payment_intent = event["data"]["object"]
        user_id = payment_intent["metadata"]["user_id"]
        logger.error(
            f"Payment for user {user_id} failed. Payment ID: {payment_intent['id']}"
        )

    else:
        logger.warning(f"Unhandled event type: {event['type']}")

    return {"status": "success"}


async def save_invoice_to_firestore(
    db: AsyncClient,
    user_id: str,
    user_email: str,
    amount: float,
    currency: str,
    invoice_id: str,
):
    """Save the invoice to Firestore under the /invoices collection."""
    if not invoice_id:
        invoice_id = str(uuid.uuid4())  # Generate a unique invoice ID if not provided

    invoice_data = {
        "email": user_email,  # Customer's email address
        "items": [
            {
                "amount": int(
                    amount * 100
                ),  # Amount in cents (Stripe requires amounts in cents)
                "currency": currency,
                "quantity": 1,  # Optional, defaults to 1
                "description": "Diogenes AI Chatbot credits",  # Description of the item
            }
        ],
        "user_id": user_id,  # The Firebase user ID for ownership checking
        "timestamp": firestore.SERVER_TIMESTAMP,  # Automatically set the timestamp when the invoice is created
    }

    # Save the invoice document in the /invoices/{invoice_id} path
    await db.collection("invoices").document(invoice_id).set(invoice_data)
    logger.info(
        f"Invoice successfully saved to Firestore for user {user_id}, invoice ID {invoice_id}."
    )


@router.post("/payment", response_model=BalanceResponse)
async def handle_payment_request(
    request: BalanceRequest,
    db=Depends(get_db),
    decoded_token=Depends(verify_firebase_token),
):
    start_time = time.time()
    try:
        logger.info("Starting handle payment request")
        session_id = str(uuid.uuid4())
        user_id = decoded_token["user_id"]
        if user_id != request.user_id:
            raise HTTPException(status_code=403, detail="Unauthorized user")

            # Get the user's email from Firebase Authentication
        user_email = decoded_token.get("email")
        if not user_email:
            raise HTTPException(status_code=400, detail="Email not found in token")

        # Get or create a Stripe customer
        stripe_id = await get_or_create_stripe_customer(user_id, user_email, db)

        if request.type == "get_balance":
            # Fetch balance from Firestore
            user_doc = await db.collection("users").document(user_id).get()
            if user_doc.exists:
                balance = user_doc.to_dict().get(
                    "balance", 0.0
                )  # Default to 0 if balance is missing
                logger.info(
                    f"Payment request handle completed in {time.time() - start_time:.2f} seconds."
                )
                return BalanceResponse(balance=balance)
            else:
                raise HTTPException(status_code=404, detail="User not found")

        elif request.type == "create_payment_intent":
            if request.amount is None or request.amount <= 0:
                raise HTTPException(
                    status_code=400, detail="Amount must be greater than zero"
                )

            # Create a PaymentIntent using Stripe
            try:
                payment_intent = stripe.PaymentIntent.create(
                    amount=int(request.amount),  # Convert to cents
                    currency="usd",
                    customer=stripe_id,  # Associate with the existing or newly created Stripe customer
                    metadata={"user_id": user_id},
                )

                # Update balance in Firestore after successful payment
                user_doc = db.collection("users").document(user_id)
                await user_doc.update(
                    {
                        "balance": firestore.FieldValue.increment(
                            request.amount
                        )  # Add the amount to the balance
                    }
                )

                logger.info(
                    f"Payment request handle completed in {time.time() - start_time:.2f} seconds."
                )
                return BalanceResponse(client_secret=payment_intent.client_secret)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        else:
            raise HTTPException(status_code=400, detail="Invalid request type")

    except Exception as e:
        logger.error(f"Error handle payment request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")


# Endpoint to handle the creation of a payment sheet
@router.post("/create-payment-intent", response_model=BalanceResponse)
async def create_payment_intent(
    request: BalanceRequest,
    db=Depends(get_db),
    decoded_token=Depends(verify_firebase_token),
):
    start_time = time.time()
    try:
        logger.info("Starting create payment intent request")
        session_id = str(uuid.uuid4())
        user_id = decoded_token["user_id"]

        if user_id != request.user_id:
            raise HTTPException(status_code=403, detail="Unauthorized user")

            # Get the user's email from Firebase Authentication
        user_email = decoded_token.get("email")
        if not user_email:
            raise HTTPException(status_code=400, detail="Email not found in token")

        # Get or create a Stripe customer
        stripe_id = await get_or_create_stripe_customer(user_id, user_email, db)

        # Ensure amount is valid
        if request.amount is None or request.amount <= 0:
            raise HTTPException(
                status_code=400, detail="Amount must be greater than zero"
            )

        # Use the currency from the request (default to 'usd')
        currency = request.currency if request.currency else "usd"

        # Use the payment_method_types from the request if available, default to ['card']
        payment_method_types = (
            request.payment_method_types if request.payment_method_types else ["card"]
        )

        try:
            # Create a PaymentIntent with Stripe
            payment_intent = stripe.PaymentIntent.create(
                amount=int(request.amount),  # Convert to cents
                currency=currency,
                metadata={"user_id": user_id},
                receipt_email=user_email,
                customer=stripe_id,  # Associate with the existing or newly created Stripe customer
                payment_method_types=payment_method_types,  # Pass payment method types
            )

            # Respond with the client secret for the PaymentIntent
            logger.info(
                f"PaymentIntent created in {time.time() - start_time:.2f} seconds."
            )
            return BalanceResponse(client_secret=payment_intent.client_secret)

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    except Exception as e:
        logger.error(f"Error creating payment intent: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")


@router.post("/payment-sheet", response_model=BalanceResponse)
async def create_payment_sheet(
    request: BalanceRequest,
    db=Depends(get_db),
    decoded_token=Depends(verify_firebase_token),
):
    start_time = time.time()
    try:
        logger.info("Starting payment sheet request")
        session_id = str(uuid.uuid4())
        user_id = decoded_token["user_id"]
        if user_id != request.user_id:
            raise HTTPException(status_code=403, detail="Unauthorized user")

        # Ensure amount is valid
        if request.amount is None or request.amount <= 0:
            raise HTTPException(
                status_code=400, detail="Amount must be greater than zero"
            )

            # Get the user's email from Firebase Authentication
        user_email = decoded_token.get("email")
        if not user_email:
            raise HTTPException(status_code=400, detail="Email not found in token")
        # Get or create a Stripe customer
        stripe_id = await get_or_create_stripe_customer(user_id, user_email, db)

        # Create a PaymentIntent for PaymentSheet with multiple payment methods
        try:
            payment_intent = stripe.PaymentIntent.create(
                amount=int(request.amount),  # Convert to cents
                currency="usd",
                metadata={"user_id": user_id},
                customer=stripe_id,  # Associate with the existing or newly created Stripe customer
                payment_method_types=[
                    "card",
                    "google_pay",
                    "apple_pay",
                ],  # Enable Google Pay and Apple Pay
            )

            # Create an ephemeral key for the customer to use with the PaymentSheet
            ephemeral_key = stripe.EphemeralKey.create(
                customer=stripe_id,  # Use the user's Stripe customer ID
                api_version="2023-08-16",  # Use the Stripe API version
            )

            logger.info(
                f"PaymentSheet created in {time.time() - start_time:.2f} seconds."
            )
            return BalanceResponse(
                client_secret=payment_intent.client_secret,
                ephemeral_key=ephemeral_key.secret,
            )

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    except Exception as e:
        logger.error(f"Error handling payment sheet request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")
