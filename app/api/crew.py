import uuid

from fastapi import APIRouter, Depends
from starlette.requests import Request

from app.config.config import chatgpt_basic
from app.config.verify_token import verify_firebase_token
from app.models.agent_model import AgentData
from app.queue.crewai_tasks import async_kickoff_crew

router = APIRouter()



# Initialize Langchain with Groq models

# Template for agent and task creation
crewai_agent_prompt_template = """
You are an AI assistant. Generate agents and tasks for CrewAI to achieve this ultimate goal {ultimate_goal} based on the following requirements.

Current agents:
{agents}

Current tasks:
{tasks}

Follow this guidelines:

Agents:
- role: "Role description"
- goal: "Agent's goal"
- backstory: "Agent's backstory"
- tools: ["Tool names"]
- allow_delegation: "boolean value indicating whether this agent should finish task assigned or can delegate to other appropriate agent"

Tasks:
- description: "Task description"
- expected_output: "Expected task output"
- agent_role: "Role assigned to the task, should match one of the agent's role"


agent tools I have defined are here:
--------------------------------


tools=[search_tool, web_tool, scrape_tool,
              selenium_tool, csv_tool, json_tools,docx_tools,pdf_tool,txt_tool, xml_tool,youtube_tool]
----------------------------------------------------------------

Ensure to create the agents and tasks following this format if any of them are missing.
The total number of agents and tasks can be adjusted based on the complexity of the problem. 
The agents and tasks should work together to complete the ultimate goal.
We should have enough  details(url,links, etc) from useful sources(arxiv, wikipedia,etc)
each agent's backstory should contain reminder to be verbose, and detailed.
Remember you do not work on the ultimate goal.  Your whole output should be a json of the agents and tasks only.
"""


# Function to generate missing agents and tasks using Groq model
async def generate_agents_and_tasks(agent_data, ultimate_goal: str):
    agents = agent_data.get("agents", [])
    tasks = agent_data.get("tasks", [])

    # Convert lists to a string for formatting
    agents_str = "\n".join(str(agent) for agent in agents) if agents else "No agents currently."
    tasks_str = "\n".join(str(task) for task in tasks) if tasks else "No tasks currently."

    if not agents or not tasks:
        # Use Langchain and Groq model to generate agents and tasks
        llm = chatgpt_basic.with_structured_output(AgentData)
        prompt = crewai_agent_prompt_template.format(agents=agents_str, tasks=tasks_str, ultimate_goal=ultimate_goal)

        # Call the LLM to generate agents and tasks
        result: AgentData = await llm.ainvoke(prompt)

        # Extract and update the agent_data with new agents and tasks
        if not agents:
            agent_data["agents"] = result.model_dump()["agents"]
        if not tasks:
            agent_data["tasks"] = result.model_dump()["tasks"]

    return agent_data


# Updated /start_crew route
@router.post("/start_crew")
async def start_crewai_task(request: Request,
                            decoded_token=Depends(verify_firebase_token),
                            ) -> dict:
    """
    Starts a Celery task for running the CrewAI simulation.

    Args:
        request (Request): The incoming request containing the agent data.

    Returns:
        dict: A dictionary containing the task ID and session ID.
    """
    # Extract parameters from the incoming request
    agent_data = await request.json()
    ultimate_goal = agent_data.get('ultimate_goal')

    # Generate agents and tasks if missing
    agent_data = await generate_agents_and_tasks(agent_data, ultimate_goal)

    session_id: str = str(uuid.uuid4())

    # Trigger the Celery task
    task = async_kickoff_crew.delay(agent_data, session_id)

    # Return the task id for tracking the task progress
    return {"task_id": task.id, "session_id": session_id,"agents": agent_data["agents"],"tasks": agent_data["tasks"]}

# async def stream_task(task_id):
#     result = AsyncResult(task_id, app=celery)
#
#     while not result.ready():
#         # Check the task status and stream the output
#         if result.state == 'PROGRESS':
#             # Extract the metadata which includes step/task updates
#             step_output = result.info.get('output', 'No output yet')
#             if 'step' in result.info:
#                 yield f"data: Step {result.info['step']} output: {step_output}\n\n"
#             elif 'task' in result.info:
#                 yield f"data: Task '{result.info['task']}' output: {step_output}\n\n"
#         await asyncio.sleep(1)
#
#     # Task completed
#     if result.successful():
#         final_output = result.get()
#         yield f"data: Final Output: {final_output}\n\n"
#     else:
#         yield "data: Task failed\n\n"
#
#
# @router.get("/stream/crew/{task_id}")
# async def crew_stream(task_id: str):
#     return StreamingResponse(stream_task(task_id), media_type="text/event-stream")
