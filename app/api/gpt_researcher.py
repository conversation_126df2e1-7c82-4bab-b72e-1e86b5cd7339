import os
import uuid

from backend.websocket_manager import WebSocketManager, run_agent
from fastapi import WebSocket, WebSocketDisconnect, APIRouter, Query
from gpt_researcher.master.actions import stream_output
from gpt_researcher.utils.enum import Tone
from multi_agents import ChiefEditorA<PERSON>
from pydantic import BaseModel
from starlette.websockets import WebSocketState

from app.config.basic_config import default_model
from app.config.constants_gpt_researcher import gpt_researcher_config
from app.config.verify_token import verify_firebase_token_ws
from app.core.db_config import get_storage
from app.core.tracing_setup import logger
from app.utils.firebase_utils import upload_to_firebase, update_session_progress

router = APIRouter()

class ResearchRequest(BaseModel):
    task: str
    report_type: str
    agent: str

manager = WebSocketManager()

for key, value in gpt_researcher_config.items():
    if value is not None:
        os.environ[key] = str(value)

async def handle_websocket_request(websocket: WebSocket, user_id: str, session_id: str, request: dict):
    if request.get("type") == "start":
        await process_start_request(websocket, user_id, session_id, request)
    elif request.get("type") == "human_feedback":
        await process_human_feedback(request)
    else:
        logger.info("Error: not enough parameters provided.")

async def process_start_request(websocket: WebSocket, user_id: str, session_id: str, request: dict):
    task = request.get("query")
    await update_session_progress(user_id, session_id, task, "started", "gpt-researcher")
    await websocket.send_json({"type": "session_status", "status": "started"})

    report_type = request.get("report_type")
    source_urls = request.get("source_urls")
    tone = request.get("tone")
    headers:dict = request.get("headers", {})
    report_source = request.get("report_source")

    if task and report_type:
        tone_enum = Tone[tone]
        report = await generate_report(task, report_type, report_source, source_urls, tone_enum, websocket, headers, user_id, session_id)
        if report:
            await upload_report_to_firebase(report, user_id, session_id, websocket, task)

async def generate_report(task: str, report_type: str, report_source: str, source_urls: list, tone_enum: Tone, websocket: WebSocket, headers: dict, user_id: str, session_id: str):
    report = None
    try:
        report = await run_agent(task, report_type, report_source, source_urls, tone_enum, websocket, headers)
    except Exception as e:
        logger.error("Error generating report: " + str(e))
        await update_session_progress(user_id, session_id, task, "failed", "gpt-researcher")
        await websocket.send_json({"type": "session_status", "status": "failed"})
    return report

async def upload_report_to_firebase(report: str, user_id: str, session_id: str, websocket: WebSocket, task: str):
    if not isinstance(report, str):
        report = str(report)

    await update_session_progress(user_id, session_id, task, "completed", "gpt-researcher")
    await websocket.send_json({"type": "session_status", "status": "completed"})

    storage = get_storage()
    article_file_path = f"{user_id}/gpt-researcher-writing/{session_id}/storm_gen_article.txt"
    await upload_to_firebase("results/gpt-researcher/" + article_file_path, report, article_file_path, storage)

async def process_human_feedback(request: dict):
    feedback_data = request.get("content")
    logger.info(f"Received human feedback: {feedback_data}")

@router.websocket("/ws/gpt_researcher/generate")
async def gpt_researcher_websocket_endpoint(websocket: WebSocket, token: str = Query(...)):

    """
    This function handles WebSocket connections for the GPT Researcher service.
    It accepts incoming WebSocket connections, generates a unique session ID, and processes incoming requests.

    Parameters:
    websocket (WebSocket): The WebSocket connection object.

    Returns:
    None
    """
    await websocket.accept()

    # Verify the Firebase token
    decoded_token = await verify_firebase_token_ws(token)
    session_id = str(uuid.uuid4())
    await websocket.send_json({"type": "session_id", "session_id": session_id})

    try:
        while True:
            request = await websocket.receive_json()
            user_id = request.get("user_id")
            if not user_id:
                await websocket.send_text("user_id is required")
                continue

            os.environ["DOC_PATH"] = f"./output/gpt_researcher/{user_id}/{session_id}"
            await handle_websocket_request(websocket, user_id, session_id, request)

            await websocket.send_text("<END_OF_AGENT>")
    except WebSocketDisconnect:
        logger.info("WebSocket disconnected")
    except Exception as e:
        await websocket.send_text(f"Error: {str(e)}<END_OF_AGENT>")
    finally:
        if websocket.client_state == WebSocketState.CONNECTED:
            await websocket.close()
            logger.info("WebSocket closed successfully")

@router.websocket("/ws/gpt_researcher/multi_agents")
async def gpt_researcher_run_multi_agents(websocket: WebSocket, token: str = Query(...)):

    """
    This function handles WebSocket connections for the GPT Researcher service.
    It accepts incoming WebSocket connections, generates a unique session ID, and processes incoming requests.

    Parameters:
    websocket (WebSocket): The WebSocket connection object.

    Returns:
    None

    This function is responsible for managing the WebSocket connection for the GPT Researcher service. It accepts incoming WebSocket connections, generates a unique session ID, and processes incoming requests. The function uses the `WebSocketManager` class to handle the WebSocket connection and the `run_research_task` function to process the incoming requests.

    The function first accepts the incoming WebSocket connection and sends back a JSON message containing the session ID. It then enters a loop where it continuously waits for incoming JSON messages from the client. Each incoming message is checked to ensure it contains a 'user_id' field. If not, an error message is sent back to the client.

    If the 'user_id' field is present, the function proceeds to process the request. It first checks if the 'query' field is present in the request. If not, an error message is sent back to the client.

    The function then extracts the 'tone' field from the request, defaulting to 'Objective' if it's not present. It attempts to convert the 'tone' field to an instance of the `Tone` enum. If this fails, an error message is sent back to the client.

    The function then calls the `run_research_task` function to process the request. This function uses the `ChiefEditorAgent` class to generate a research report based on the provided task. The generated report is then sent back to the client.

    Finally, the function sends a special message "<END_OF_AGENT>" to indicate that the agent has finished processing the request.

    If the WebSocket connection is disconnected, the function logs an info message indicating that the connection has been closed. If any errors occur during the processing of the request, an error message is sent back to the client.

    When the function exits its loop, it closes the WebSocket connection and logs an info message indicating that the connection has been closed successfully.
    """
    await websocket.accept()

    # Verify the Firebase token
    decoded_token = await verify_firebase_token_ws(token)
    session_id = str(uuid.uuid4())
    await websocket.send_json({"type": "session_id", "session_id": session_id})

    try:
        while True:
            request = await websocket.receive_json()
            user_id = request.get("user_id")
            if not user_id:
                await websocket.send_text("user_id is required")
                continue

            query = request.get("query")
            if not query:
                await websocket.send_text("query is required")
                continue

            tone_str = request.get("tone", "Objective")
            tone = Tone.Objective
            try:
                tone = Tone[tone_str]
            except KeyError:
                await websocket.send_text(f"Invalid tone: {tone_str}")
                continue
            if request.get("type") == "start":
                report = await run_research_task(user_id=user_id, session_id=session_id, task=request, websocket=websocket, stream_output=stream_output, tone=tone)
            elif request.get("type") == "human_feedback":
                await process_human_feedback(request)

            await websocket.send_text("<END_OF_AGENT>")
    except WebSocketDisconnect:
        logger.info("WebSocket disconnected")
    except Exception as e:
        await websocket.send_text(f"Error: {str(e)}<END_OF_AGENT>")
    finally:
        if websocket.client_state == WebSocketState.CONNECTED:
            await websocket.close()
            logger.info("WebSocket closed successfully")

async def run_research_task(user_id: str, session_id: str, task, websocket: WebSocket = None, stream_output=None, tone=Tone.Objective, headers=None):
    task.setdefault("max_sections", 10)
    task.setdefault("publish_formats", {"markdown": True, "pdf": True, "docx": True})
    task.setdefault("include_human_feedback", False)
    task.setdefault("follow_guidelines", False)
    task.setdefault("model", default_model)
    task.setdefault("guidelines", [])
    task.setdefault("verbose", True)

    logger.info("Writing process started successfully.")
    topic = task.get("query")

    await update_session_progress(user_id, session_id, topic, "started", "gpt-researcher")
    await websocket.send_json({"type": "session_status", "status": "started"})

    chief_editor = ChiefEditorAgent(task, websocket, stream_output, tone, headers)
    research_report = ""

    try:
        research_report = await chief_editor.run_research_task(task_id=session_id)
    except Exception as e:
        logger.error("An error occurred during the writing process: %s", str(e))
        await update_session_progress(user_id, session_id, topic, "failed", "gpt-researcher", str(e))
        await websocket.send_json({"type": "session_status", "status": "failed"})
        return research_report

    if research_report["report"]:
        await upload_report_to_firebase(research_report["report"], user_id, session_id, websocket, topic)
    if websocket and stream_output:
        await stream_output(type="logs", output="research_report", content=research_report, websocket=websocket)

    return research_report
