
import os
import time
import uuid
from collections import defaultdict
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor
from tempfile import NamedTemporaryFile
from typing import Dict, Any, List

import cv2
import numpy as np
from fastapi import UploadFile, File, HTTPException, APIRouter, Depends
from fastapi.responses import JSONResponse
import torch
from ultralytics import YOLO
from ultralytics.engine.results import Results, Probs, Boxes, Masks
from ultralytics.utils.plotting import Annotator, colors

from app.config.verify_token import verify_firebase_token
from app.core.tracing_setup import logger
from app.model.yoloseg import YOLOv11Seg
from moviepy.editor import VideoFileClip

router = APIRouter()

# Ensure the outputs directory exists
if not os.path.exists("outputs"):
    os.makedirs("outputs", exist_ok=True)

# Load the YOLO models
# Load the YOLO models
current_dir = os.path.dirname(__file__)
logger.info(f"current_dir : {current_dir}")

# Navigate to the 'yolo' directory
yolo_dir = os.path.join(current_dir, '../yolo')  # Move up one directory and go to 'yolo'
logger.info(f"yolo_dir : {yolo_dir}")

# Load the segmentation model from the yolo directory
segmentation_model = YOLO(os.path.join(yolo_dir, 'yolo11x-seg.pt'))

# Define the path for the exported ONNX model
segmentation_model_onnx_path = os.path.join(yolo_dir, 'yolo11x-seg.onnx')  # Update the file name as needed

# Check if the ONNX model file already exists
if not os.path.exists(segmentation_model_onnx_path):
    # Export the model to ONNX format
    segmentation_model.export(format="onnx")
    logger.info(f"Model exported to {segmentation_model_onnx_path}")
else:
    logger.info(f"Model already exists at {segmentation_model_onnx_path}. Skipping export.")
logger.info(f"segmentation_model_onnx_path: {segmentation_model_onnx_path}")

# Construct the ONNX model path
segmentation_model_onnx_path = os.path.join(yolo_dir, 'yolo11x-seg.onnx')
logger.info(f"now segmentation_model_onnx_path: {segmentation_model_onnx_path}")
#   def __init__(self, onnx_model): need onnx_model file location in constructor
seg_model = YOLOv11Seg(segmentation_model_onnx_path)
detection_model = YOLO("app/yolo/yolo11x.pt")
classification_model = YOLO("app/yolo/yolo11x-cls.pt")
pose_model = YOLO("app/yolo/yolo11x-pose.pt")
obb_model = YOLO("app/yolo/yolo11x-obb.pt")

executor = ThreadPoolExecutor(max_workers=4)  # Limit to 4 concurrent threads


def serialize_masks(masks: Masks) -> List[Dict[str, Any]]:
    """Serialize mask boundaries."""
    if masks is None:
        return []

    return [{
        # "data": masks.numpy().tolist(),  # Convert the mask tensor to a list
        # "orig_shape": masks.orig_shape,
        "xy": [segment.tolist() for segment in masks.xy],  # Convert each segment to a list
        "xyn": [segment.tolist() for segment in masks.xyn]  # Convert each normalized segment to a list
    }]


def serialize_boxes(boxes:Boxes) -> List[Dict[str, Any]]:
    if boxes is None:
        return []
    return [{"xyxy": box.xyxy.tolist(), "conf": box.conf.item(), "cls": box.cls.item(), "is_track": box.is_track} for box in boxes]  # Simplified


def serialize_probs(probs) -> Dict[str, Any]:
    """Serialize classification probabilities."""
    if probs is None:
        return {}

    return {
        "top1": probs.top1,
        "top5": probs.top5,  # Already a list from the class property
        "top1conf": probs.top1conf.item() if isinstance(probs.top1conf, (np.ndarray, torch.Tensor)) else probs.top1conf,
        "top5conf": probs.top5conf.tolist() if isinstance(probs.top5conf, (np.ndarray, torch.Tensor)) else probs.top5conf
    }


def serialize_results(results: Results):
    """Serialize YOLO results into a JSON-serializable format."""
    return {
        # "orig_img": results.orig_img.tolist(),  # Convert to list for JSON serialization
        "orig_shape": results.orig_shape,
        "masks": serialize_masks(results.masks),
        "boxes": serialize_boxes(results.boxes),
        "probs": serialize_probs(results.probs) if results.probs is not None else None,
        "keypoints": results.keypoints.tolist() if results.keypoints is not None else None,
        "obb": results.obb.tolist() if results.obb is not None else None,
        "speed": results.speed,
        "names": results.names,
        "path": results.path
    }


def process_image(image: np.ndarray, model: YOLO) -> Results:
    results = model.predict(image,retina_masks=True)
    return results[0]  # Return the result object


async def save_image(image: np.ndarray, path: str, filename: str) -> str:
    output_path = os.path.join(path, filename)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    cv2.imwrite(output_path, image)
    return output_path


@router.post("/segment", dependencies=[Depends(verify_firebase_token)])
async def segment(file: UploadFile = File(...), decoded_token=Depends(verify_firebase_token)):
    if not file.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="Invalid image file type.")

    user_id: str = decoded_token["user_id"]
    session_id = str(uuid.uuid4())
    output_path = os.path.join("outputs", user_id, session_id)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    try:
        # Read the image
        image_data = await file.read()
        image = cv2.imdecode(np.frombuffer(image_data, np.uint8), cv2.IMREAD_COLOR)

        start_time = time.time()  # Start timing
        # Perform segmentation in a thread pool
        # Perform segmentation and visualization in a thread pool

        future = executor.submit(seg_model, image, 0.4, 0.45)  # Perform segmentation
        boxes, segments, masks = future.result()  # Get the segmentation results

        # Draw the boxes and segments on the original image
        future_visualize = executor.submit(seg_model.draw_and_visualize, image, boxes, segments, False, False)
        seg_results=future_visualize.result()  # Wait for visualization to complete
        # Save the segmented image with boxes and segments
        saved_path = await save_image(image, output_path, "segmented_output.jpg")
        #
        # future = executor.submit(process_image, image, segmentation_model)
        #
        # # Get the segmentation results
        # segmentation_results: Results = future.result()
        elapsed_time = time.time() - start_time  # End timing

        # Save the segmented image

        # segmented_image = segmentation_results.plot()  # Assuming the plot() method is used to visualize results
        # saved_path = await save_image(segmented_image, output_path)

        logger.info(f"Segmentation completed for user {user_id}, session {session_id} in {elapsed_time:.2f}s")
        return JSONResponse(content={
            "message": "Segmentation completed",
            "output_path": saved_path,
            "elapsed_time": elapsed_time,
            # "results": segmentation_results.to_json()
            # "results": serialize_results(segmentation_results)
        })
    except Exception as e:
        logger.error(f"Error during segmentation: {str(e)}")
        raise HTTPException(status_code=500, detail="Segmentation failed.")


@router.post("/detect", dependencies=[Depends(verify_firebase_token)])
async def detect(file: UploadFile = File(...), decoded_token=Depends(verify_firebase_token)):
    if not file.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="Invalid image file type.")

    user_id: str = decoded_token["user_id"]
    session_id = str(uuid.uuid4())
    output_path = os.path.join("outputs", user_id, session_id)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    try:
        # Read the image
        image_data = await file.read()
        image = cv2.imdecode(np.frombuffer(image_data, np.uint8), cv2.IMREAD_COLOR)

        start_time = time.time()  # Start timing
        # Perform object detection in a thread pool
        future = executor.submit(process_image, image, detection_model)

        # Get the detection results
        detection_results: Results = future.result()
        elapsed_time = time.time() - start_time  # End timing

        # Save the detected image
        detected_image = detection_results.plot()  # Assuming the plot() method is used to visualize results
        saved_path = await save_image(detected_image, output_path, "detected_output.jpg")

        logger.info(f"Detection completed for user {user_id}, session {session_id} in {elapsed_time:.2f}s")
        return JSONResponse(content={
            "message": "Detection completed",
            "output_path": saved_path,
            "elapsed_time": elapsed_time,
            "results": detection_results.to_json()
            # "results": serialize_results(detection_results)
        })
    except Exception as e:
        logger.error(f"Error during detection: {str(e)}")
        raise HTTPException(status_code=500, detail="Detection failed.")


@router.post("/classify", dependencies=[Depends(verify_firebase_token)])
async def classify(file: UploadFile = File(...), decoded_token=Depends(verify_firebase_token)):
    if not file.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="Invalid image file type.")

    user_id: str = decoded_token["user_id"]
    session_id = str(uuid.uuid4())
    output_path = os.path.join("outputs", user_id, session_id)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    try:
        # Read the image
        image_data = await file.read()
        image = cv2.imdecode(np.frombuffer(image_data, np.uint8), cv2.IMREAD_COLOR)

        start_time = time.time()  # Start timing
        # Perform classification in a thread pool
        future = executor.submit(process_image, image, classification_model)

        # Get the classification results
        classification_results: Results = future.result()
        elapsed_time = time.time() - start_time  # End timing

        # Save the classified image
        classified_image = classification_results.plot()  # Assuming the plot() method is used to visualize results
        saved_path = await save_image(classified_image, output_path, "classified_output.jpg")

        logger.info(f"Classification completed for user {user_id}, session {session_id} in {elapsed_time:.2f}s")
        return JSONResponse(content={
            "message": "Classification completed",
            "output_path": saved_path,
            "elapsed_time": elapsed_time,
            "results": classification_results.to_json()
            # "results": serialize_results(classification_results)
        })
    except Exception as e:
        logger.error(f"Error during classification: {str(e)}")
        raise HTTPException(status_code=500, detail="Classification failed.")


@router.post("/pose", dependencies=[Depends(verify_firebase_token)])
async def pose(file: UploadFile = File(...), decoded_token=Depends(verify_firebase_token)):
    if not file.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="Invalid image file type.")

    user_id: str = decoded_token["user_id"]
    session_id = str(uuid.uuid4())
    output_path = os.path.join("outputs", user_id, session_id)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    try:
        # Read the image
        image_data = await file.read()
        image = cv2.imdecode(np.frombuffer(image_data, np.uint8), cv2.IMREAD_COLOR)

        start_time = time.time()  # Start timing
        # Perform pose estimation in a thread pool
        future = executor.submit(process_image, image, pose_model)

        # Get the pose results
        pose_results: Results = future.result()
        elapsed_time = time.time() - start_time  # End timing

        # Save the pose image
        pose_image = pose_results.plot()  # Assuming the plot() method is used to visualize results
        saved_path = await save_image(pose_image, output_path, "pose_output.jpg")

        logger.info(f"Pose estimation completed for user {user_id}, session {session_id} in {elapsed_time:.2f}s")
        return JSONResponse(content={
            "message": "Pose estimation completed",
            "output_path": saved_path,
            "elapsed_time": elapsed_time,
            "results": pose_results.to_json()
        })
    except Exception as e:
        logger.error(f"Error during pose estimation: {str(e)}")
        raise HTTPException(status_code=500, detail="Pose estimation failed.")

@router.post("/obb", dependencies=[Depends(verify_firebase_token)])
async def obb(file: UploadFile = File(...), decoded_token=Depends(verify_firebase_token)):
    if not file.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="Invalid image file type.")

    user_id: str = decoded_token["user_id"]
    session_id = str(uuid.uuid4())
    output_path = os.path.join("outputs", user_id, session_id)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    try:
        # Read the image
        image_data = await file.read()
        image = cv2.imdecode(np.frombuffer(image_data, np.uint8), cv2.IMREAD_COLOR)

        start_time = time.time()  # Start timing
        # Perform OBB detection in a thread pool
        future = executor.submit(process_image, image, obb_model)

        # Get the OBB results
        obb_results: Results = future.result()
        elapsed_time = time.time() - start_time  # End timing

        # Save the OBB image
        obb_image = obb_results.plot()  # Assuming the plot() method is used to visualize results
        saved_path = await save_image(obb_image, output_path, "obb_output.jpg")

        logger.info(f"OBB detection completed for user {user_id}, session {session_id} in {elapsed_time:.2f}s")
        return JSONResponse(content={
            "message": "OBB detection completed",
            "output_path": saved_path,
            "elapsed_time": elapsed_time,
            "results": obb_results.to_json()
        })
    except Exception as e:
        logger.error(f"Error during OBB detection: {str(e)}")
        raise HTTPException(status_code=500, detail="OBB detection failed.")


@router.post("/track", dependencies=[Depends(verify_firebase_token)])
async def track(file: UploadFile = File(...), decoded_token=Depends(verify_firebase_token)):
    # Dictionary to store tracking history with default empty lists
    track_history = defaultdict(lambda: [])

    # Check if the uploaded file is a valid video type (mp4 or mov)
    if not file.content_type.startswith("video/"):
        raise HTTPException(status_code=400, detail="Invalid video file type.")

    user_id: str = decoded_token["user_id"]
    session_id = str(uuid.uuid4())
    output_path = os.path.join("outputs", user_id, session_id, "tracked_output.mp4")
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Determine the suffix based on the original filename
    suffix = ".mp4" if file.filename.endswith(".mp4") else ".mov"

    # Create a temporary file to store the uploaded video
    with NamedTemporaryFile(delete=True, suffix=suffix) as temp_file:
        try:
            # Read the video file
            video_data = await file.read()
            temp_file.write(video_data)  # Write the uploaded data to the temp file
            temp_file.flush()  # Ensure the data is written
            # Extract audio from the original video using moviepy
            original_video = VideoFileClip(temp_file.name)
            audio = original_video.audio
            # Open the video capture using the temporary file path
            temp_file.seek(0)  # Go back to the start of the file
            video_capture = cv2.VideoCapture(temp_file.name)

            # Retrieve video properties
            fps = video_capture.get(cv2.CAP_PROP_FPS)
            w, h = int(video_capture.get(cv2.CAP_PROP_FRAME_WIDTH)), int(video_capture.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # Use 'mp4v' codec for MP4 files and also supported by MOV
            fourcc = cv2.VideoWriter.fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (w, h))

            start_time = time.time()  # Start timing

            while True:
                ret, frame = video_capture.read()
                if not ret:
                    break  # End of video

                # Create an annotator object to draw on the frame
                annotator = Annotator(frame, line_width=2)

                # Perform object tracking on the current frame
                results = segmentation_model.track(frame, persist=True)

                # Check if tracking IDs and masks are present in the results
                if results[0].boxes.id is not None and results[0].masks is not None:
                    masks = results[0].masks.xy  # Extract masks
                    track_ids = results[0].boxes.id.int().cpu().tolist()  # Extract tracking IDs

                    # Annotate each mask with its corresponding tracking ID and color
                    for mask, track_id in zip(masks, track_ids):
                        annotator.seg_bbox(mask=mask, mask_color=colors(track_id, True), label=str(track_id))

                # Write the annotated frame to the output video
                out.write(annotator.result())  # Use the annotated result frame

            video_capture.release()
            out.release()

            elapsed_time = time.time() - start_time  # End timing
            # Combine the processed video with the original audio
            processed_video = VideoFileClip(output_path)
            final_video = processed_video.set_audio(audio)
            final_video.write_videofile(output_path, codec='libx264', audio_codec='aac')

            logger.info(f"Tracking completed for user {user_id}, session {session_id} in {elapsed_time:.2f}s")
            return JSONResponse(content={
                "message": "Tracking completed",
                "output_path": output_path,
                "elapsed_time": elapsed_time
            })
        except Exception as e:
            logger.error(f"Error during tracking: {str(e)}")
            raise HTTPException(status_code=500, detail="Tracking failed.")