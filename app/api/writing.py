import uuid

from fastapi import Depends, APIRouter
from google.cloud.firestore import As<PERSON><PERSON><PERSON>
from google.cloud.storage import Client
from starlette.requests import Request

from app.config.verify_token import verify_firebase_token
from app.core.db_config import get_db, get_storage
from app.core.tracing_setup import logger
from app.models.model import WritingRequestData
from app.queue.writing_tasks import process_writing_task, async_process_writing_task

# Configure logging

router = APIRouter()


@router.post("/writing")
async def writing(request: Request,
                  decoded_token=Depends(verify_firebase_token),
                  db: AsyncClient = Depends(get_db),
                  storage: Client = Depends(get_storage), verify_firebase_token=None):
    """
    This function handles the writing process by either using LangChain or directly with STORMWikiRunner.
    It generates an article based on the user's topic and performs research, outline generation, article creation, and polishing.
    The function also handles the storage of the generated article and updates progress status in Firestore.

    Parameters:
    - request: The Starlette Request object containing the user's input.
    - db: The asynchronous Firestore client for database operations.
    - storage: The Google Cloud Firebase Storage client for file storage operations.

    Returns:
    - A dictionary containing the generated article.
    """

    logger.info("Starting writing process.")
    session_id: str = str(uuid.uuid4())
    data = await request.json()
    writing_request_data: WritingRequestData = WritingRequestData.model_validate(data)

    # Immediately return session ID
    # Convert the WritingRequestData object to a dictionary
    writing_request_data_dict = writing_request_data.model_dump()

    # Start the Celery task
    process_writing_task.delay(writing_request_data_dict, session_id, )
    return {"session_id": session_id}


@router.post("/writing_sync")
async def writing_sync(request: Request,
                        decoded_token=Depends(verify_firebase_token),
                       db: AsyncClient = Depends(get_db), storage: Client = Depends(get_storage)):
    """
    This function handles the writing process by either using LangChain or directly with STORMWikiRunner.
    It generates an article based on the user's topic and performs research, outline generation, article creation, and polishing.
    The function also handles the storage of the generated article and updates progress status in Firestore.

    Parameters:
    - request: The Starlette Request object containing the user's input.
    - db: The asynchronous Firestore client for database operations.
    - storage: The Google Cloud Firebase Storage client for file storage operations.

    Returns:
    - A dictionary containing the generated article.
    """

    logger.info("Starting writing process.")
    session_id: str = str(uuid.uuid4())
    data = await request.json()
    writing_request_data: WritingRequestData = WritingRequestData.model_validate(data)

    # Immediately return session ID
    # Convert the WritingRequestData object to a dictionary
    writing_request_data_dict = writing_request_data.model_dump()

    logger.info("Task started")

    # Wrap the entire async logic inside one asyncio.run() call
    await async_process_writing_task(writing_request_data_dict, session_id)
    return {"session_id": session_id}