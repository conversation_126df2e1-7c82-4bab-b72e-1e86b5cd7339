"""
Chat AI API module for handling OpenAI-based chat interactions.

This module provides API endpoints for text-based and WebSocket-based
chat interactions using OpenAI's language models.
"""

import urllib.parse
import base64
import httpx
import json
import uuid
from typing import Dict, Any, AsyncGenerator, List, Optional
from app.config.basic_config import BUCKET_NAME
from app.core.db_config import get_db, get_storage
from google.cloud.storage import Client
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query, Depends
from langchain.chains.base import Chain
from langchain_core.messages import ChatMessage, HumanMessage, AIMessage
from langchain_core.runnables import Runnable
from langchain_openai import Chat<PERSON>penAI
from pydantic import BaseModel, Field
from starlette.websockets import WebSocketState

from app.config.verify_token import verify_firebase_token_ws
from app.core.tracing_setup import logger
from app.utils.util_chain import prepare_agent_exe
from traceloop.sdk.decorators import workflow

router = APIRouter()


class ChatRequest(BaseModel):
    model: str
    max_tokens: int = Field(..., gt=0)
    messages: List[ChatMessage]
    temperature: float = Field(..., ge=0, le=1)
    file_list: Optional[List[str]] = Field(
        default_factory=list,
    )
    usage_type: Optional[str] = Field(default="chat")
    image_urls: Optional[List[str]] = Field(
        default_factory=list
    )  # New field for image URLs
    audio_urls: Optional[List[str]] = Field(
        default_factory=list
    )  # New field for audio URLs
    text_urls: Optional[List[str]] = Field(
        default_factory=list
    )  # New field for text URLs
    video_urls: Optional[List[str]] = Field(
        default_factory=list
    )  # New field for video URLs


async def fetch_data(
    agent_exe: Runnable, user_input, name
) -> AsyncGenerator[Dict[str, Any], None]:
    """
    Fetch and stream data from the agent executor.

    Args:
        agent_exe (Chain): The agent executor chain.
        user_input : The user's input question.

    Yields:
        Dict[str, Any]: Event data for streaming.
    """
    try:
        async for event in agent_exe.astream_events(user_input, version="v2"):
            kind = event["event"]
            if kind == "on_chain_start" and event["name"] == name:
                logger.info(
                    f"Starting agent: {event['name']} with input: {event['data'].get('input')}"
                )
            elif kind == "on_chain_end" and event["name"] == name:
                logger.info(
                    f"Done agent: {event['name']} with output: {event['data'].get('output')['output']}"
                )
            elif kind == "on_chat_model_stream":
                token = event["data"]["chunk"].content
                if token:
                    yield {"event": name, "data": token}
            elif kind == "on_tool_start":
                logger.info(
                    f"Starting tool: {event['name']} with inputs: {event['data'].get('input')}"
                )
            elif kind == "on_chat_model_end":
                logger.info(f'Token usage: {event["data"]["output"].usage_metadata}\n')
                yield {"event": "usage", "data": event["data"]["output"].usage_metadata}
            elif kind == "on_tool_end":
                logger.info(f"Done tool: {event['name']}")
                logger.info(f"Tool output was: {event['data'].get('output')}")

        yield {"event": name, "data": "<END_OF_AGENT>"}
    except Exception as e:
        logger.error(f"Error in fetch_data: {str(e)}")
        yield {"event": "error", "data": str(e)}


# Extract the blob name from the Firebase Storage URL
def extract_blob_name(image_url: str) -> str:
    # Parse the URL
    parsed_url = urllib.parse.urlparse(image_url)

    # Check if the path contains '/o/' and extract the blob name from the path
    if "/o/" in parsed_url.path:
        # Remove everything before and including '/o/' from the path
        encoded_path = parsed_url.path.split("/o/")[1]
        # Decode the URL-encoded path to get the blob name
        blob_name = urllib.parse.unquote(encoded_path)
        return blob_name
    else:
        logger.error(f"Invalid Firebase Storage URL: {image_url}")
        return ""  # Return empty if URL format is invalid


# Function to get signed URL
def get_signed_url(blob_name):
    storage = get_storage()
    bucket = storage.bucket(BUCKET_NAME)
    blob = bucket.blob(blob_name)

    try:
        blob.make_public()
        signed_url = blob.public_url
        # signed_url = blob.generate(expiration=3600)  # URL expires in 1 hour
        return signed_url
    except Exception as e:
        logger.error(f"Error generating signed URL for {blob_name}: {e}")
        return None


# Helper function to download the image and convert it to base64
async def get_image_base64(image_url: str) -> str:
    try:
        # Check if the URL has an access token (simple heuristic for example)
        if "token" not in image_url:
            # If no access token, generate a signed URL using the blob name (file path)
            blob_name = extract_blob_name(image_url)
            if not blob_name:
                logger.error(f"Invalid blob name extracted from {image_url}")
                return ""
            image_url = get_signed_url(blob_name)
        # Fetch image data using httpx
        async with httpx.AsyncClient() as client:
            response = await client.get(image_url)

            if response.status_code == 200:
                # Log content type for verification
                content_type = response.headers.get("Content-Type", "")
                logger.info(f"Content-Type of the image: {content_type}")

                # Check if the image is of a supported type (jpeg, png, gif)
                if (
                    "image/jpeg" in content_type
                    or "image/png" in content_type
                    or "image/gif" in content_type
                ):
                    base64_image = base64.b64encode(response.content).decode("utf-8")
                    return base64_image
                else:
                    logger.error(
                        f"Unsupported image format from {image_url}. Content-Type: {content_type}"
                    )
                    return ""
            else:
                logger.error(
                    f"Failed to fetch image from {image_url}. Status Code: {response.status_code}"
                )
                return ""
    except Exception as e:
        logger.error(f"Error fetching image from {image_url}: {e}")
        return ""


@workflow(name="askai")
@router.websocket("/ws/ask_openai")
async def ask_openai_websocket_endpoint(
    websocket: WebSocket,
    storage: Client = Depends(get_storage),
    token: str = Query(...),
):
    """
    Handle OpenAI chat requests via WebSocket and process multimodal input with image URLs.
    """
    await websocket.accept()
    session_id: str = str(uuid.uuid4())

    # Verify the Firebase token
    decoded_token = await verify_firebase_token_ws(token)
    user_id = decoded_token["user_id"]
    try:
        raw_data = await websocket.receive_text()

        # Decode the JSON string
        chat_request = json.loads(raw_data)
        logger.info(f"Parsed chat request: {chat_request}")

        # Extract image URLs from the request
        image_urls = chat_request.get("image_urls", [])

        # Process the image URLs into base64
        image_data_list = []
        for image_url in image_urls:
            # get_signed_url
            image_data_base64 = await get_image_base64(image_url)
            if image_data_base64:
                image_data_list.append(
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_data_base64}"
                        },
                    }
                )
            else:
                logger.error(f"Failed to convert image {image_url} to base64.")

        # Check if the model usage type is 'grammar' or 'tutor' and handle it
        # * when input image url is not zero, we should also use one request instead of agent executor since agent executor not support multimodal request for now
        if (
            chat_request["usage_type"] == "grammar"
            or chat_request["usage_type"] == "tutor"
            or len(image_data_list) != 0
        ):

            openai_chat = ChatOpenAI(
                model=chat_request["model"],
                max_tokens=chat_request["max_tokens"],
                temperature=chat_request["temperature"],
                streaming=True,
                verbose=True,
                stream_usage=True,
            ).with_config(
                {
                    "configurable": {
                        "run_name": "ask_openai",
                        "session_id": session_id,
                        "user_id": user_id,
                    }
                }
            )

            # user_input = str(
            #     [{msg["role"]: msg["content"]} for msg in chat_request["messages"]]
            # )

            # Combine the image data with the user input and send to model
            message = HumanMessage(
                content=[
                    {"type": "text", "text": chat_request["messages"][-1]["content"]},
                    *image_data_list,  # Add the image data as part of the message
                ]
            )

            chat_history = []
            if len(image_data_list) != 0:

                for msg in chat_request["messages"]:
                    if msg["role"] == "user":
                        chat_history.append(HumanMessage(content=msg["content"]))
                    else:
                        chat_history.append(AIMessage(content=msg["content"]))

            # Stream response from ChatOpenAI
            async for event in fetch_data(
                openai_chat, chat_history + [message], "ask_openai"
            ):
                await websocket.send_json(event)
        else:
            # Handle other usage types (e.g., agent-based)
            user_input = f' {chat_request["messages"][-1]["content"]}'
            chat_request["messages"] = chat_request["messages"][:-1]

            agent_exe = (
                await prepare_agent_exe(
                    model=chat_request["model"],
                    max_tokens=chat_request["max_tokens"],
                    temperature=chat_request["temperature"],
                    messages=chat_request["messages"],
                    session_id_=session_id,
                )
            ).with_config(
                {
                    "configurable": {
                        "run_name": "ask_openai",
                        "session_id": session_id,
                        "user_id": user_id,
                    }
                }
            )

            chat_history = []
            for msg in chat_request["messages"]:
                if msg["role"] == "user":
                    chat_history.append(HumanMessage(content=msg["content"]))
                else:
                    chat_history.append(AIMessage(content=msg["content"]))

            async for event in fetch_data(
                agent_exe,
                {"input": user_input, "chat_history": chat_history},
                "ask_openai",
            ):
                await websocket.send_json(event)

    except WebSocketDisconnect:
        logger.error("Client disconnected")
        return
    except json.JSONDecodeError:
        logger.error("Received data is not valid JSON")
        await websocket.send_json({"event": "error", "data": "Invalid JSON format"})
    except Exception as e:
        logger.error(f"Error in WebSocket connection: {str(e)}")
        await websocket.send_json(
            {"event": "error", "data": "Error processing request"}
        )
    finally:
        try:
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.close()
                logger.info("WebSocket closed successfully")
        except Exception as close_error:
            logger.error(f"Error closing WebSocket: {str(close_error)}")
