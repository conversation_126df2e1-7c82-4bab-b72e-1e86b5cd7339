import io
import json
import logging
import os
import re
import time
import uuid
from datetime import datetime
from http import HTT<PERSON>tatus
from typing import Literal, List

import openai
import requests
from fastapi import BackgroundTasks
from fastapi import HTTPEx<PERSON>, APIRouter, Depends
from google.cloud.firestore_v1 import As<PERSON><PERSON><PERSON>
from google.cloud.storage import <PERSON><PERSON><PERSON>
from langchain_openai import ChatOpenAI
from openai import AsyncOpenAI
from pydantic import BaseModel

from app.config.basic_config import BUCKET_NAME, default_model
from app.config.config import chatgpt_basic
from app.config.verify_token import verify_firebase_token
from app.core.db_config import get_db, get_storage

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Assuming you have the OpenAI API key stored in an environment variable
openai.api_key = os.getenv("OPENAI_API_KEY")


class StoryRequest(BaseModel):
    length: str
    genre: str
    characters: list
    age_group: str
    themes: List[str]
    brief_story: str
    image_style: str  # New field for image style
    voice: Literal["alloy", "echo", "fable", "onyx", "nova", "shimmer"]


async def generate_openai_story(request):
    start_time = time.time()
    # Map user length choice to max_tokens
    max_tokens_map = {
        'Short': 3000,
        'Medium': 8000,
        'Long': 15000,
    }
    max_tokens = max_tokens_map.get(
        request.length, 15000)  # Default to 15000 tokens
    prompt = (f"Write a  (totally {max_tokens} tokens) story for age group"
              f" {request.age_group.lower()} "
              f"in the"
              f" {request.genre.lower()} genre.")
    prompt += ("do not generate too long or too short paragraphs. A paragraph can have at least 5 lines, at most 10 "
               "lines.")
    prompt += (f"Make sure EVERYTIME the demonstrative pronouns of a character appears first time in a paragraph, put name and species and all other important traits after it using [[]]."
               f" After first time in the paragraph, always put name inside [[]] after demonstrative pronouns. For "
               f"example:\n He [[Simon, young boy,in red clothes and blue sunglass]] said,'Shall we go?'. She [[Nina, the red young chicken with a hat]] laughed and agreed. Then they [[Simon and Nina]] went together.\n For characters in one paragraph, we can  put the detailed traits inside [[]] for one time.")
    prompt += (f"Here is an example of two paragraphs story: \nShe [[Lucy, young girl,in red clothes and blue "
               f"sunglass]]aid to her friend[[Nina, the red young chicken with a hat]] : 'I[[Lucy]] hope we [[Lucy "
               f"and Nina]] can go'. Her friend [[Nina]] laughed and agreed. Then they [[Lucy and Nina]] go to the "
               f"festival together.\n They[Lucy(young girl,in red clothes and blue sunglass) and Nina (the red young chicken with a hat) ]] arrived "
               f"one hour late after the opening. They [[Lucy and Nina]] missed some of the show.")
    prompt += (f"As you can see, for the two paragraphs story, you still need to add description of the characters in "
               f"the second paragraph.")

    if request.characters:
        prompt += "The story should feature the following characters: "
        for character in request.characters:
            prompt += f"{character['name']} - {character['description']}. "
        prompt += ("Additionally, ensure that each character has distinct visual characteristics, "
                   "such as hair color, hairstyle, eye color, skin tone, facial features, height, "
                   "body type, clothing style, accessory choices (like hats, jewelry, or glasses), "
                   "fur patterns (if applicable), and even expressions, etc. "
                   "These characteristics should complement the descriptions provided by the user and must not conflict with one another.")
    else:
        prompt += ("The story will feature original characters created by the AI. Please ensure that each character "
                   "has distinct visual characteristics, including hair color, hairstyle, eye color, skin tone, facial features, "
                   "height, body type, clothing style, accessory choices (like hats, jewelry, or glasses), "
                   "fur patterns (if applicable),, and even expressions, etc. "
                   "These should be original and cohesive for each character.")

    prompt += f" The themes of the story are {request.themes}"
    if request.brief_story is not None and request.brief_story != "":
        prompt += f"The story should expand this brief story: \n===========\n {request.brief_story} \n============\n "

    #* do not reuse same ChatOpenAI
    llm : ChatOpenAI = ChatOpenAI(
    model=default_model,
    streaming=True,
    verbose=True,
    temperature=0.8, # we need it to be more creative when telling stories.
    max_tokens=max_tokens,
)

    try:
        response = await llm.ainvoke(prompt)
        logger.info(
            f"Story generated in {time.time() - start_time:.2f} seconds.")
        return response.content
    except Exception as e:
        logger.error(f"Error generating story: {e}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail="Error generating story")


async def generate_audio_from_text(story: str, audio_model: str = "tts-1",
                                   voice_: Literal["alloy", "echo", "fable", "onyx", "nova", "shimmer"] = "alloy"):
    start_time = time.time()
    client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    # Remove contents inside [[]] along with [[]]
    cleaned_story = re.sub(r'\[\[.*?\]\]', '', story)

    # Function to split text into chunks without splitting words
    def split_text_into_chunks(text: str, max_length: int) -> list[str]:
        words = text.split()
        chunks = []
        current_chunk = []

        for word in words:
            # Check if adding the word would exceed the max_length
            if len(' '.join(current_chunk + [word])) <= max_length:
                current_chunk.append(word)
            else:
                # If it exceeds, save the current chunk and start a new one
                chunks.append(' '.join(current_chunk))
                current_chunk = [word]  # Start a new chunk with the current word

        # Add any remaining words as a chunk
        if current_chunk:
            chunks.append(' '.join(current_chunk))

        return chunks

    # Split the cleaned story into chunks
    story_chunks = split_text_into_chunks(cleaned_story, 4096)

    combined_audio = io.BytesIO()  # Create a single BytesIO object to hold all audio data
    try:
        for chunk in story_chunks:
            response = await client.audio.speech.create(
                input=chunk,
                model=audio_model,
                voice=voice_,
                response_format="mp3",
            )
            for part in response.iter_bytes():
                combined_audio.write(part)  # Append each part to the combined audio file
        logger.info(f"Audio generated in {time.time() - start_time:.2f} seconds.")
        combined_audio.seek(0)  # Reset the pointer to the start of the BytesIO object
        return combined_audio.getvalue()  # Return the complete audio data as a single byte string

    except Exception as e:
        logger.error(f"Error generating audio: {e}")
        raise HTTPException(status_code=HTTPStatus.BAD_REQUEST,
                            detail="Error generating audio")


async def generate_images_from_story(story: str, request: StoryRequest):
    start_time = time.time()
    max_images_map = {
        'Short': 5,
        'Medium': 8,
        'Long': 12,
    }
    max_images = max_images_map.get(
        request.length, 5)  # Default to 12 images if not specified
    images = []

    # Split the story into paragraphs and filter out empty ones
    paragraphs = [p.strip() for p in story.split('\n') if p.strip()]
    total_paragraphs = len(paragraphs)

    # Log total paragraphs
    logger.info(f"Total paragraphs: {total_paragraphs}")

    # Determine the number of images to generate (maximum of 5)
    num_images = min(total_paragraphs, max_images)
    paragraphs_per_image = total_paragraphs // num_images
    # Handle any leftover paragraphs
    remaining_paragraphs = total_paragraphs % num_images

    logger.info(
        f"Images to generate: {num_images}, Paragraphs per image: {paragraphs_per_image}, Remaining paragraphs: {remaining_paragraphs}")

    # Prepare the style-related prompt components
    style_prompt = (
        f"Create images in a {request.image_style.lower()} style for a {request.age_group.lower()} audience, "
        f"focusing on the {request.genre.lower()} genre with themes of {', '.join(request.themes)}. "
    )

    # Extract or generate character characteristics
    character_characteristics: List = await extract_characteristics(paragraphs)

    # Construct a prompt for the LLM to generate an array of image prompts
    combined_prompt = ("Please generate a JSON array of image prompts based on the following section(each image "
                       "prompt for Section:\n")
    start_index = 0
    for i in range(num_images):
        num_paragraphs = paragraphs_per_image + \
                         (1 if i < remaining_paragraphs else 0)
        current_paragraphs = paragraphs[start_index:start_index + num_paragraphs]

        if current_paragraphs:  # Ensure there are paragraphs to use
            combined_prompt += f"\n\n{i + 1}. Section {i + 1}: {' '.join(current_paragraphs)}\n"
            start_index += num_paragraphs


    combined_prompt += ("\n\nReturn the prompts as a JSON array string like this: [\"prompt1\", \"prompt2\", "
                        "...]. Remember one image prompt for one of the Section.")

    combined_prompt += (
        f"\nUse the style instructions: {style_prompt}\n"
        "Be specific and detailed about the setting, objects, colors, and mood. "
        "Convey the desired atmosphere; use adjectives to refine the image, e.g., 'serene,' 'chaotic,' or 'mystical.' "
        "Consider the perspective and composition; specify if you want a close-up or a specific angle. "
        "Mention the lighting and time of day, such as 'sunny,' 'cloudy,' or 'candlelight.' "
        "If applicable, describe actions or movements to add dynamism to the image. "
        f"Character characteristics: {json.dumps(character_characteristics)}\n."
        f" If a character name is mentioned in some paragraph, then the full original detailed characteristic should always follow the name and appear in the image prompt to generate consistent image for this character."
        f"Ensure that all generated content is appropriate and does not contain any offensive or sensitive material.\n"
    )
    combined_prompt += (f"Make sure EVERYTIME the demonstrative pronouns of a character appears first time in a paragraph, put name and species and all other important traits after it using [[]]."
               f" After first time in the paragraph, always put name inside [[]] after demonstrative pronouns. For "
               f"example:\n He [[Simon, young boy,in red clothes and blue sunglass]] said,'Shall we go?'. She [[Nina, the red young chicken with a hat]] laughed and agreed. Then they [[Simon and Nina]] went together.\n For characters in one paragraph, we can  put the detailed traits inside [[]] for one time.")


    llm = chatgpt_basic
    try:
        # Call the LLM to generate prompts
        refined_response = await llm.ainvoke(combined_prompt)

        # Fix the JSON array string using regex to extract the image prompts
        json_array_string = refined_response.content.strip()

        # Parse the JSON array string into a list of strings
        refined_prompts = json.loads(extract_json_array(json_array_string))
        logger.info("refined_prompts: {}".format(refined_prompts))

        # Generate images using the refined prompts
        client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        for i, prompt in enumerate(refined_prompts):
            if prompt:  # Skip any empty prompts
                # Append the style to each prompt
                prompt = f"{prompt} {style_prompt}"
                logger.info(f"current prompt used: {prompt}")
                image_response = await client.images.generate(
                    prompt=prompt,
                    model="dall-e-3",
                    quality="standard",
                    n=1,
                    size="1024x1024"
                )
                for image in image_response.data:
                    # Calculate the starting index and number of paragraphs for this image
                    start_index = i * paragraphs_per_image + \
                                  min(i, remaining_paragraphs)
                    num_paragraphs = paragraphs_per_image + \
                                     (1 if i < remaining_paragraphs else 0)
                    corresponding_paragraphs = paragraphs[start_index:start_index + num_paragraphs]
                    #  remove those character description
                    for i in range(len(corresponding_paragraphs)):
                        corresponding_paragraphs[i] = re.sub(
                            r'\[\[.*?\]\]', '', corresponding_paragraphs[i])

                    images.append({
                        'image_url': image.url,
                        'prompt': prompt,  # Store the prompt used for this image
                        'paragraphs': corresponding_paragraphs  # Store corresponding paragraphs
                    })

        logger.info(
            f"Images generated in {time.time() - start_time:.2f} seconds.")
        return images

    except Exception as e:
        logger.error(f"Error generating images: {e}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail="Error generating images")


def extract_json_array(input_string):
    cleaned_input = input_string.replace("```json\n", "").replace("\n```", "")

    return cleaned_input
    # # Define a regex pattern to match the JSON array
    # json_pattern = r'(\[[\s\S]*?\])'
    #
    # # Search for the JSON array in the cleaned input string
    # match = re.search(json_pattern, cleaned_input)
    #
    # # Search for the JSON array in the input string
    # match = re.search(json_pattern, input_string)
    #
    # if match:
    #     json_array_string = match.group(1)  # Extract the matched JSON array string
    #     try:
    #         # Parse the JSON array string into a Python object
    #         json_array = json.loads(json_array_string)
    #         return json_array
    #     except json.JSONDecodeError:
    #         logger.info("Failed to decode JSON.")
    #         return None
    # else:
    #     logger.info("No valid JSON array found.")
    #     return None


async def extract_characteristics(paragraphs):
    # Combine paragraphs to analyze for character traits
    all_text = " ".join(paragraphs)
    character_prompt = (
        "Extract the characteristics of the characters mentioned in the text. "
        "Provide detailed attributes such as species(human, animal(what animal, bear? bunny?)), hair color, eye color, age, gender, species, "
        "height, weight, clothing style, and any unique traits or accessories. "
        "If a character is not mentioned, generate a default set of characteristics. "
        "For example, 'Simon the bear, black fur, young male, 3 feet tall, "
        "weighs 80 pounds, wearing a red hat, blue shorts, and has a playful personality.' You can add more traits depends on the story. "
        "always assign color to the character(if it is human, it is clothes color, if not human, it could be fur color(like a brown bear)"
        f"Text: {all_text}"
        "return a json array of the characters, key is name of the character,value is detail of the character"
    )

    # Call the LLM to extract characteristics
    llm = chatgpt_basic
    response = await llm.ainvoke(character_prompt)

    # Process the response to return a structured format
    try:
        logger.info(f"characteristics response generated: {response}")
        characteristics = json.loads(
            extract_json_array(response.content.strip()))
        logger.info(f"characteristics: {characteristics}")
    except json.JSONDecodeError:
        logger.error(
            "Failed to decode JSON response from character extraction.")
        characteristics = []

    return characteristics


async def save_to_firebase(request: StoryRequest, story, audio_data, image_urls, user_id, session_id):
    db: AsyncClient = get_db()  # Assuming get_db() returns an instance of AsyncClient
    now = datetime.now()
    timestamp = now.strftime("%Y%m%d%H%M%S")

    # Create a new document in the user's stories collection
    # Point to the collection without user_id here
    stories_ref = db.collection('stories')
    doc_ref = stories_ref.document(user_id).collection('user_stories').document(
        session_id)  # Create a sub-collection for user stories
    cleaned_story = re.sub(r'\[\[.*?\]\]', '', story)

    story_data = {
        "id": session_id,
        'story': story,
        'cleaned_story': cleaned_story,
        'audio_url': None,
        'image_urls': image_urls,
        'created_at': now.isoformat(),
        'length': request.length,
        'genre': request.genre,
        'characters': request.characters,
        'age_group': request.age_group,
        'themes': request.themes,
        'brief_story': request.brief_story,
        'image_style': request.image_style,
    }

    storage = get_storage()
    bucket = storage.bucket(BUCKET_NAME)

    try:
        # Saving audio file to Firebase Storage
        audio_blob: Blob = bucket.blob(f"audios/{user_id}/{timestamp}.mp3")
        audio_blob.upload_from_string(audio_data, content_type='audio/mpeg')
        audio_blob.make_public()
        audio_storage_url = audio_blob.public_url
        story_data['audio_url'] = audio_storage_url

        # Saving images to Firebase Storage
        for index, image_url in enumerate(image_urls):
            image_response = requests.get(image_url["image_url"])
            if image_response.status_code == 200:
                image_blob: Blob = bucket.blob(
                    f"images/{user_id}/{timestamp}_{index}.png")
                image_blob.upload_from_string(
                    image_response.content, content_type='image/png')
                image_blob.make_public()
                story_data['image_urls'][index]["image_url"] = image_blob.public_url
            else:
                logger.warning(
                    f'Failed to download image {image_url["image_url"]}')

        # Set the story data in the document created earlier
        # Use set to create or overwrite the document
        await doc_ref.set(story_data)
        return audio_storage_url, story_data['image_urls']

    except Exception as e:
        logger.error(f"Error saving to Firebase: {e}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail="Error saving to Firebase")


router = APIRouter()


@router.post("/generate-story/")
async def generate_story(request: StoryRequest,
                         background_tasks: BackgroundTasks,
                         decoded_token=Depends(verify_firebase_token)):
    user_id: str = decoded_token["user_id"]
    session_id: str = str(uuid.uuid4())

    # Create a background task to handle story generation
    background_tasks.add_task(handle_story_generation, request, user_id, session_id)

    # Return the session_id immediately
    return {"session_id": session_id}


async def handle_story_generation(request: StoryRequest, user_id: str, session_id: str):
    try:
        start_time = time.time()
        # Generate the story
        story = await generate_openai_story(request)
        audio_chunk = await generate_audio_from_text(story=story, voice_=request.voice)
        image_urls = await generate_images_from_story(story, request)
        audio_url, storage_image_urls = await save_to_firebase(request, story, audio_chunk, image_urls, user_id,
                                                               session_id)

        logger.info(f"Story generated in {time.time() - start_time:.2f} seconds.")


    except HTTPException as http_ex:
        raise http_ex
    except Exception as e:
        logger.error(f"Error in generating story: {e}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail="Error generating story")
