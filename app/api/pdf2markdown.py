import os
import time
import uuid

import aiohttp
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Body
from loguru import logger
from pypdf import PdfReader
from fastapi import UploadFile, File, Form
from app.config.verify_token import verify_firebase_token
from app.queue.pdf2markdown_tasks import process_pdf_task, async_kickoff_process_pdf_task

router = APIRouter()

@router.post("/convert-pdf-upload/")
async def upload_pdf(
        file: UploadFile = File(...),
        end_pages: int = Form(-1),  # Default to -1 if not provided
        decoded_token=Depends(verify_firebase_token),
        background_tasks: BackgroundTasks = BackgroundTasks()
):
    user_id: str = decoded_token["user_id"]
    session_id: str = str(uuid.uuid4())

    # Directory to store output files temporarily
    OUTPUT_DIR = f'./output/pdf2markdown/{user_id}/{session_id}'
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    pdf_path = os.path.join(OUTPUT_DIR, f"{session_id}.pdf")  # Use session_id for the saved file name

    try:
        start_time = time.time()
        logger.info(f"Started conversion by user {user_id}")

        # Save the uploaded PDF file
        with open(pdf_path, "wb") as f:
            content = await file.read()
            f.write(content)

        # Call the existing conversion logic
        return await process_pdf(pdf_path, end_pages, user_id, session_id, background_tasks)

    except Exception as e:
        logger.exception("An error occurred while uploading PDF: %s", e)
        raise HTTPException(status_code=500, detail="Internal server error.")


async def process_pdf(pdf_path: str, end_pages: int, user_id: str, session_id: str, background_tasks: BackgroundTasks):
    try:
        start_time = time.time()
        # Get the number of pages in the PDF
        with open(pdf_path, "rb") as f:
            pdf_reader = PdfReader(f)
            total_pages = len(pdf_reader.pages)

        # Set end_pages to the total pages if it's not provided
        if end_pages == -1 or end_pages > total_pages:
            end_pages = total_pages

        logger.info(f"Total pages in PDF: {total_pages}, using end_pages: {end_pages}")

        use_celery = False  # Set this based on your logic

        if use_celery:
            # Call the Celery task
            task = async_kickoff_process_pdf_task.delay(pdf_path, end_pages, user_id, session_id)

            end_time = time.time()
            elapsed_time = end_time - start_time
            logger.info(f"Successfully initiated conversion in {elapsed_time:.2f} seconds")

            return {
                "task_id": task.id,
                "status": "Processing started, check back for results.",
                "session_id": session_id,
            }
        else:
            if os.environ.get("ENABLE_HEAVY_TASKS", "False") == "True":
                # Add the processing task to the background
                background_tasks.add_task(process_pdf_task, None, pdf_path, end_pages, user_id, session_id)
            else:
                logger.info("ENABLE_HEAVY_TASKS not enabled")

        return {
            "status": "Processing completed, check storage for results",
            "session_id": session_id
        }

    except Exception as e:
        logger.exception("An error occurred while processing PDF: %s", e)
        raise HTTPException(status_code=500, detail="Internal server error.")

@router.post("/convert-pdf/")
async def convert_pdf(
        url: str = Body(...),  # Now expecting JSON body
        end_pages: int = Body(-1),  # Default to -1 if not provided
        decoded_token=Depends(verify_firebase_token),
        background_tasks: BackgroundTasks = BackgroundTasks()
):
    user_id: str = decoded_token["user_id"]
    session_id: str = str(uuid.uuid4())

    # Directory to store output files temporarily
    OUTPUT_DIR = f'./output/pdf2markdown/{user_id}/{session_id}'
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    pdf_path = os.path.join(OUTPUT_DIR, f"{session_id}.pdf")  # Use session_id for the saved file name

    try:
        start_time = time.time()
        logger.info(f"Started conversion by user {user_id}")

        if url:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        raise HTTPException(status_code=400, detail="Failed to download PDF from the provided URL.")
                    with open(pdf_path, "wb") as f:
                        f.write(await response.read())
        else:
            raise HTTPException(status_code=400, detail="No file or URL provided.")

        # Call the existing processing logic
        return await process_pdf(pdf_path, end_pages, user_id, session_id, background_tasks)

    except Exception as e:
        logger.exception("An error occurred while converting PDF: %s", e)
        raise HTTPException(status_code=500, detail="Internal server error.")
