
from haystack import Pipeline
from langchain_core.retrievers import BaseRetriever


#  TODO: use QdrantDocumentStore to do it


class HaystackRetriever(BaseRetriever):
    def __init__(self, haystack_pipeline: Pipeline, top_k: int = 5):
        self.pipeline = haystack_pipeline
        self.top_k = top_k

    def _get_relevant_documents(self, query: str):
        # Run the query through the Haystack pipeline
        results = self.pipeline.run(query=query, top_k_retriever=self.top_k)
        # Convert Haystack results to LangChain Document format
        documents = [Document(page_content=doc.content, metadata=doc.meta)
                     for doc in results["documents"]]
        return documents
