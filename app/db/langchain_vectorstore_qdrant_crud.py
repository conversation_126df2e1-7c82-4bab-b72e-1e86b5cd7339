from typing import Any, List, Dict

from langchain_core.embeddings import Embeddings
from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import PointStruct

from app.core.tracing_setup import logger
from app.db.vector_database_crud import VectorDatabaseCRUD


class LangchainQdrantVectorDatabaseCRUD(VectorDatabaseCRUD):

    def __init__(self, qdrant_client: QdrantClient, embed_func: Embeddings) -> None:
        self.qdrant_client = qdrant_client
        self.embed_func = embed_func
        self.vector_stores = {}  # * saves all the vector stores for each user. serves as cache

    def get_vector_store(self, user_id: str) -> QdrantVectorStore:
        if user_id not in self.vector_stores:
            self.vector_stores[user_id]: QdrantVectorStore = QdrantVectorStore(
                client=self.qdrant_client,
                collection_name=f"Userdocument_users_{user_id}",
                embedding=self.embed_func,
            )
        return self.vector_stores[user_id]

    def create_user_index(self, user_id: str):
        try:
            collection_name = f"Userdocument_users_{user_id}"
            self.ensure_index_exists(collection_name)

        except Exception as e:
            logger.error(f"Failed to create index for user {user_id}: {e}")
            raise

    def ensure_index_exists(self, index: str):
        try:
            # * delete if already exist
            # if self.qdrant_client.collection_exists(index):
            #     self.qdrant_client.delete_collection(index)

            if not self.qdrant_client.collection_exists(index):
                self.qdrant_client.create_collection(
                    index,
                    vectors_config=models.VectorParams(
                        size=3072, distance=models.Distance.COSINE),
                )
                self.qdrant_client.create_payload_index(
                    collection_name=index,
                    field_name="metadata.document_firebaseurl",
                    field_schema=models.KeywordIndexParams(
                        type="keyword",
                    ),
                )

                self.qdrant_client.create_payload_index(
                    collection_name=index,
                    field_name="metadata.document_number",
                    field_schema=models.IntegerIndexParams(
                        type=models.IntegerIndexType.INTEGER,
                        lookup=False,
                        range=True,

                    ),

                )
                logger.info(f"collection {index} created with index")
        except Exception as e:
            logger.error(f"Failed to ensure index exists for {index}: {e}")
            raise

    def get_bot_documents(self, original_bot_id: str, original_user_id: str):
        try:
            collection_name = f"Userdocument_users_{original_user_id}"
            response = self.qdrant_client.scroll(
                collection_name=collection_name,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="metadata.bot_ids",
                            match=models.MatchValue(
                                value=original_bot_id)
                        )]
                ),
                limit=1000,
                with_payload=True,
                with_vectors=False,
                order_by=models.OrderBy(
                    key="metadata.document_number", direction="asc")
            )
            return [doc.payload["metadata"] for doc in response[0]]
        except Exception as e:
            logger.error(
                f"Failed to get bot documents for bot {original_bot_id} and user {original_user_id}: {e}")
            raise

    def create_document(self, document_datas: List[Any], original_user_id: str):
        try:
            vector_store: QdrantVectorStore = self.get_vector_store(
                original_user_id)
            texts = [data_obj.get("page_content")
                     for data_obj in document_datas]
            metadatas = document_datas
            vector_store.add_texts(texts, metadatas)

        except Exception as e:
            logger.error(
                f"Failed to create documents for user {original_user_id}: {e}")
            raise

    def update_document(self, document_id: str, updated_data, original_user_id: str):
        try:
            collection_name = f"Userdocument_users_{original_user_id}"
            document_content = updated_data.get("page_content")
            vector_ = self.embed_func.embed_documents(
                [document_content])
            point = PointStruct(
                id=document_id,
                vector=vector_,
                payload={"page_content": document_content, "metadata": updated_data})
            self.qdrant_client.upsert(collection_name, [point])
        except Exception as e:
            logger.error(
                f"Failed to update document {document_id} for user {original_user_id}: {e}")
            raise

    def assign_documents_to_bot(self, original_bot_id: str, document_ids: List[str], original_user_id: str):
        if len(document_ids) == 0:
            logger.info("document_ids is zero, no action")
            return
        try:
            collection_name = f"Userdocument_users_{original_user_id}"
            response = self.qdrant_client.scroll(
                collection_name=collection_name,
                scroll_filter=models.Filter(
                    must=models.HasIdCondition(has_id=document_ids),
                ),
                limit=len(document_ids),
                with_payload=True,
                with_vectors=True,
                order_by=models.OrderBy(
                    key="metadata.document_number", direction="asc")
            )
            points = []
            for document_ in response[0]:
                if original_bot_id not in document_.payload["metadata"]["bot_ids"]:
                    document_.payload["metadata"]["bot_ids"].append(
                        original_bot_id)
                    point = PointStruct(
                        id=document_.id,
                        vector=document_.vector,
                        payload={"page_content": document_.payload["page_content"], "metadata": document_.payload["metadata"]})
                    points.append(point)
            # now we need to put updated databack
            if len(points) != 0:
                self.qdrant_client.upsert(collection_name, points)
        except Exception as e:
            logger.error(
                f"Failed to assign documents to bot {original_bot_id} for user {original_user_id}: {e}")
            raise

    def remove_documents_from_bot(self, original_bot_id: str, document_ids: List[str], original_user_id: str):
        if len(document_ids) == 0:
            logger.info("document_ids is zero, no action")
            return
        try:
            collection_name = f"Userdocument_users_{original_user_id}"
            response = self.qdrant_client.scroll(
                collection_name=collection_name,
                scroll_filter=models.Filter(
                    must=models.HasIdCondition(has_id=document_ids),
                ),
                limit=len(document_ids),
                with_payload=True,
                with_vectors=True,
                order_by=models.OrderBy(
                    key="metadata.document_number", direction="asc")
            )
            points = []
            for document_ in response[0]:
                if original_bot_id in document_.payload["metadata"]["bot_ids"]:
                    ids = set(document_.payload["metadata"]["bot_ids"])
                    ids.remove(original_bot_id)
                    document_.payload["metadata"]["bot_ids"] = list(ids)
                    point = PointStruct(
                        id=document_.id,
                        vector=document_.vector,
                        payload={"page_content": document_.payload["page_content"], "metadata": document_.payload["metadata"]})
                    points.append(point)
            # now we need to put updated databack
            if len(points) != 0:
                self.qdrant_client.upsert(collection_name, points)
        except Exception as e:
            logger.error(
                f"Failed to remove documents from bot {original_bot_id} for user {original_user_id}: {e}")
            raise

    def get_documents_by_firebase_urls(self, document_firebase_urls: List[str], original_user_id: str):
        try:
            collection_name = f"Userdocument_users_{original_user_id}"
            response = self.qdrant_client.scroll(
                collection_name=collection_name,
                scroll_filter=models.Filter(
                    should=[models.FieldCondition(
                        key="metadata.document_firebaseurl",
                        match=models.MatchValue(value=url)
                    ) for url in document_firebase_urls]
                ),
                limit=len(document_firebase_urls) * 100,
                with_payload=True,
                with_vectors=False,
                order_by=models.OrderBy(
                    key="metadata.document_number", direction="asc")
            )
            return response[0]
        except Exception as e:
            logger.error(
                f"Failed to get documents by Firebase URLs for user {original_user_id}: {e}")
            raise

    def get_document_ids_by_firebase_urls(self, document_firebase_urls: List[str], original_user_id: str) -> List[str]:
        try:
            documents = self.get_documents_by_firebase_urls(
                document_firebase_urls, original_user_id)
            if len(documents) == 0:
                return []
            return [str(doc.id) for doc in documents]
        except Exception as e:
            logger.error(
                f"Failed to get document IDs by Firebase URLs for user {original_user_id}: {e}")
            raise

    def get_document_content_by_firebase_urls(self, firebase_urls: List[str], original_user_id: str) -> List[str]:
        try:
            documents = self.get_documents_by_firebase_urls(
                firebase_urls, original_user_id)
            return [doc.payload["page_content"] for doc in documents]
        except Exception as e:
            logger.error(
                f"Failed to get document content by Firebase URLs for user {original_user_id}: {e}")
            raise

    def delete_documents(self, document_ids: List[str], original_user_id: str):
        if len(document_ids) == 0:
            return
        try:
            collection_name = f"Userdocument_users_{original_user_id}"
            self.qdrant_client.delete(
                collection_name, points_selector=document_ids)
        except Exception as e:
            logger.error(
                f"Failed to delete documents {document_ids} for user {original_user_id}: {e}")
            raise

    def delete_documents_by_firebase_urls(self, firebase_urls: List[str], original_user_id: str):
        try:
            document_ids = self.get_document_ids_by_firebase_urls(
                firebase_urls, original_user_id)
            self.delete_documents(document_ids, original_user_id)
        except Exception as e:
            logger.error(
                f"Failed to delete documents by Firebase URLs for user {original_user_id}: {e}")
            raise

    def search_document(self, query: str, original_user_id: str):
        try:
            collection_name = f"Userdocument_users_{original_user_id}"
            query_vector = self.embed_func.embed_query(query)
            response = self.qdrant_client.search(
                collection_name,
                query_vector=query_vector,
                limit=10
            )
            return [hit.payload for hit in response]
        except Exception as e:
            logger.error(
                f"Failed to search documents for user {original_user_id}: {e}")
            raise

    def document_exists(self, document_id: str, original_user_id: str) -> bool:
        try:
            collection_name = f"Userdocument_users_{original_user_id}"
            response = self.qdrant_client.retrieve(
                collection_name, [document_id])
            return len(response) > 0
        except Exception as e:
            logger.error(
                f"Failed to check if document {document_id} exists for user {original_user_id}: {e}")
            raise

    def search_document_by_embedding(self, embedding_vector, original_user_id: str, original_bot_id: str):
        try:
            collection_name = f"Userdocument_users_{original_user_id}"
            response = self.qdrant_client.search(
                collection_name,
                query_vector=embedding_vector,
                filter=models.Filter(
                    must=[models.FieldCondition(
                        key="metadata.bot_ids",
                        match=models.MatchValue(value=original_bot_id)
                    )]
                ),
                limit=10
            )
            return [hit.payload for hit in response]
        except Exception as e:
            logger.error(
                f"Failed to search document by embedding for user {original_user_id} and bot {original_bot_id}: {e}")
            raise

    def create_filter(self, bot_id: str, user_id: str, query: str) -> Dict[str, Any]:
        try:
            query_vector = self.embed_func.embed_query(query)
            return {
                "query_vector": query_vector,
                "filter": {
                    "must": [
                        {"key": "metadata.bot_ids", "match": {"value": bot_id}},
                        {"key": "Userdocument_users_" + user_id,
                            "match": {"value": user_id}}
                    ]
                }
            }
        except Exception as e:
            logger.error(
                f"Failed to create filter for bot {bot_id} and user {user_id}: {e}")
            raise
