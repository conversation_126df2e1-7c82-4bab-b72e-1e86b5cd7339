"""Abstract CRUD interface for vector databases."""

from abc import ABC<PERSON>eta, abstractmethod
from typing import Any, Dict, List

from app.utils.util import get_encoded_id


class VectorDatabaseCRUD(metaclass=ABCMeta):
    """Base class for vector store implementations."""

    def get_index_name_by_prefix(self, prefix: str, original_id: str) -> str:
        """Return a unique index name based on a prefix and identifier."""
        encoded_id = get_encoded_id(original_id)
        return f"{prefix}_{encoded_id}"

    @abstractmethod
    def create_user_index(self, user_id: str) -> None:
        """Create a dedicated index for a user if it does not already exist."""
        pass

    @abstractmethod
    def ensure_index_exists(self, index: str, mappings: Dict[str, Any]) -> None:
        """Ensure ``index`` exists with the given ``mappings``."""
        pass

    @abstractmethod
    def get_bot_documents(self, original_bot_id: str, original_user_id: str) -> List[Dict[str, Any]]:
        """Return all documents assigned to a bot."""
        pass

    @abstractmethod
    def create_document(self, document_datas: List[Any], original_user_id: str) -> None:
        """Insert new documents for a user."""
        pass

    @abstractmethod
    def update_document(self, document_id: str, updated_data: Dict[str, Any], original_user_id: str) -> None:
        """Update an existing document."""
        pass

    @abstractmethod
    def assign_documents_to_bot(self, original_bot_id: str, document_ids: List[str], original_user_id: str) -> None:
        """Assign documents to a bot by ID."""
        pass

    @abstractmethod
    def remove_documents_from_bot(
        self, original_bot_id: str, document_ids: List[str], original_user_id: str
    ) -> None:
        """Remove the link between documents and a bot."""
        pass

    def assign_documents_to_bot_by_firebase_url(
            self,
            original_bot_id: str,
            document_firebase_urls: List[str],
            original_user_id: str,
    ):
        """Assign documents to a bot using their Firebase URLs."""
        # Find documents by Firebase URLs
        document_ids = self.get_document_ids_by_firebase_urls(
            document_firebase_urls, original_user_id
        )

        self.assign_documents_to_bot(original_bot_id, document_ids, original_user_id)

    def remove_documents_from_bot_by_firebase_url(
            self,
            original_bot_id: str,
            document_firebase_urls: List[str],
            original_user_id: str,
    ):
        """Remove the link between Firebase based documents and a bot."""
        # Find documents by Firebase URLs
        document_ids = self.get_document_ids_by_firebase_urls(
            document_firebase_urls, original_user_id
        )

        self.remove_documents_from_bot(original_bot_id, document_ids, original_user_id)

    @abstractmethod
    def get_documents_by_firebase_urls(self, document_firebase_urls: List[str], original_user_id: str) -> List[Dict[str, Any]]:
        """Return documents given their Firebase URLs."""
        pass

    @abstractmethod
    def get_document_ids_by_firebase_urls(
        self, document_firebase_urls: List[str], original_user_id: str
    ) -> List[str]:
        """Return document IDs for a set of Firebase URLs."""
        pass

    @abstractmethod
    def get_document_content_by_firebase_urls(
        self, firebase_urls: List[str], original_user_id: str
    ) -> List[str]:
        """Return document content identified by Firebase URLs."""
        pass

    @abstractmethod
    def delete_documents(self, document_ids: List[str], original_user_id: str) -> None:
        """Delete documents by ID."""
        pass

    @abstractmethod
    def delete_documents_by_firebase_urls(self, firebase_urls: List[str], original_user_id: str) -> None:
        """Delete documents using their Firebase URLs."""
        pass

    @abstractmethod
    def search_document(self, query: str, original_user_id: str) -> List[Dict[str, Any]]:
        """Search documents using a plain text query."""
        pass

    @abstractmethod
    def document_exists(self, document_id: str, original_user_id: str) -> bool:
        """Return ``True`` if ``document_id`` exists for the user."""
        pass

    @abstractmethod
    def search_document_by_embedding(
        self, embedding_vector: Any, original_user_id: str, original_bot_id: str
    ) -> List[Dict[str, Any]]:
        """Search for documents using an embedding vector."""
        pass

    @abstractmethod
    def create_filter(self, bot_id: str, user_id: str, query: str) -> Dict[str, Any]:
        """Return an implementation specific search filter for a query."""
        return {}
