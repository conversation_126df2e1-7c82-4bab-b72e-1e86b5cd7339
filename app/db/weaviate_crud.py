from typing import Any, Dict, List, Optional

from weaviate.classes.query import Sort
from weaviate.client import WeaviateClient
from weaviate.collections.classes.filters import Filter
from weaviate.collections.classes.tenants import Tenant
from weaviate.util import generate_uuid5

from app.core.tracing_setup import logger
from app.db.vector_database_crud import VectorDatabaseCRUD


class WeaviateVectorDatabaseCRUD(VectorDatabaseCRUD):
    def __init__(self, we_client: WeaviateClient) -> None:
        self.we_client = we_client
        self.we_client.batch.fixed_size(batch_size=100)
        self.index_mappings = {
            "class": "Userdocument",
            "multiTenancyConfig": {"enabled": True},
            "invertedIndexConfig": {
                "bm25": {"b": 0.75, "k1": 1.2},
                "cleanupIntervalSeconds": 60,
                "stopwords": {"additions": None, "preset": "en", "removals": None},
            },
            "moduleConfig": {
                "text2vec-openai": {
                    "model": 'text-embedding-3-large',
                    "dimensions": 3072,
                    "type": "text",
                    "vectorizeClassName": True,
                },
                "generative-openai": {"model": "gpt-3.5-turbo-16k"},
            },
            "properties": [
                {
                    "dataType": ["text"],
                    "moduleConfig": {
                        "text2vec-openai": {
                            "skip": False,
                            "vectorizePropertyName": False,
                        }
                    },
                    "name": "document_name",
                    "tokenization": "word",
                },
                {
                    "dataType": ["text"],
                    "moduleConfig": {
                        "text2vec-openai": {
                            "skip": False,
                            "vectorizePropertyName": False,
                        }
                    },
                    "name": "page_content",
                    "tokenization": "word",
                },
                {"dataType": ["int"], "name": "document_number"},
                {"dataType": ["text"], "name": "document_firebaseurl"},
                {"dataType": ["text[]"], "name": "bot_ids"},
                {"dataType": ["text"], "name": "user_id"},
            ],
            "replicationConfig": {"factor": 1},
            "vectorIndexConfig": {
                "skip": False,
                "cleanupIntervalSeconds": 300,
                "maxConnections": 64,
                "efConstruction": 245,
                "ef": -1,
                "dynamicEfMin": 100,
                "dynamicEfMax": 500,
                "dynamicEfFactor": 8,
                "vectorCacheMaxObjects": 1000000000000,
                "flatSearchCutoff": 40000,
                "distance": "cosine",
                "pq": {
                    "enabled": False,
                    "bitCompression": False,
                    "segments": 0,
                    "centroids": 256,
                    "encoder": {"type": "kmeans", "distribution": "log-normal"},
                },
            },
            "vectorIndexType": "hnsw",
            "vectorizer": "text2vec-openai",
        }

    def get_index_name_by_prefix(
            self, prefix: str, original_id: str, create_schema: bool = True
    ):
        # DO not encode for Weaviate
        user_index_name = f"{prefix}_{original_id}"
        if create_schema:
            # logger.error(" get_index_name_by_prefix createSchema called")
            self.ensure_index_exists(
                index=user_index_name, mappings=self.index_mappings
            )
        return user_index_name

    def create_user_index(self, user_id: str):

        self.get_index_name_by_prefix("users", user_id)

    def ensure_index_exists(self, index: str, mappings):
        if not self.we_client.collections.exists("Userdocument"):
            self.we_client.collections.create_from_dict(mappings)

        collections_get = self.we_client.collections.get("Userdocument")
        tenants = collections_get.tenants.get()

        # Find the tenant with the matching name
        tenant: Optional[Tenant] = tenants.get(index, None)

        if tenant is None:
            # If the tenant doesn't exist, create it
            new_tenant = Tenant(name=index)
            collections_get.tenants.create(tenants=[new_tenant])

    def get_bot_documents(self, original_bot_id: str, original_user_id: str):
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)
        collections = self.we_client.collections.get(
            "Userdocument").with_tenant(user_index_name)
        documents = (
            collections.query.fetch_objects(
                return_properties=[
                    "document_number",
                    "page_content",
                    "document_firebaseurl",
                    "document_name",
                ],
                filters=Filter.by_property(
                    "bot_ids").contains_any([original_bot_id]),
                sort=Sort.by_property(name="document_number", ascending=True).by_property(name="document_firebaseurl", ascending=True),
            )

        )

        return [document.properties for document in documents.objects]

    def update_document(self, document_id: str, updated_data, original_user_id: str):
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)
        documents = self.we_client.collections.get(
            "Userdocument").with_tenant(user_index_name)
        documents.data.update(
            uuid=document_id,
            properties={
                "page_content": updated_data.get("page_content", ""),
                "document_name": updated_data.get("document_name", ""),
                "document_number": updated_data.get("document_number", 0),
                "document_firebaseurl": updated_data.get("document_firebaseurl", ""),
                "user_id": updated_data.get("user_id", ""),
                "bot_ids": updated_data.get("bot_ids", []),
            },
        )

    def update_documents_batch(self, documents_data: List[Any], original_user_id: str):
        """
        Update multiple documents using a batch operation.
        :param documents_data: List of tuples (document_id, updated_data, user_index_name)
        """
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)
        collections = self.we_client.collections.get(
            "Userdocument").with_tenant(user_index_name)
        # document_ids = []
        # for document_id, updated_data, user_index_name in documents_data:
        #     document_ids.append(document_id)
        #
        # if len(document_ids) > 0:
        #     # * delete and add in a single batch
        #     collections.data.delete_many(
        #         where=Filter.by_id().contains_any(document_ids)
        #     )
        #
        # # * now we add it back without uuid term
        # # * this might not work as expected if we have duplicate document_ids in our documents_data
        # # * since we are not using uuid, we are not able to delete and add in a single batch
        # # * and we will end up with duplicates
        # # * alternatively, we can create a new batch operation for each document
        # # * but this will be more complex and less efficient
        # # * instead, we can just delete and add in a single batch for now
        # # * we will handle any potential duplicates in our application layer
        # # * or if we want to handle duplicates in the database layer, we can add a unique constraint on the document_id field
        # with collections.batch.dynamic() as batch:
        #     for document_id, updated_data, user_index_name in documents_data:
        #         batch.add_object(
        #             properties={
        #                 "page_content": updated_data.get("page_content", ""),
        #                 "document_name": updated_data.get("document_name", ""),
        #                 "document_number": updated_data.get("document_number", 0),
        #                 "document_firebaseurl": updated_data.get("document_firebaseurl", ""),
        #                 "user_id": updated_data.get("user_id", ""),
        #                 "bot_ids": updated_data.get("bot_ids", []),
        #
        #             },
        #             vector=updated_data.get("vector_field", []),  # Handle vector_field as needed
        #         )
        #     if batch.number_errors > 0:
        #         logger.error(f"Batch operation failed with {batch.number_errors} errors.")
        #         failed_objs = self.we_client.batch.failed_objects
        #         failed_references = self.we_client.batch.failed_references
        #         failed_objs_collection = collections.batch.failed_objects
        #         failed_references_collection = collections.batch.failed_references

        # TODO: * since there are some issues with batch update. we delete then add. but we need to fix this in long term
        try:
            for document_id, updated_data, user_index_name, vector_ in documents_data:
                collections.data.update(
                    properties={
                        "page_content": updated_data.get("page_content", ""),
                        "document_name": updated_data.get("document_name", ""),
                        "document_number": updated_data.get("document_number", 0),
                        "document_firebaseurl": updated_data.get("document_firebaseurl", ""),
                        "user_id": updated_data.get("user_id", ""),
                        "bot_ids": updated_data.get("bot_ids", []),
                    },
                    # Handle vector_field as needed
                    uuid=document_id,
                    # vector=vector_,
                )
        except Exception as e:
            logger.error(f"Error when updating documents batch {e}")

    def get_documents_batch(self, document_firebase_urls: List[str], original_user_id: str):
        """
        Retrieve multiple documents using a batch query operation.
        :param original_user_id: The user ID to determine the tenant index.
        :return: List of retrieved documents.
        """
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)

        collections = self.we_client.collections.get(
            "Userdocument").with_tenant(user_index_name)
        filter = Filter.by_property("document_firebaseurl").contains_any(document_firebase_urls) if len(
            document_firebase_urls) > 0 else None
        # Query Weaviate to retrieve the documents
        documents = (
            collections.query.fetch_objects(
                return_properties=[
                    "document_number",
                    "page_content",
                    "document_firebaseurl",
                    "document_name",
                    "user_id",
                    "bot_ids",
                ],
                include_vector=True,
                filters=filter,
                sort=Sort.by_property(name="document_number", ascending=True).by_property(name="document_firebaseurl", ascending=True),
            )
        )

        documents_data = documents.objects

        # Return documents data or an empty list if it's not found
        return documents_data if documents_data is not None else []

    def assign_documents_to_bot(self, original_bot_id: str, document_ids: List[str], original_user_id: str):
        pass

    def remove_documents_from_bot(
            self, original_bot_id: str, document_ids: List[str], original_user_id: str
    ):
        pass

    def assign_documents_to_bot_by_firebase_url(
            self,
            original_bot_id: str,
            document_firebase_urls: List[str],
            original_user_id: str,
    ):
        try:

            user_index_name = self.get_index_name_by_prefix(
                "users", original_user_id)

            # Retrieve all documents in a batch
            documents = self.get_documents_batch(
                document_firebase_urls, original_user_id)
            documents_data = []

            for doc in documents:
                if doc:  # Ensure the document exists
                    doc_id = str(doc.uuid)
                    # * reset _additional since it is reserved
                    updated_data = doc.properties
                    if "bot_ids" not in updated_data or updated_data["bot_ids"] is None:
                        updated_data["bot_ids"] = []
                    if original_bot_id not in updated_data["bot_ids"]:
                        updated_data["bot_ids"].append(original_bot_id)

                    documents_data.append((doc_id, updated_data, user_index_name, doc.vector))

            # Perform batch update
            self.update_documents_batch(documents_data, original_user_id)
        except Exception as e:
            logger.error(f"Error assigning documents to bot  {e}")

    def remove_documents_from_bot_by_firebase_url(
            self,
            original_bot_id: str,
            document_firebase_urls: List[str],
            original_user_id: str,
    ):
        try:
            # Retrieve all documents in a batch using the get_documents_batch method
            documents = self.get_documents_batch(
                document_firebase_urls, original_user_id)
            documents_data = []

            for doc in documents:
                if doc:  # Ensure the document exists
                    doc_id = str(doc.uuid)
                    updated_data = doc.properties
                    if "bot_ids" in updated_data:
                        updated_data["bot_ids"] = [
                            bot_id
                            for bot_id in updated_data["bot_ids"]
                            if bot_id != original_bot_id
                        ]

                    documents_data.append(
                        (
                            doc_id,
                            updated_data,
                            self.get_index_name_by_prefix(
                                "users", original_user_id),
                            doc.vector
                        )
                    )

            # Perform batch update
            self.update_documents_batch(documents_data, original_user_id)
        except Exception as e:
            logger.error(f"Error when removing documents from bot: {e}")


    def search_document(self, query: str, original_user_id: str):
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)
        collections = self.we_client.collections.get(
            "Userdocument").with_tenant(user_index_name)
        documents = collections.query.fetch_objects(
            return_properties=[
                "document_number",
                "page_content",
                "document_firebaseurl",
                "document_name",
            ],
            filters=Filter.by_property("page_content").like(query),
            sort=Sort.by_property(name="document_number", ascending=True).by_property(name="document_firebaseurl", ascending=True),
        )

        return [document.properties for document in documents.objects]

    def document_exists(self, document_id: str, original_user_id: str) -> bool:
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)
        try:
            collections = self.we_client.collections.get(
                "Userdocument").with_tenant(user_index_name)
            return collections.data.exists(document_id)

        except Exception as e:
            return False

    def create_document(self, document_datas: List[Any], original_user_id: str):
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)
        collections = self.we_client.collections.get(
            "Userdocument").with_tenant(user_index_name)
        with collections.batch.dynamic() as batch:
            for data_obj in document_datas:
                obj = {
                        "page_content": data_obj.get("page_content", ""),
                        "document_name": data_obj.get("document_name", ""),
                        "document_number": data_obj.get("document_number", 0),
                        "document_firebaseurl": data_obj.get("document_firebaseurl", ""),
                        "user_id": data_obj.get("user_id", ""),
                        "bot_ids": data_obj.get("bot_ids", []),
                    }
                uuid=generate_uuid5(obj)
                batch.add_object(
                    properties=obj,
                    uuid=uuid,
                    vector=data_obj.get("vector", []),
                )

                if batch.number_errors > 0:
                    logger.error(f"Batch operation failed with {batch.number_errors} errors.")
                    failed_objs = self.we_client.batch.failed_objects
                    failed_references = self.we_client.batch.failed_references
                    failed_objs_collection = collections.batch.failed_objects
                    failed_references_collection = collections.batch.failed_references

    def get_documents_by_firebase_urls(
            self, document_firebase_urls: List[str], original_user_id: str
    ):
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)

        # Use a high value or make a preliminary query to determine the number of results
        limit = 10000  # adjust as necessary
        collections = self.we_client.collections.get(
            "Userdocument").with_tenant(user_index_name)
        filter = Filter.by_property("document_firebaseurl").contains_any(document_firebase_urls) if len(
            document_firebase_urls) > 0 else None
        try:
            documents = (
                collections.query.fetch_objects(
                    return_properties=[
                        "document_number",
                        "page_content",
                        "document_firebaseurl",
                        "document_name",
                        "user_id",
                        "bot_ids",
                    ],
                    limit=limit,
                    filters=filter,
                    sort=Sort.by_property(name="document_number", ascending=True).by_property(name="document_firebaseurl", ascending=True),
                )

            )

            # Safely access the nested dictionary, providing default values if any key is missing
            documents_data = documents.objects

            # Return documents data or an empty list if it's not found
            return documents_data if documents_data is not None else []

        except Exception as e:
            # Handle exceptions that might occur during the query
            logger.error(f"An error occurred while fetching documents: {str(e)}")
            return []

    def get_document_ids_by_firebase_urls(
            self, document_firebase_urls: List[str], original_user_id: str
    ) -> List[str]:
        documents = self.get_documents_by_firebase_urls(
            document_firebase_urls, original_user_id
        )
        document_ids = [str(doc.uuid) for doc in documents]

        return document_ids

    def get_document_content_by_firebase_urls(
            self, firebase_urls: List[str], original_user_id: str
    ) -> List[str]:
        documents = self.get_documents_by_firebase_urls(
            firebase_urls, original_user_id)
        document_contents = [doc.properties["page_content"]
                             for doc in documents]

        return document_contents

    def delete_documents_batch(self, documents_data: List[Any]):
        """
        Delete multiple documents using a batch operation.
        :param documents_data: List of tuples (document_id, user_index_name)
        """
        for document_id, user_index_name in documents_data:
            collections = self.we_client.collections.get(
                "Userdocument").with_tenant(user_index_name)
            collections.datat.delete_by_id(document_id)

    def delete_documents(self, document_ids: List[str], original_user_id: str):
        if len(document_ids) == 0:
            return
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)
        # TODO: here since one document may have more than one tenant, so if original_user_id is not None, we simply remove this tenant
        # * if tenant list is empty, we remove this document completely
        # * or if original_user_id is None, we remove this document from all tenants by delete it permanently
        collections = self.we_client.collections.get(
            "Userdocument").with_tenant(user_index_name)
        collections.data.delete_many(
            where=Filter.by_id().contains_any(document_ids)
        )

    def delete_documents_by_firebase_urls(self, firebase_urls: List[str], original_user_id: str):
        document_ids = self.get_document_ids_by_firebase_urls(
            firebase_urls, original_user_id
        )
        self.delete_documents(document_ids, original_user_id)

    def search_document_by_embedding(
            self, embedding_vector, original_user_id: str, original_bot_id: str
    ):
        logger.error("search_document_by_embedding not implemented")
        return {}

    def create_filter(self, bot_id: str, user_id: str, query: str) -> Dict[str, Any]:
        logger.error("create_filter not implemented")
        return {}
