import logging
import time
from typing import List, Dict, Any

from langchain_core.embeddings import Embeddings
from opensearchpy import ConflictError, OpenSearch
from opensearchpy import NotFoundError
from opensearchpy.helpers import bulk

from app.db.vector_database_crud import VectorDatabaseCRUD
from app.utils.util import get_encoded_id


class OpenSearchVectorDatabaseCRUD(VectorDatabaseCRUD):
    def __init__(self, os_client: OpenSearch, embed_func: Embeddings):

        # Generate a unique document ID
        # self.document_id = str(uuid.uuid4())
        # user_id is passed in as parameter
        # Usage:
        self.os_client = os_client
        self.embed_func = embed_func
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger("DiogenesSearch")

    def create_user_index(self, user_id: str):
        # Encode the user_id to base64

        user_index_mappings = {
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 1,
                "index": {"knn": True, "knn.algo_param.ef_search": 100},
            },
            "mappings": {
                "properties": {
                    "original_user_id": {"type": "text"},
                    "document_number": {"type": "integer"},
                    "document_name": {"type": "text"},
                    "page_content": {"type": "text"},
                    "document_firebaseurl": {"type": "keyword"},
                    "vector_field": {
                        "type": "knn_vector",
                        "dimension": 3072,
                        "method": {
                            "name": "hnsw",
                            "space_type": "cosinesimil",
                            "engine": "nmslib",
                            "parameters": {"m": 2048, "ef_construction": 245},
                        },
                    },
                    # New field to represent the association between documents and bots
                    "assigned_bots": {
                        "type": "nested",
                        "properties": {"encoded_bot_id": {"type": "keyword"}},
                    },
                }
            },
        }

        user_index_name = self.get_index_name_by_prefix("users", user_id)

        self.ensure_index_exists(
            index=user_index_name, mappings=user_index_mappings)

    def ensure_index_exists(self, index: str, mappings: Dict[str, Any]):
        if not self.os_client.indices.exists(index):
            self.os_client.indices.create(index=index, body=mappings)

    def get_bot_documents(self, original_bot_id: str, original_user_id: str):
        encoded_bot_id = get_encoded_id(original_bot_id)
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)

        # Define the nested query to search for documents with the specified bot_id in the assigned_bots field
        query = {
            "size": 10000,  # otherwise it will get default 10 documents
            "query": {
                "nested": {
                    "path": "assigned_bots",
                    "query": {"term": {"assigned_bots.encoded_bot_id": encoded_bot_id}},
                    "inner_hits": {},  # Retrieve the matching documents
                }
            },
            "sort": [
                {
                    "document_firebaseurl": {"order": "asc"}
                },  # Sort by document_firebaseurl first
                # Then sort by document_number
                {"document_number": {"order": "asc"}},
            ],
            # Specify the fields to return in the response
            "_source": ["page_content", "document_firebaseurl", "document_number"],
        }

        # Execute the search query on the user index
        response = self.os_client.search(index=user_index_name, body=query)

        # Extract the matching documents from the response
        documents = []
        for hit in response["hits"]["hits"]:
            documents.append(hit["_source"])
        return documents

    # Document CRUD

    def create_document(
            self, document_datas: List[Dict[str, Any]], original_user_id: str
    ):
        index_name = self.get_index_name_by_prefix("users", original_user_id)

        document_ids = []
        try:
            # here index is generated automatically if not exist. we need to check and add mapping if not exist
            self.create_user_index(user_id=original_user_id)
            actions = []
            for document_data in document_datas:
                action = {
                    "_op_type": "create",
                    "_index": index_name,
                    "_source": document_data,
                }
                actions.append(action)

            response = bulk(self.os_client, actions)
            self.logger.error(
                "create_document response(1 for success)" + str(response))
            return
        except ConflictError:
            raise ValueError(
                f"Document with id '{document_data['document_id']}' already exists."
            )

    def update_document(
            self, document_id: str, updated_data: Dict[str, Any], original_user_id: str
    ):
        index_name = self.get_index_name_by_prefix("users", original_user_id)
        self.os_client.update(
            index=index_name, id=document_id, body={"doc": updated_data}
        )

    def assign_documents_to_bot(
            self, original_bot_id: str, document_ids: List[str], original_user_id: str
    ):
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)
        encoded_bot_id = get_encoded_id(original_bot_id)

        # Prepare bulk update actions
        actions = []
        for document_id in document_ids:
            action = {
                "_op_type": "update",
                "_index": user_index_name,
                "_id": document_id,
                "script": {
                    "source": """if (ctx._source.assigned_bots == null) { 
                                   ctx._source.assigned_bots = new ArrayList(); 
                                 }
                                 if (!ctx._source.assigned_bots.stream().anyMatch(bot_id -> bot_id.encoded_bot_id == params.bot_id)) { 
                                   ctx._source.assigned_bots.add(['encoded_bot_id': params.bot_id]); 
                                 }""",
                    "params": {"bot_id": encoded_bot_id},
                },
            }
            actions.append(action)

        # Perform bulk update
        response = bulk(self.os_client, actions)
        self.logger.error(
            f"response when assign document to bot: {str(response)}")

    def remove_documents_from_bot(
            self, original_bot_id: str, document_ids: List[str], original_user_id: str
    ):
        encoded_bot_id = get_encoded_id(original_bot_id)
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)

        actions = []
        for document_id in document_ids:
            action = {
                "_op_type": "update",
                "_index": user_index_name,
                "_id": document_id,
                "script": {
                    "source": "if (ctx._source.assigned_bots != null) { ctx._source.assigned_bots.removeIf(bot -> bot.encoded_bot_id == params.bot_id); }",
                    "params": {"bot_id": encoded_bot_id},
                },
            }
            actions.append(action)

        response = bulk(self.os_client, actions)
        self.logger.error(
            f"response when remove document from bot: {str(response)}")

    def get_documents_by_firebase_urls(
            self, document_firebase_urls: List[str], original_user_id: str
    ):
        self.logger.error("starting get_documents_by_firebase_urls")
        index_name = self.get_index_name_by_prefix("users", original_user_id)

        match_clauses = [
            {"match": {"document_firebaseurl": url}} for url in document_firebase_urls
        ]

        query = {
            "size": 10000,  # other wise it will return 10 documents by default
            "query": {"bool": {"should": match_clauses}},
            "sort": [
                {
                    "document_firebaseurl": {"order": "asc"}
                },  # Sort by document_firebaseurl first
                # Then sort by document_number
                {"document_number": {"order": "asc"}},
            ],
            # Specify the fields to return in the response
            "_source": ["page_content", "document_firebaseurl", "document_number"],
        }
        response = self.os_client.search(index=index_name, body=query)
        return response["hits"]["hits"]

    def get_document_ids_by_firebase_urls(
            self, document_firebase_urls: List[str], original_user_id: str
    ) -> List[str]:
        self.logger.error("starting get_document_ids_by_firebase_urls")
        results = self.get_documents_by_firebase_urls(
            document_firebase_urls, original_user_id
        )
        document_ids = [hit["_id"] for hit in results]
        return document_ids

    def get_document_content_by_firebase_urls(
            self, firebase_urls: List[str], original_user_id: str
    ) -> List[str]:
        self.logger.error("starting get_document_content_by_firebase_urls")
        index_name = self.get_index_name_by_prefix("users", original_user_id)

        queries = []

        for firebase_url in firebase_urls:
            header = {"index": index_name}
            query = {
                "size": 10000,
                "query": {"term": {"document_firebaseurl": firebase_url}},
                "sort": [
                    {
                        "document_firebaseurl": {"order": "asc"}
                    },  # Sort by document_firebaseurl first
                    {
                        "document_number": {"order": "asc"}
                    },  # Then sort by document_number
                ],
                # Specify the fields to return in the response
                "_source": [
                    "page_content",
                    "document_firebaseurl",
                    "document_number",
                ],
            }
            queries.append(header)
            queries.append(query)

        responses = self.os_client.msearch(body=queries)

        document_contents = []
        for response in responses["responses"]:
            hits = response["hits"]["hits"]

            combined_content = ""
            for hit in hits:
                if hit["_source"]["page_content"] is not None:
                    combined_content += hit["_source"]["page_content"]

            document_contents.append(combined_content)

        self.logger.error("ending get_document_content_by_firebase_urls")
        return document_contents

    def delete_documents(self, document_ids: List[str], original_user_id: str):
        index_name = self.get_index_name_by_prefix("users", original_user_id)
        bulk_operations = []

        for document_id in document_ids:
            operation = {"_op_type": "delete",
                         "_index": index_name, "_id": document_id}
            bulk_operations.append(operation)

        if bulk_operations:
            bulk(self.os_client, bulk_operations)

    def delete_documents_by_firebase_urls(
            self, firebase_urls: List[str], original_user_id: str
    ):
        index_name = self.get_index_name_by_prefix("users", original_user_id)
        query = {"query": {"terms": {"document_firebaseurl": firebase_urls}}}

        # Perform the deletion directly using delete_by_query
        response = self.os_client.delete_by_query(index=index_name, body=query)

        return response  # Contains information about the deletion

    def search_document(self, query: str, original_user_id: str):
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)
        search_body = {
            "query": {
                "multi_match": {
                    "query": query,
                    "fields": ["document_name", "page_content"],
                }
            }
        }
        response = self.os_client.search(
            index=user_index_name, body=search_body)
        return response["hits"]["hits"]

    def document_exists(self, document_id: str, original_user_id: str) -> bool:
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)
        try:
            self.os_client.get(index=user_index_name,
                               id=document_id, _source=False)
            return True
        except NotFoundError:
            return False

    def search_document_by_embedding(
            self, embedding_vector, original_user_id: str, original_bot_id: str = None
    ):
        user_index_name = self.get_index_name_by_prefix(
            "users", original_user_id)

        if original_bot_id is not None:
            encoded_bot_id = get_encoded_id(original_bot_id)

            search_body = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "nested": {
                                    "path": "assigned_bots",
                                    "query": {
                                        "term": {
                                            "assigned_bots.encoded_bot_id": encoded_bot_id
                                        }
                                    },
                                }
                            },
                            {
                                "knn": {
                                    "vector_field": {
                                        "vector": embedding_vector,
                                        "k": 10,
                                    }
                                }
                            },
                        ]
                    }
                }
            }
        else:
            search_body = {
                "query": {
                    "knn": {"vector_field": {"vector": embedding_vector, "k": 10}}
                }
            }

        response = self.os_client.search(
            index=user_index_name, body=search_body)
        return response["hits"]["hits"]

    def create_filter(self, bot_id: str, user_id: str, query: str) -> Dict[str, Any]:
        embeddings = self.embed_func
        start_time = time.time()
        self.logger.error("started embedding.embed_query")
        query_embedding_vector = embeddings.embed_query(query)
        end_time = time.time()
        self.logger.error(
            f"finished embedding.embed_query in {end_time - start_time}")

        if bot_id is not None:
            search_filter = {
                "bool": {
                    "must": [
                        {
                            "nested": {
                                "path": "assigned_bots",
                                "query": {
                                    "term": {
                                        "assigned_bots.encoded_bot_id": get_encoded_id(
                                            bot_id
                                        )
                                    }
                                },
                            }
                        },
                        {
                            "knn": {
                                "vector_field": {
                                    "vector": query_embedding_vector,
                                    "k": 10,
                                }
                            }
                        },
                    ]
                }
            }
        else:
            search_filter = {
                "knn": {"vector_field": {"vector": query_embedding_vector, "k": 10}}
            }

        return search_filter
