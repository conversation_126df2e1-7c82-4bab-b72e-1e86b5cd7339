# # TODO: use <PERSON><PERSON><PERSON><PERSON>
# from typing import List, Any, Dict

# from pymilvus import CollectionSchema, FieldSchema, DataType

# from app.db.vector_database_crud import VectorDatabaseCRUD


# class MilvusVectorDatabaseCRUD(VectorDatabaseCRUD):
#     def get_bot_documents(self, original_bot_id: str, original_user_id: str):
#         pass

#     def assign_documents_to_bot(self, original_bot_id: str, document_ids: List[str], original_user_id: str):
#         pass

#     def remove_documents_from_bot(self, original_bot_id: str, document_ids: List[str], original_user_id: str):
#         pass

#     def get_document_ids_by_firebase_urls(self, document_firebase_urls: List[str], original_user_id: str) -> List[str]:
#         pass

#     def get_document_content_by_firebase_urls(self, firebase_urls: List[str], original_user_id: str) -> List[str]:
#         pass

#     def delete_documents_by_firebase_urls(self, firebase_urls: List[str], original_user_id: str):
#         pass

#     def search_document_by_embedding(self, embedding_vector, original_user_id: str, original_bot_id: str):
#         pass

#     def create_filter(self, bot_id: str, user_id: str, query: str) -> Dict[str, Any]:
#         pass

#     def __init__(self, milvus_client) -> None:
#         self.milvus_client = milvus_client

#     def create_user_index(self, user_id: str):
#         # Create a collection for the user
#         collection_name = f"user_{user_id}"
#         if not self.milvus_client.has_collection(collection_name):
#             schema = CollectionSchema(
#                 fields=[
#                     FieldSchema(name="document_id",
#                                 dtype=DataType.INT64, is_primary=True),
#                     FieldSchema(name="page_content", dtype=DataType.STRING),
#                     FieldSchema(name="document_name", dtype=DataType.STRING),
#                     FieldSchema(name="document_firebaseurl",
#                                 dtype=DataType.STRING),
#                     FieldSchema(name="user_id", dtype=DataType.STRING),
#                     FieldSchema(name="bot_ids", dtype=DataType.STRING),
#                 ]
#             )
#             self.milvus_client.create_collection(collection_name, schema)

#     def ensure_index_exists(self, index: str, mappings):
#         # Ensure the index exists (Milvus handles this differently)
#         pass

#     def create_document(self, document_datas: List[Any], original_user_id: str):
#         collection_name = f"user_{original_user_id}"
#         if self.milvus_client.has_collection(collection_name):
#             self.milvus_client.insert(collection_name, document_datas)

#     def update_document(self, document_id: str, updated_data, original_user_id: str):
#         collection_name = f"user_{original_user_id}"
#         if self.milvus_client.has_collection(collection_name):
#             self.milvus_client.update(
#                 collection_name, document_id, updated_data)

#     def delete_documents(self, document_ids: List[str], original_user_id: str):
#         collection_name = f"user_{original_user_id}"
#         if self.milvus_client.has_collection(collection_name):
#             self.milvus_client.delete(collection_name, document_ids)

#     def get_documents_by_firebase_urls(self, document_firebase_urls: List[str], original_user_id: str):
#         collection_name = f"user_{original_user_id}"
#         if self.milvus_client.has_collection(collection_name):
#             return self.milvus_client.query(collection_name, document_firebase_urls)

#     def search_document(self, query: str, original_user_id: str):
#         collection_name = f"user_{original_user_id}"
#         if self.milvus_client.has_collection(collection_name):
#             return self.milvus_client.search(collection_name, query)

#     def document_exists(self, document_id: str, original_user_id: str) -> bool:
#         collection_name = f"user_{original_user_id}"
#         if self.milvus_client.has_collection(collection_name):
#             return self.milvus_client.has_document(collection_name, document_id)
