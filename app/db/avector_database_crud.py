from abc import ABC<PERSON><PERSON>, abstractmethod
from typing import Any, Dict, List

from app.utils.util import get_encoded_id


# TODO: use async client

class AVectorDatabaseCRUD(metaclass=ABCMeta):
    async def get_index_name_by_prefix(self, prefix: str, original_id: str):
        encoded_id = get_encoded_id(original_id)
        return f"{prefix}_{encoded_id}"

    @abstractmethod
    async def create_user_index(self, user_id: str):
        pass

    @abstractmethod
    async def ensure_index_exists(self, index: str, mappings):
        pass

    @abstractmethod
    async def get_bot_documents(self, original_bot_id: str, original_user_id: str):
        pass

    @abstractmethod
    async def create_document(self, document_datas: List[Any], original_user_id: str):
        pass

    @abstractmethod
    async def update_document(self, document_id: str, updated_data, original_user_id: str):
        pass

    @abstractmethod
    async def assign_documents_to_bot(self, original_bot_id: str, document_ids: List[str], original_user_id: str):
        pass

    @abstractmethod
    async def remove_documents_from_bot(
            self, original_bot_id: str, document_ids: List[str], original_user_id: str
    ):
        pass

    async def assign_documents_to_bot_by_firebase_url(
            self,
            original_bot_id: str,
            document_firebase_urls: List[str],
            original_user_id: str,
    ):
        # Find documents by Firebase URLs
        document_ids = self.get_document_ids_by_firebase_urls(
            document_firebase_urls, original_user_id
        )

        self.assign_documents_to_bot(original_bot_id, document_ids, original_user_id)

    async def remove_documents_from_bot_by_firebase_url(
            self,
            original_bot_id: str,
            document_firebase_urls: List[str],
            original_user_id: str,
    ):
        # Find documents by Firebase URLs
        document_ids = self.get_document_ids_by_firebase_urls(
            document_firebase_urls, original_user_id
        )

        self.remove_documents_from_bot(original_bot_id, document_ids, original_user_id)

    @abstractmethod
    async def get_documents_by_firebase_urls(self, document_firebase_urls: List[str], original_user_id: str):
        pass

    @abstractmethod
    async def get_document_ids_by_firebase_urls(
            self, document_firebase_urls: List[str], original_user_id: str
    ) -> List[str]:
        pass

    @abstractmethod
    async def get_document_content_by_firebase_urls(
            self, firebase_urls: List[str], original_user_id: str
    ) -> List[str]:
        pass

    @abstractmethod
    async def delete_documents(self, document_ids: List[str], original_user_id: str):
        pass

    @abstractmethod
    async def delete_documents_by_firebase_urls(self, firebase_urls: List[str], original_user_id: str):
        pass

    @abstractmethod
    async def search_document(self, query: str, original_user_id: str):
        pass

    @abstractmethod
    async def document_exists(self, document_id: str, original_user_id: str) -> bool:
        pass

    @abstractmethod
    async def search_document_by_embedding(
            self, embedding_vector, original_user_id: str, original_bot_id: str
    ):
        pass

    @abstractmethod
    async def create_filter(self, bot_id: str, user_id: str, query: str) -> Dict[str, Any]:
        """_summary_

        Args:
            bot_id (_type_): _description_
            user_id (_type_): _description_
            query (_type_): _description_

        Returns:
            Dict[str, Any]: _description_
        """
        return {}
