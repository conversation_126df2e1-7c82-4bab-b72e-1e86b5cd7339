import re


def extract_outline(markdown_text):
    if not markdown_text:
        return None

    # Regex pattern to match Markdown headings
    heading_pattern = re.compile(r'^(#{1,6})\s+(.*)', re.MULTILINE)

    # Dictionary to store the outline
    outline = {}

    # Find all headings
    headings = heading_pattern.findall(markdown_text)

    if not headings:
        return None

    # Build the outline
    for level, title in headings:
        level_number = len(level)
        if level_number not in outline:
            outline[level_number] = []
        outline[level_number].append(title.strip())

    return outline
