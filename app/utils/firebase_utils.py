import os
from datetime import datetime

from google.cloud.firestore_v1 import <PERSON><PERSON><PERSON><PERSON>
from google.cloud.storage import Client

from app.config.basic_config import BUCKET_NAME
from app.core.db_config import get_db
from app.core.tracing_setup import logger


async def update_progress(progress_ref, progress_data):
    await progress_ref.set(progress_data)  # Create or update the document


async def upload_to_firebase(file_path, file_content, blob_name, storage: Client):
    storage_bucket = storage.bucket(BUCKET_NAME)
    os.makedirs(os.path.dirname(file_path), exist_ok=True)  # Create the directory if it doesn't exist
    if file_content is not None:
        with open(file_path, 'w') as f:
            f.write(file_content)
    logger.info(f"Uploading {file_path} to {blob_name} in the storage bucket.")
    blob = storage_bucket.blob("documents/" + blob_name)
    blob.upload_from_filename(file_path)  # This will create the path if it doesn't exist


db: AsyncClient = get_db()


async def update_session_progress(user_id:str, session_id:str, topic:str, status:str, type: str, err:str=None):
    progress_data = {
        "user_id": user_id,
        "topic": topic,
        "session_id": session_id,
        "status": status,
        "timestamp": str(datetime.utcnow()),
        "type":type,
        "error":err,
    }
    progress_ref = db.collection("progress").document(user_id).collection("sessions").document(session_id)
    await progress_ref.set(progress_data)
