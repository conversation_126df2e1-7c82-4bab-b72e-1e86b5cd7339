"""
Utility module for creating and managing language model chains and agents.

This module provides functions to create and configure various language model
chains and agents using LangChain components.
"""

import uuid
from typing import List, Dict, Any

from langchain.agents import AgentExecutor, ZeroShotAgent, create_tool_calling_agent, Agent, create_json_chat_agent, \
    create_openai_tools_agent
from langchain.chains.base import Chain
from langchain.chains.conversational_retrieval.base import ConversationalRetrievalChain
from langchain.chains.llm_math.base import LLMMathChain
from langchain.chains.qa_with_sources.retrieval import RetrievalQAWithSourcesChain
from langchain.chains.retrieval_qa.base import RetrievalQA
from langchain.memory import ConversationBufferMemory
from langchain_chroma import Chroma
from langchain_community.agent_toolkits.load_tools import load_tools
from langchain_community.retrievers import WebResearchRetriever
from langchain_community.tools import YouTubeSearchTool, WikipediaQueryRun
from langchain_community.utilities import WikipediaAPIWrapper
from langchain_core.memory import BaseMemory
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable
from langchain_core.tools import Tool
from langchain_core.vectorstores import VectorStoreRetriever, VectorStore
from langchain_google_community import GoogleSearchAPIWrapper, GooglePlacesTool
from langchain_openai import ChatOpenAI

from app.config.basic_config import default_model, embed_func
from app.config.config import chatgpt_basic
from app.utils.custom_out_parser import CustomOutputParser


async def make_agent(
        retriever: VectorStoreRetriever,
        chromavectorstore: VectorStore,
        memory: BaseMemory,
        default_model: str = default_model,
) -> Chain:
    """
    Create and configure an agent with various tools and chains.

    Args:
        retriever (VectorStoreRetriever): The retriever for the knowledge base.
        chromavectorstore (VectorStore): The vector store for web research.
        memory (BaseMemory): The memory component for the agent.
        default_model (str): The default language model to use.

    Returns:
        Chain: The configured agent chain.
    """
    chatgpt = chatgpt_basic

    chatgpt_no_web_response = ChatOpenAI(
        model=default_model, streaming=True, callbacks=[], verbose=True, temperature=0,
    )

    # Configure tools
    tools = _configure_tools(chatgpt, chatgpt_no_web_response, retriever, chromavectorstore, memory)

    # Create agent
    agent = _create_zero_shot_agent(chatgpt, tools)

    # Create agent executor
    mrkl = AgentExecutor(
        agent=agent,
        tools=tools,
        verbose=True,
        memory=memory,
        handle_parsing_errors="Check your output and make sure it conforms!",
    )

    # Use conversational retrieval chain for better retrieval quality
    conversation_retrieval_qa_chain = ConversationalRetrievalChain.from_llm(
        llm=chatgpt_no_web_response,
        chain_type="stuff",
        verbose=True,
        retriever=retriever,
        memory=memory,
        return_source_documents=True,
    )

    return conversation_retrieval_qa_chain


def _configure_tools(
        chatgpt: ChatOpenAI,
        chatgpt_no_web_response: ChatOpenAI,
        retriever: VectorStoreRetriever,
        chromavectorstore: VectorStore,
        memory: BaseMemory,
) -> List[Tool]:
    """
    Configure and return a list of tools for the agent.

    Args:
        chatgpt (ChatOpenAI): The main language model.
        chatgpt_no_web_response (ChatOpenAI): The language model for non-web responses.
        retriever (VectorStoreRetriever): The retriever for the knowledge base.
        chromavectorstore (VectorStore): The vector store for web research.
        memory (BaseMemory): The memory component for the agent.

    Returns:
        List[Tool]: A list of configured tools.
    """
    llm_math_chain = LLMMathChain.from_llm(llm=chatgpt, verbose=True)
    retrieval_qa_chain = RetrievalQA.from_chain_type(
        llm=chatgpt_no_web_response,
        chain_type="stuff",
        verbose=True,
        retriever=retriever,
        memory=memory,
    )
    search = GoogleSearchAPIWrapper()
    youtube = YouTubeSearchTool()
    wikipedia = WikipediaQueryRun(api_wrapper=WikipediaAPIWrapper())
    web_research_retriever = WebResearchRetriever.from_llm(
        vectorstore=chromavectorstore,
        llm=chatgpt_no_web_response,
        search=search,
        allow_dangerous_requests=True,
    )
    web_research_qa_chain = RetrievalQAWithSourcesChain.from_chain_type(
        llm=chatgpt_no_web_response, retriever=web_research_retriever, verbose=True
    )
    places = GooglePlacesTool()

    tools = [
        Tool(
            name="Calculator",
            func=llm_math_chain.run,
            description="Useful when you need to answer questions about math",
        ),
        Tool(
            name="KnowledgeBase",
            func=retrieval_qa_chain.invoke,
            description="You should always check this Knowledge Base first to see if you can find answer here before use search engine also please include sources of answer(which document and position of answer is from) in your answer.",
        ),
        Tool(
            name="Wikipedia",
            description="Search Wikipedia for high quality information. Check this before use search engine and web search",
            func=wikipedia.run,
        ),
        Tool(
            name="WebResearchTool",
            func=web_research_qa_chain.invoke,
            description="useful when doing web research online for a query, it will search multiple steps to find better answer on the internet",
        ),
        Tool(
            name="GooglePlaces",
            description="A tool for Google Places. Useful for when you need to validate or discover addressed from ambiguous text. Input should be a search query.",
            func=places.run,
        ),
        Tool(
            name="YoutubeSearch",
            description="Search Youtube for recent videos.",
            func=youtube.run,
        ),
    ]
    extra_tools = load_tools(["arxiv", "dalle-image-generator"])
    tools.extend(extra_tools)
    return tools


def _create_zero_shot_agent(chatgpt: ChatOpenAI, tools: List[Tool]) -> Agent:
    """
    Create a ZeroShotAgent with the given language model and tools.

    Args:
        chatgpt (ChatOpenAI): The language model to use.
        tools (List[Tool]): The list of tools for the agent.

    Returns:
        ZeroShotAgent: The created agent.
    """
    prefix = """Have a conversation with a human, answering the following questions as best you can and Final Answer should always include all relevant details found for user's question. Your response style is {character}.----Chat history begin------:  {chat_history} ----Chat history end----- You have access to the following tools(priority of each tool is the same as the order in the list, e.g: always prefer Knowledge Base first over Wikipedia and Google Search):"""
    suffix = """Begin!"
    
Question: {input}
{agent_scratchpad}"""
    output_parser = CustomOutputParser()
    return ZeroShotAgent.from_llm_and_tools(
        llm=chatgpt,
        tools=tools,
        prefix=prefix,
        suffix=suffix,
        input_variables=["input", "chat_history", "agent_scratchpad", "character"],
        output_parser=output_parser,
        verbose=True,
    )


async def prepare_agent_exe(model: str, max_tokens: int, temperature: float, messages: List[Dict[str, Any]],
                            session_id_: str
                            ) -> Runnable:
    """
    Prepare an agent executor with the specified configuration, using chat history as memory.

    Args:
        model (str): The name of the language model to use.
        max_tokens (int): The maximum number of tokens for the model output.
        temperature (float): The temperature for the language model.
        messages (List[Dict[str, Any]]): The list of messages for the agent's prompt.

    Returns:
        Chain: The configured agent executor.
    """
    # Initialize the chat model
    openaichat = chatgpt_basic

    # Set up tools for the agent
    tools=[]
    tools = _configure_tools_for_executor(openaichat)

    # Create a memory object to store chat history
    memory = ConversationBufferMemory(return_messages=True)

    chat_messages = []

    # Load the previous messages into memory
    for message in messages:
        memory.chat_memory.add_user_message(message["content"]) if message[
                                                                       "role"] == "user" else memory.chat_memory.add_ai_message(
            message["content"])
        chat_messages.append(( "ai" if message["role"] != "user" else "user" , message["content"]))

    # Create an output parser (if custom one is needed)
    output_parser = CustomOutputParser()

    # Define the agent's prompt with memory included
    prefix = """Answer the following questions as best you can. You have access to the following tools (use dalle tool only if necessary; if used, include the file link in the final answer):"""
    prompt_template = ChatPromptTemplate([("system", prefix),
                                                        ("placeholder","{chat_history}"),
                                                        ("placeholder","{tools}"),
                                                        ("placeholder","{tool_names}"),
                                                        ("human", "{input}"), ("placeholder", "{agent_scratchpad}"), ])

    # Initialize the agent with memory

    agent = create_tool_calling_agent(llm=openaichat,
                                      tools=tools,
                                      prompt=prompt_template, )

    # Create the agent executor with memory
    agent_exe: Runnable = AgentExecutor(agent=agent, tools=tools, verbose=True)

    return agent_exe


def _configure_tools_for_executor(openaichat: ChatOpenAI) -> List[Tool]:
    """
    Configure and return a list of tools for the agent executor.

    Args:
        openaichat (ChatOpenAI): The language model to use for tool configuration.

    Returns:
        List[Tool]: A list of configured tools.
    """
    search = GoogleSearchAPIWrapper()
    wikipedia = WikipediaQueryRun(api_wrapper=WikipediaAPIWrapper())
    places = GooglePlacesTool()
    youtube = YouTubeSearchTool()

    chromavectorstore = Chroma(
        embedding_function=embed_func,
        persist_directory=f"./chromadb/chromadb_{uuid.uuid4()}",
    )
    chat_llm = ChatOpenAI(model=chatgpt_basic.model_name, max_tokens=chatgpt_basic.max_tokens,
                      streaming=chatgpt_basic.streaming)
    web_research_retriever = WebResearchRetriever.from_llm(
        vectorstore=chromavectorstore,
        llm=chat_llm,
        search=search,
        allow_dangerous_requests=True,
    )
    web_research_qa_chain = RetrievalQAWithSourcesChain.from_chain_type(
        llm=openaichat, retriever=web_research_retriever, verbose=True
    )

    tools = [
        Tool(
            name="WebResearchTool",
            func=web_research_qa_chain.invoke,
            description="useful when doing web research online for a query, it will search multiple steps to find better answer on the internet",
        ),
        Tool(
            name="GoogleSearch",
            description="Search Google for recent results.",
            func=search.run,
        ),
        Tool(
            name="Wikipedia",
            description="Search Wikipedia for high quality information. Check this before use search engine and web search",
            func=wikipedia.run,
        ),
        Tool(
            name="googlePlaces",
            description="A tool for Google Places. Useful for when you need to validate or discover addressed from ambiguous text. Input should be a search query.",
            func=places.run,
        ),
        Tool(
            name="YoutubeSearch",
            description="Search Youtube for recent videos.",
            func=youtube.run,
        ),
    ]
    extra_tools = load_tools(["arxiv", "dalle-image-generator"])
    tools.extend(extra_tools)
    return tools
