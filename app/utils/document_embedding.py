import os
import re
import shutil
import tempfile
import time
from typing import List, Any

from langchain_community.document_loaders import (
    ArxivLoader,
    AsyncHtmlLoader,
    CSVLoader,
    GitLoader,
    PyPDFLoader,
    UnstructuredEPubLoader,
    UnstructuredEmailLoader,
    UnstructuredExcelLoader,
    UnstructuredFileLoader,
    UnstructuredImageLoader,
    UnstructuredMarkdownLoader,
    UnstructuredODTLoader,
    UnstructuredPowerPointLoader,
    UnstructuredRSTLoader,
    UnstructuredTSVLoader,
    UnstructuredWordDocumentLoader,
    UnstructuredXMLLoader,
    # FireCrawlLoader,
)
from langchain_community.document_transformers import BeautifulSoupTransformer
from langchain_core.documents import Document
from langchain_core.embeddings import Embeddings
from langchain_text_splitters import (
    Language,
    RecursiveCharacterTextSplitter,
)

from app.core.db_config import firebase_storage
from app.core.tracing_setup import logger
from app.db.vector_database_crud import VectorDatabaseCRUD
from app.utils.util import parse_firebase_url


class DocumentEmbedding:

    # TODO: add more document format support, and git
    # TODO: need task queue for large documents

    def get_user_temp_dir(self, user_id):
        if user_id in self.user_temp_dirs:
            return self.user_temp_dirs[user_id]

        temp_dir = f"/tmp/{user_id}/"
        os.makedirs(temp_dir, exist_ok=True)
        self.user_temp_dirs[user_id] = temp_dir

        return temp_dir

    def __init__(self, vectordb: VectorDatabaseCRUD, embed_func: Embeddings):

        # Generate a unique document ID
        # self.document_id = str(uuid.uuid4())
        # user_id is passed in as parameter

        self.storage_client = firebase_storage

        self.user_temp_dirs = {}
        self.vectordb = vectordb
        self.embed_func = embed_func

    async def embed_document_from_file_url(
            self,
            file_urls: List[str],
            user_id: str,
            update_on_exist=False,
            optional_predefined_content_tofill=[],
    ):
        """
        Embed documents from file URLs.

        :param file_urls: List of file URLs
        :param user_id: User ID
        :param update_on_exist: Update if the document exists, default to False
        """
        total_start_time = time.time()

        new_documents_count = 0
        new_embeddings_count = 0
        dir_path = self.get_user_temp_dir(user_id)

        for j in range(len(file_urls)):
            file_url = file_urls[j]
            # Handle different Firebase URL patterns
            bucket_name, blob_name, document_name, file_extension = parse_firebase_url(
                file_url
            )

            # Treat as a normal web URL if bucket_name is None
            bucket, blob = self.get_bucket_and_blob(bucket_name, blob_name)

            temp_file = tempfile.NamedTemporaryFile(delete=True, dir=dir_path)
            documents: List[Document] = []
            language = ""
            # if optional_predefined_content_tofill is not null,
            if (
                    len(optional_predefined_content_tofill) > j
                    and optional_predefined_content_tofill[j] is not None
                    and optional_predefined_content_tofill[j] != ""
            ):
                documents = [
                    Document(
                        page_content=optional_predefined_content_tofill[j])
                ]
            else:
                language, documents = await self.load_documents(
                    file_extension, bucket, blob, temp_file, file_url, user_id
                )

            if len(documents) == 0:
                logger.info("Documents not loaded. return")
                return

            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=2000,
                chunk_overlap=100,
                length_function=len,
                is_separator_regex=False,
            )

            if language == "python":
                text_splitter = RecursiveCharacterTextSplitter.from_language(
                    language=Language.PYTHON,
                    chunk_size=2000,
                    chunk_overlap=100,
                    length_function=len,
                    is_separator_regex=False,
                )

            splitted_documents = text_splitter.split_documents(documents)
            texts = [doc.page_content for doc in splitted_documents]

            new_documents_count += len(texts)

            try:
                # * make sure index exists
                self.vectordb.create_user_index(user_id)
                results = self.vectordb.get_document_ids_by_firebase_urls(
                    [file_url], user_id
                )
                logger.info(
                    "Try to find it first. if exist, update, else create")

                if len(results) > 0 and not update_on_exist:
                    # Do not need to do anything for this document
                    continue
                else:
                    self.vectordb.delete_documents(results, user_id)

                # Embed and store the document
                await self.embed_and_store_documents(
                    texts, file_url, document_name, user_id
                )
                new_embeddings_count += len(texts)

            except Exception as e:
                logger.error("Error processing query: %s", str(e))

            finally:
                logger.info(
                    "Finally processing embedded documents, temp file not removed"
                )

        logger.info("New documents count: %s", new_documents_count)
        logger.info("New embeddings count: %s", new_embeddings_count)
        logger.info(
            "Time to embed documents: %s seconds", time.time() - total_start_time
        )

    def get_bucket_and_blob(self, bucket_name: str, blob_name: str):
        bucket = None
        blob = None

        if bucket_name is not None:
            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(blob_name)

        return bucket, blob

    async def load_documents(
            self,
            file_extension: str,
            bucket: str,
            blob: str,
            temp_file: str,
            file_url: str,
            user_id: str,
    ) -> tuple[str, list[Document] | Any]:
        start_time = time.time()

        language = ""

        if bucket is not None:
            # Load documents from Firebase
            documents = await self.load_documents_from_firebase(
                bucket, blob, temp_file, file_extension
            )
        else:
            # Load documents from normal web URL
            language, documents = await self.load_documents_from_web(
                file_url, user_id)

        logger.info(
            "Time to load documents: %s seconds    ", time.time() - start_time
        )

        return language, documents

    async def load_documents_from_firebase(
            self, bucket: str, blob: str, temp_file: str, file_extension: str
    ) -> List[Document]:
        start_time = time.time()
        with open(temp_file.name, "wb") as f:
            # Download from Firebase
            blob.download_to_file(f)

        logger.info("Time to download blob: %s seconds",
                     time.time() - start_time)

        # Load documents using appropriate loader based on file extension
        if file_extension == ".pdf":
            loader = PyPDFLoader(temp_file.name)
        elif file_extension == ".docx" or file_extension == ".doc":
            loader = UnstructuredWordDocumentLoader(temp_file.name)
        elif file_extension == ".epub":
            loader = UnstructuredEPubLoader(temp_file.name)
        elif file_extension == ".csv":
            loader = CSVLoader(temp_file.name)
        elif file_extension == ".xlsx" or file_extension == ".xls":
            loader = UnstructuredExcelLoader(temp_file.name, mode="elements")
        #     TODO: need to embed image
        elif file_extension == ".jpg":
            loader = UnstructuredImageLoader(temp_file.name)
        elif file_extension == ".ppt" or file_extension == ".pptx":
            loader = UnstructuredPowerPointLoader(temp_file.name)
        elif file_extension == ".xml":
            loader = UnstructuredXMLLoader(temp_file.name)
        elif file_extension == ".tsv":
            loader = UnstructuredTSVLoader(temp_file.name)
        elif file_extension == ".md":
            loader = UnstructuredMarkdownLoader(temp_file.name)
        elif file_extension == ".rst":
            loader = UnstructuredRSTLoader(
                file_path=temp_file.name, mode="elements")
        elif file_extension == ".odt":
            loader = UnstructuredODTLoader(temp_file.name)
        elif file_extension == ".eml":
            loader = UnstructuredEmailLoader(temp_file.name)
        #     TODO: need to handle audio, video embedding separately
        else:
            loader = UnstructuredFileLoader(temp_file.name)

        return await loader.aload()

    async def load_documents_from_web(
            self, file_url: str, user_id: str
    ) -> tuple[str, list[Document]]:
        """
        Loads documents from a web URL based on the URL type.

        Parameters:
        - file_url (str): The URL of the web page to load documents from.
        - user_id (str): The ID of the user associated with the documents.

        Returns:
        - tuple[str, list[Document]]: A tuple containing the language of the documents and the list of documents.

        This function checks if the URL is an arxiv URL or a GitHub repository URL. If it's an arxiv URL, it extracts the arxiv ID and uses the ArxivLoader to load the documents. If it's a GitHub repository URL, it extracts the repository name and creates a unique temporary directory. It then ensures the directory doesn't exist or cleans it if it does. It uses the GitLoader to load the documents from the GitHub repository. If the URL is neither an arxiv URL nor a GitHub repository URL, it uses the AsyncHtmlLoader to load the documents.

        The function returns a tuple containing the language of the documents (currently always an empty string) and the list of documents.
        """

        # Check if the URL is an arxiv URL
        arxiv_pattern = r"https?://arxiv\.org/abs/(\d+\.\d+)"
        match_arxiv = re.search(arxiv_pattern, file_url)

        # Check if the URL is a GitHub repository URL
        git_pattern = r"https?://github\.com/([^/]+)/([^/]+)/?"
        match_git = re.search(git_pattern, file_url)

        if match_arxiv:
            # If arxiv URL, get the arxiv id and use ArxivLoader
            arxiv_id = match_arxiv.group(1)
            loader = ArxivLoader(query=arxiv_id, load_max_docs=2)
            docs = await loader.aload()
            return "", docs

        elif match_git:
            # Extract the repo name from the matched git URL
            user_name = match_git.group(1)
            repo_name = match_git.group(2)
            unique_temp_path = f"{user_id}_{user_name}_{repo_name}"
            unique_temp_dir = os.path.join(
                tempfile.gettempdir(), unique_temp_path)

            # Ensure directory doesn't exist or clean it if it does
            if os.path.exists(unique_temp_dir):
                shutil.rmtree(unique_temp_dir)
            os.makedirs(unique_temp_dir)

            # If GitHub repository URL, use GitLoader
            #  TODO: or maybe use branch main
            loader = GitLoader(
                clone_url=file_url,
                repo_path=unique_temp_dir,
                branch="master",
                file_filter=lambda file_path: file_path.endswith(
                    (
                        ".py",
                        ".js",
                        ".html",
                        ".css",
                        ".java",
                        ".c",
                        ".cpp",
                        ".go",
                        ".rs",
                        ".swift",
                        ".ts",
                        ".php",
                    )
                ),
            )

            # # try to clone and load it
            # repo = Repo.clone_from(file_url, to_path=unique_temp_dir)
            # loader = GenericLoader.from_filesystem(
            #     unique_temp_dir,
            #     glob="**/*",
            #     suffixes=[".py"],
            #     parser=LanguageParser(language=Language.PYTHON, parser_threshold=500),
            # )
            docs = await loader.aload()
            return "python", docs

        else:
            # Else use your default loader
            loader = AsyncHtmlLoader(file_url)
            docs = await loader.aload()
            # TODO: need to fix firecrawl: 1. empty queue failed
            # loader = FireCrawlLoader(
            #     url=file_url,
            #     api_key=os.environ["FIRECRAWL_API_KEY"],
            #     api_url=os.environ["FIRECRAWL_API_URL"],
            #     mode="scrape",
            # )
            #
            # docs = await loader.aload()
            # html2text = Html2TextTransformer()
            # docs_transformed = html2text.transform_documents(docs)
            transformer = BeautifulSoupTransformer()
            docs_transformed = transformer.transform_documents(docs)
            # TODO: need to add to determine if url is git/arxiv/youtube:

            # TODO: need to load web site instead of a single page
            # loader = WebBaseLoader(file_url)

            return "", docs_transformed

    async def embed_and_store_documents(
            self, texts: List[str], file_url: str, document_name: str, user_id: str
    ):
        """
        Embeds and stores documents using the provided embeddings function.

        Parameters:
        - texts (List[str]): A list of text documents to be embedded.
        - file_url (str): The URL of the file from which the documents were loaded.
        - document_name (str): The name of the document.
        - user_id (str): The ID of the user associated with the documents.

        Returns:
        - List[List[float]]: A list of embeddings for each document.

        This function performs the following steps:
        1. Measures the time taken to embed the documents.
        2. Embeds the documents using the provided embeddings function.
        3. Logs the time taken to embed the documents.
        4. Logs the process of embedding and storing the documents.
        5. Measures the time taken to create the documents.
        6. Creates a list of document data dictionaries.
        7. Stores the documents in the vector database using the create_document method.
        8. Logs the time taken to create the documents.
        9. Returns the list of document embeddings.
        """
        start_time = time.monotonic()
        embeddings = self.embed_func
        document_embeddings = await embeddings.aembed_documents(texts)
        end_time = time.monotonic()
        logger.info(
            f"Time taken to embed documents {file_url}: {end_time - start_time} seconds"
        )

        logger.info(f"Embedding and storing document for file_url {file_url}")

        start_time = time.monotonic()
        document_datas = [
            {
                "original_user_id": user_id,
                "user_id": user_id,
                "document_number": i,
                "document_name": document_name,
                "page_content": texts[i],
                "document_firebaseurl": file_url,
                "vector_field": document_embeddings[i],
                "vector": document_embeddings[i],
                "bot_ids": [],
            }
            for i in range(len(texts))
        ]
        self.vectordb.create_document(document_datas, user_id)
        end_time = time.monotonic()
        logger.info(
            f"Time taken to create documents {file_url}: {end_time - start_time} seconds"
        )
