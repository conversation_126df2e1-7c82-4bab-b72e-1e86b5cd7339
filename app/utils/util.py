"""
Utility module containing various helper functions for the application.

This module provides utility functions for parsing URLs, handling Firebase data,
processing web content, and managing conversations and messages.
"""

import base64
import json
import os
import re
import tempfile
import time
from datetime import datetime
from typing import Dict, Any, Tuple, Optional, Union
from urllib.parse import unquote, urlsplit

import html2text
import requests
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
from fastapi import HTTPException
from google.cloud.firestore_v1 import AsyncClient
from langchain_community.chat_message_histories import ChatMessageHistory
from langchain_core.chat_history import BaseChatMessageHistory
from langchain_core.messages import ChatMessage
from starlette.requests import Request

from app.core.tracing_setup import logger
from app.models.model import AaskRequestData, AchatRequestData, WritingRequestData

# Import markdown for parse_markdown function
try:
    import markdown
except ImportError:
    markdown = None

# Import storage for upload_document_to_firebase function
try:
    from app.core.db_config import get_storage
except ImportError:
    get_storage = None


def get_main_content(url: str) -> str:
    """
    Fetch and extract the main content from a given URL.

    Args:
        url (str): The URL to fetch content from.

    Returns:
        str: The extracted main content or an error message.
    """
    user_agent = UserAgent()
    headers = {"User-Agent": user_agent.random}

    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.text, "html.parser")

    main_content = (
        soup.find("article")
        or soup.find("main")
        or soup.find("div", {"class": "main-content"})
        or soup.find("div", {"id": "main-content"})
    )

    if main_content:
        for script in main_content.find_all(["script", "style"]):
            script.decompose()
        return main_content.prettify()
    else:
        return "Main content not found"


def save_main_content_to_temp_file(url: str) -> str:
    """
    Save the main content of a URL to a temporary file.

    Args:
        url (str): The URL to fetch content from.

    Returns:
        str: The path to the temporary file containing the content.
    """
    main_content = get_main_content(url)
    with tempfile.NamedTemporaryFile(delete=False, suffix=".html") as temp_file:
        temp_file.write(main_content.encode("utf-8"))
        return temp_file.name


def parse_firebase_url(
    url: str,
) -> Tuple[Optional[str], Optional[str], Optional[str], Optional[str]]:
    """
    Parse a Firebase storage URL to extract relevant components.

    Args:
        url (str): The Firebase storage URL to parse.

    Returns:
        Tuple[Optional[str], Optional[str], Optional[str], Optional[str]]:
        A tuple containing bucket_name, blob_name, document_name, and file_extension.
    """
    if url.startswith("gs://"):
        components = url[5:].split("/")
        bucket_name = components[0]
        blob_name = "/".join(components[1:])
    else:
        components = urlsplit(url)
        if components.netloc == "firebasestorage.googleapis.com":
            path_parts = unquote(components.path).split("/")
            bucket_name = path_parts[3] if len(path_parts) > 2 else ""
            blob_name = "/".join(path_parts[5:])
        else:
            return None, None, None, None

    document_name = os.path.basename(blob_name)
    file_extension = os.path.splitext(document_name)[1]

    return bucket_name, blob_name, document_name, file_extension


def extract_main_content(url: str) -> str:
    """
    Extract the main content from a webpage as plain text.

    Args:
        url (str): The URL of the webpage.

    Returns:
        str: The extracted plain text content.
    """
    response = requests.get(url)
    soup = BeautifulSoup(response.text, "html.parser")

    for unwanted_element in soup(["script", "style"]):
        unwanted_element.decompose()

    text_maker = html2text.HTML2Text()
    text_maker.ignore_links = True
    text_maker.ignore_images = True
    return text_maker.handle(str(soup))


def get_encoded_id(id_original: str) -> str:
    """
    Encode an ID using base32 encoding.

    Args:
        id_original (str): The original ID to encode.

    Returns:
        str: The encoded ID.
    """
    return (
        base64.b32encode(id_original.encode("utf-8"))
        .decode("utf-8")
        .rstrip("=")
        .lower()
    )


async def create_conversation(
    db: AsyncClient, user_id: Optional[str], bot_id: str
) -> str:
    """
    Create a new conversation in the database.

    Args:
        db (AsyncClient): The database client.
        user_id (Optional[str]): The user ID.
        bot_id (str): The bot ID.

    Returns:
        str: The ID of the newly created conversation.
    """
    conversations_ref = db.collection("conversations")
    conversation_data = {
        "userId": user_id,
        "botId": bot_id,
        "messages": [],
        "createdAt": datetime.utcnow().isoformat(),
    }
    new_conversation_ref = await conversations_ref.add(conversation_data)
    return new_conversation_ref[1].id


async def add_message_to_conversation(
    db: AsyncClient, conversation_id: str, message: Dict[str, Any]
) -> bool:
    """
    Add a message to an existing conversation in the database.

    Args:
        db (AsyncClient): The database client.
        conversation_id (str): The ID of the conversation.
        message (Dict[str, Any]): The message to add.

    Returns:
        bool: True if the message was added successfully, False otherwise.
    """
    try:
        start_time = time.time()
        logger.info("Adding message to conversation started")

        conversations_ref = db.collection("conversations")
        await conversations_ref.document(conversation_id).collection("messages").add(
            message
        )

        end_time = time.time()
        logger.info(
            f"Message added to conversation. Time taken: {end_time - start_time} seconds"
        )
        return True
    except Exception as e:
        logger.error(f"Error adding message to conversation: {e}")
        return False


def get_timestamp_now() -> datetime:
    """
    Get the current UTC timestamp.

    Returns:
        datetime: The current UTC datetime.
    """
    return datetime.utcnow()


def log_answer_stats(cb: Any, answer: Any) -> None:
    """
    Log statistics about the model's answer.

    Args:
        cb (Any): The callback object containing token and cost information.
        answer (Any): The model's answer.
    """
    logger.info(f"Total Tokens: {cb.total_tokens}")
    logger.info(f"Prompt Tokens: {cb.prompt_tokens}")
    logger.info(f"Completion Tokens: {cb.completion_tokens}")
    logger.info(f"Total Cost (USD): ${cb.total_cost}")
    logger.info(f"Answer: {answer}")


def log_total_time(total_start_time: float) -> None:
    """
    Log the total execution time.

    Args:
        total_start_time (float): The start time of the operation.
    """
    total_end_time = time.time()
    logger.info(f"Total execution time: {total_end_time - total_start_time} seconds")


async def get_conversation_history(
    db: AsyncClient, agent_name: str, conversation_id: str
) -> BaseChatMessageHistory:
    """
    Retrieve the conversation history from the database.

    Args:
        db (AsyncClient): The database client.
        agent_name (str): The name of the agent.
        conversation_id (str): The ID of the conversation.

    Returns:
        BaseChatMessageHistory: The conversation history.
    """
    conversation_ref = db.collection("conversations").document(conversation_id)
    messages_ref = conversation_ref.collection("messages").order_by(
        "timestamp", direction="ASCENDING"
    )
    messages_docs = await messages_ref.get()
    messages_list = [message_doc.to_dict() for message_doc in messages_docs]

    history = ChatMessageHistory()
    for message in messages_list:
        if message["text"] and message["text"].strip():
            content = message["text"] + " <END_OF_TURN>"
            if not message["isBot"]:
                history.add_user_message(content)
            else:
                history.add_ai_message(content)
    return history


def get_bot_property(bot: Dict[str, Any], key: str, default_value: Any = None) -> Any:
    """
    Safely retrieve a property from the bot dictionary.

    Args:
        bot (Dict[str, Any]): The bot dictionary.
        key (str): The key to retrieve.
        default_value (Any, optional): The default value if the key is not found.

    Returns:
        Any: The value associated with the key or the default value.
    """
    return bot.get(key, default_value)


async def parse_request_data(request: Request) -> AaskRequestData:
    """
    Parse the request data for the Aask endpoint.

    Args:
        request (Request): The incoming request object.

    Returns:
        AaskRequestData: The parsed request data.
    """
    if request.method == "POST":
        data = await request.json()
    else:
        data = dict(request.query_params)

    if "firebase_urls" in data:
        if isinstance(data["firebase_urls"], str):
            data["firebase_urls"] = json.loads(data["firebase_urls"])
        elif isinstance(data["firebase_urls"], list):
            data["firebase_urls"] = data["firebase_urls"]
        elif isinstance(data["firebase_urls"], dict):
            data["firebase_urls"] = list(data["firebase_urls"].values())
        else:
            raise ValueError("Invalid type for 'firebase_urls'")

    return AaskRequestData(
        firebase_urls=data.get("firebase_urls", []),
        query=data["query"],
        token=data.get("token"),
        user_id=data["user_id"],
        bot_id=data["bot_id"],
        conversation_id=data["conversation_id"],
    )


def find_last_final_answer(total_response: str) -> str:
    """
    Find the last final answer in the total response string.

    Args:
        total_response (str): The complete response string.

    Returns:
        str: The last final answer or the original response if not found.
    """
    match = re.search(r".*Final Answer:(.*)", total_response, re.DOTALL)
    return match.group(1).strip() if match else total_response


def filter_string(text: str) -> str:
    """
    Filter and clean the input text by removing specific markers.

    Args:
        text (str): The input text to filter.

    Returns:
        str: The filtered text.
    """
    end_of_turn_index = text.find("<END_OF_TURN>")
    if end_of_turn_index != -1:
        text = text[:end_of_turn_index]

    last_separator_index = text.rfind("<END_OF_TOKEN>")
    if last_separator_index != -1:
        text = text[last_separator_index + len("<END_OF_TOKEN>") :]

    return text.strip()


def get_request_data(
    request: Union[Request, Dict[str, Any]], key: str, default: Any = None
) -> Any:
    """
    Get data from a request object or dictionary.

    Args:
        request (Union[Request, Dict[str, Any]]): The request object or dictionary.
        key (str): The key to retrieve.
        default (Any, optional): The default value if the key is not found.

    Returns:
        Any: The value associated with the key or the default value.
    """
    if isinstance(request, dict):
        return request.get(key, default)
    if request.method == "POST" and request.is_json:
        return request.json.get(key, default)
    elif request.method == "GET":
        return request.query_params.get(key, default)
    elif request.method == "POST":
        try:
            return json.loads(request.form.get(key, default))
        except (TypeError, json.JSONDecodeError):
            return default
    else:
        return default


async def parse_achat_request_data(request: Request) -> AchatRequestData:
    """
    Parse the request data for the Achat endpoint.

    Args:
        request (Request): The incoming request object.

    Returns:
        AchatRequestData: The parsed request data.
    """
    if request.method == "POST":
        try:
            data = await request.json()
        except ValueError:
            data = dict(request.query_params)
    else:
        data = dict(request.query_params)

    return AchatRequestData(
        user_input=data["user_input"],
        bot_id=data["bot_id"],
        user_id=data["user_id"],
        conversation_id=data["conversation_id"],
    )


async def parse_writing_request_data(request: Request) -> WritingRequestData:
    """
    Parse the request data for the Writing endpoint.

    Args:
        request (Request): The incoming request object.

    Returns:
        WritingRequestData: The parsed request data.
    """
    if request.method == "POST":
        try:
            data = await request.json()
        except ValueError:
            data = dict(request.query_params)
    else:
        data = dict(request.query_params)

    return WritingRequestData(
        user_id=data["user_id"],
        topic=data["topic"],
    )


async def process_data(request: Request) -> dict:
    """
    Process the incoming request data.

    Args:
        request (Request): The incoming request object.

    Returns:
        dict: The processed request data.

    Raises:
        HTTPException: If the request method is invalid.
    """
    if request.method == "POST":
        return await request.json()
    elif request.method == "GET":
        data = dict(request.query_params)
        if "messages" in data:
            data["messages"] = json.loads(data["messages"])
        return data
    else:
        raise HTTPException(status_code=400, detail="Invalid request method")


def format_time(timestamp: int, format_type: str = "seconds") -> str:
    """
    Format a timestamp into a readable string.

    Args:
        timestamp (int): The timestamp to format.
        format_type (str): The format type ("seconds" or "date").

    Returns:
        str: The formatted time string.
    """
    dt = datetime.fromtimestamp(timestamp)
    if format_type == "seconds":
        return dt.strftime("%H:%M:%S")
    elif format_type == "date":
        return dt.strftime("%Y-%m-%d")
    else:
        return dt.isoformat()


def get_timestamp() -> int:
    """
    Get the current timestamp as an integer.

    Returns:
        int: The current timestamp.
    """
    return int(datetime.now().timestamp())


def upload_document_to_firebase(
    path: str, bucket_name: str, destination_blob_name: str
) -> str:
    """
    Upload a document to Firebase Storage.

    Args:
        path (str): The local file path.
        bucket_name (str): The Firebase storage bucket name.
        destination_blob_name (str): The destination blob name in storage.

    Returns:
        str: The public URL of the uploaded file.
    """
    if get_storage is None:
        raise ImportError("Firebase storage is not available")

    storage = get_storage()
    bucket = storage.bucket(bucket_name)
    blob = bucket.blob(destination_blob_name)
    blob.upload_from_filename(path)
    return blob.public_url


def parse_markdown(markdown_text: str) -> str:
    """
    Parse markdown text to HTML.

    Args:
        markdown_text (str): The markdown text to parse.

    Returns:
        str: The HTML representation of the markdown.
    """
    if markdown is None:
        raise ImportError("Markdown library is not available")

    return markdown.markdown(markdown_text)
