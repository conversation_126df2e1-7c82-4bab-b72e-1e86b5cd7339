# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: diogenesaichatbot.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x64iogenesaichatbot.proto\x12\x11\x64iogenesaichatbot\x1a\x1bgoogle/protobuf/empty.proto\"\xa7\x01\n\x0c\x44ocumentData\x12H\n\rdocument_data\x18\x01 \x03(\x0b\x32\x31.diogenesaichatbot.DocumentData.DocumentDataEntry\x12\x18\n\x10original_user_id\x18\x02 \x01(\t\x1a\x33\n\x11\x44ocumentDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"O\n\x13\x44ocumentRequestData\x12\x18\n\x10original_user_id\x18\x01 \x01(\t\x12\x1e\n\x16\x64ocument_firebase_urls\x18\x02 \x03(\t\"M\n\x1a\x44ocumentContentRequestData\x12\x15\n\rfirebase_urls\x18\x01 \x03(\t\x12\x18\n\x10original_user_id\x18\x02 \x01(\t\"L\n\x12\x42odyCreateDocument\x12\x36\n\rdocument_data\x18\x01 \x01(\x0b\x32\x1f.diogenesaichatbot.DocumentData\"z\n\x12\x42odyUpdateDocument\x12\x13\n\x0b\x64ocument_id\x18\x01 \x01(\t\x12\x35\n\x0cupdated_data\x18\x02 \x01(\x0b\x32\x1f.diogenesaichatbot.DocumentData\x12\x18\n\x10original_user_id\x18\x03 \x01(\t\"C\n\x12\x42odyDeleteDocument\x12\x13\n\x0b\x64ocument_id\x18\x01 \x01(\t\x12\x18\n\x10original_user_id\x18\x02 \x01(\t\"T\n!BodyDeleteDocumentsByFirebaseUrls\x12\x15\n\rfirebase_urls\x18\x01 \x03(\t\x12\x18\n\x10original_user_id\x18\x02 \x01(\t\"\x83\x01\n\x11\x42odyEmbedDocument\x12\x15\n\rfirebase_urls\x18\x01 \x03(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x1a\n\x12\x62ool_updateOnExist\x18\x03 \x01(\x08\x12*\n\"optional_predefined_content_tofill\x18\x04 \x03(\t\"\xe1\x01\n\x13\x42odyCreateItinerary\x12\x11\n\tinterests\x18\x01 \x01(\t\x12\x16\n\x0estart_location\x18\x02 \x01(\t\x12\x14\n\x0c\x65nd_location\x18\x03 \x01(\t\x12\x0e\n\x06places\x18\x04 \x03(\t\x12\x15\n\rorder_matters\x18\x05 \x01(\x08\x12\x12\n\nstart_date\x18\x06 \x01(\t\x12\x10\n\x08\x65nd_date\x18\x07 \x01(\t\x12\x10\n\x08rent_car\x18\x08 \x01(\x08\x12\x14\n\x0ctravel_style\x18\t \x01(\t\x12\x14\n\x0ctravel_focus\x18\n \x03(\t\"I\n\x13HTTPValidationError\x12\x32\n\x06\x64\x65tail\x18\x01 \x03(\x0b\x32\".diogenesaichatbot.ValidationError\"9\n\x0fValidationError\x12\x0b\n\x03loc\x18\x01 \x03(\t\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t2\x95\x04\n\x0f\x44ocumentService\x12O\n\x0e\x43reateDocument\x12%.diogenesaichatbot.BodyCreateDocument\x1a\x16.google.protobuf.Empty\x12O\n\x0eUpdateDocument\x12%.diogenesaichatbot.BodyUpdateDocument\x1a\x16.google.protobuf.Empty\x12O\n\x0e\x44\x65leteDocument\x12%.diogenesaichatbot.BodyDeleteDocument\x1a\x16.google.protobuf.Empty\x12m\n\x1d\x44\x65leteDocumentsByFirebaseUrls\x12\x34.diogenesaichatbot.BodyDeleteDocumentsByFirebaseUrls\x1a\x16.google.protobuf.Empty\x12M\n\rEmbedDocument\x12$.diogenesaichatbot.BodyEmbedDocument\x1a\x16.google.protobuf.Empty\x12Q\n\x0f\x43reateItinerary\x12&.diogenesaichatbot.BodyCreateItinerary\x1a\x16.google.protobuf.Emptyb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'diogenesaichatbot_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _DOCUMENTDATA_DOCUMENTDATAENTRY._options = None
  _DOCUMENTDATA_DOCUMENTDATAENTRY._serialized_options = b'8\001'
  _DOCUMENTDATA._serialized_start=76
  _DOCUMENTDATA._serialized_end=243
  _DOCUMENTDATA_DOCUMENTDATAENTRY._serialized_start=192
  _DOCUMENTDATA_DOCUMENTDATAENTRY._serialized_end=243
  _DOCUMENTREQUESTDATA._serialized_start=245
  _DOCUMENTREQUESTDATA._serialized_end=324
  _DOCUMENTCONTENTREQUESTDATA._serialized_start=326
  _DOCUMENTCONTENTREQUESTDATA._serialized_end=403
  _BODYCREATEDOCUMENT._serialized_start=405
  _BODYCREATEDOCUMENT._serialized_end=481
  _BODYUPDATEDOCUMENT._serialized_start=483
  _BODYUPDATEDOCUMENT._serialized_end=605
  _BODYDELETEDOCUMENT._serialized_start=607
  _BODYDELETEDOCUMENT._serialized_end=674
  _BODYDELETEDOCUMENTSBYFIREBASEURLS._serialized_start=676
  _BODYDELETEDOCUMENTSBYFIREBASEURLS._serialized_end=760
  _BODYEMBEDDOCUMENT._serialized_start=763
  _BODYEMBEDDOCUMENT._serialized_end=894
  _BODYCREATEITINERARY._serialized_start=897
  _BODYCREATEITINERARY._serialized_end=1122
  _HTTPVALIDATIONERROR._serialized_start=1124
  _HTTPVALIDATIONERROR._serialized_end=1197
  _VALIDATIONERROR._serialized_start=1199
  _VALIDATIONERROR._serialized_end=1256
  _DOCUMENTSERVICE._serialized_start=1259
  _DOCUMENTSERVICE._serialized_end=1792
# @@protoc_insertion_point(module_scope)
