from datetime import datetime
from typing import Dict




class BotChatMessage:
    def __init__(self, text: str, sender_id: str, timestamp, is_bot: bool):
        self.text = text
        self.sender_id = sender_id
        self.timestamp = timestamp
        self.is_bot = is_bot

    def to_dict(self) -> Dict[str, any]:
        return {
            "text": self.text,
            "senderId": self.sender_id,
            "timestamp": self.timestamp,
            "isBot": self.is_bot,
        }

    @staticmethod
    def create_bot_response(
            response_data: Dict[str, any], bot_id: str
    ) -> "BotChatMessage":
        return BotChatMessage(
            text=response_data["answer"],
            sender_id=bot_id,
            timestamp=datetime.utcnow(),
            is_bot=True,
        )
