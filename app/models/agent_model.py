from typing import List, Optional

from pydantic import BaseModel


class Agent(BaseModel):
    role: str
    goal: str
    backstory: str
    allow_delegation: Optional[bool] = True
    tools: Optional[List[str]] = []


class Task(BaseModel):
    description: str
    expected_output: str
    agent_role: str


class AgentData(BaseModel):
    agents: Optional[List[Agent]] = None
    tasks: Optional[List[Task]] = None
    building_task: Optional[str] = None
