import time
from typing import List, Optional

from fastapi import Request
from pydantic import BaseModel, Field
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.tracing_setup import logger


class AaskRequestData(BaseModel):
    """
    Represents the data required for making a request to the Aask API.

       :param firebase_urls: A list of Firebase URLs to be queried.
       :type firebase_urls: List[str]
       :param query: The query to be used for retrieving the data.
       :type query: str
       :param token: An optional token for authentication.
       :type token: Optional[str]
       :param bot_id: The ID of the bot sending the request.
       :type bot_id: str
       :param user_id: The ID of the user making the request.
       :type user_id: str
       :param conversation_id: The ID of the conversation.
       :type conversation_id: str
       """

    firebase_urls: List[str] = Field(default_factory=list)
    query: str
    token: Optional[str] = None
    bot_id: str
    user_id: str
    conversation_id: str


class AchatRequestData(BaseModel):
    """
    Represents the data required for making a request to the Achat API.

    :param bot_id: The ID of the bot sending the request.
    :type bot_id: str
    :param user_id: The ID of the user making the request.
    :type user_id: str
    :param conversation_id: The ID of the conversation.
    :type conversation_id: str
    :param user_input: The input provided by the user.
    :type user_input: str
    """

    bot_id: str
    user_id: str
    conversation_id: str
    user_input: str


class WritingRequestData(BaseModel):
    user_id: str
    topic: str


class AskRequestData(BaseModel):
    firebase_urls: Optional[List[str]]
    bot_id: Optional[str]
    query: str
    token: str


class EmbedDocumentRequestData(BaseModel):
    firebase_urls: List[str]
    user_id: str
    bool_updateOnExist: bool = False
    optional_predefined_content_tofill: List[str] = Field(default_factory=list)


class DocumentBotRequestData(BaseModel):
    original_bot_id: str
    document_firebase_urls: List[str] = Field(default_factory=list)
    original_user_id: str


class DocumentContentRequestData(BaseModel):
    firebase_urls: List[str] = Field(default_factory=list)
    original_user_id: str


class DocumentRequestData(BaseModel):
    original_user_id: str
    document_firebase_urls: List[str] = Field(default_factory=list)


class RequestLoggerMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Log the start of the request with the method name
        start_time = time.time()
        logger.info(f"Started {request.method} request to {request.url.path}")

        response = await call_next(request)

        # Calculate the time used
        end_time = time.time()
        elapsed_time = end_time - start_time

        # Log the end of the request with the method name and time used
        logger.info(
            f"Ended {request.method} request to {request.url.path} in {elapsed_time:.2f} seconds"
        )

        return response
