import asyncio

from langchain_community.vectorstores import SKLearnVectorStore
from langchain_core.documents import Document
from langchain_core.messages import AIMessage
from langchain_core.runnables import Runnable
from langchain_openai import OpenAIEmbeddings
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, StateGraph, START

from app.config.config import llm_writing
from app.core.tracing_setup import logger
from app.model.writing import gen_answer, route_messages
from app.models.writing import generate_outline_direct, InterviewState, refine_outline_chain, writer, \
    ResearchState, survey_subjects, \
    generate_question, Perspectives, section_writer_prompt, WikiSection


async def initialize_research(state: ResearchState):
    topic = state["topic"]
    coros = (
        generate_outline_direct.ainvoke({"topic": topic}),
        survey_subjects.ainvoke(topic),
    )
    results = await asyncio.gather(*coros)
    return {
        **state,
        "outline": results[0],
        "editors": results[1].editors,
    }


def format_conversation(interview_state):
    messages = interview_state["messages"]
    convo = "\n".join(f"{m.name}: {m.content}" for m in messages)
    return f'Conversation with {interview_state["editor"].name}\n\n' + convo


async def refine_outline(state: ResearchState):
    convos = "\n\n".join(
        [
            format_conversation(interview_state)
            for interview_state in state["interview_results"]
        ]
    )

    updated_outline = await refine_outline_chain.ainvoke(
        {
            "topic": state["topic"],
            "old_outline": state["outline"].as_str,
            "conversations": convos,
        }
    )
    return {**state, "outline": updated_outline}


async def write_article(state: ResearchState):
    topic = state["topic"]
    sections = state["sections"]
    draft = "\n\n".join([section.as_str for section in sections])
    article = await writer.ainvoke({"topic": topic, "draft": draft})
    return {
        **state,
        "article": article,
    }


builder = StateGraph(InterviewState)
builder.add_node("ask_question", generate_question)
builder.add_node("answer_question", gen_answer)
builder.add_conditional_edges("answer_question", route_messages)
builder.add_edge("ask_question", "answer_question")
builder.add_edge(START, "ask_question")
interview_graph = builder.compile().with_config(run_name="Conduct Interviews")


async def awrite(user_topic: str, user_id: str, session_id: str) -> ResearchState:
    # global interview_graph, step, name, refined_outline, vectorstore, retriever, retrieve, format_conversation, config, article

    # Using turbo preview since the context can get quite long
    initial_outline = await generate_outline_direct.ainvoke({"topic": user_topic})
    logger.info(initial_outline.as_str)
    perspectives: Perspectives = await survey_subjects.ainvoke(user_topic)
    final_step = None
    initial_state = {
        "editor": perspectives.editors[0],
        "messages": [
            AIMessage(
                content=f"So you said you were writing an article on {user_topic}?",
                name="Subject_Matter_Expert",
            )
        ],
    }
    async for step in interview_graph.astream(initial_state):
        name = next(iter(step))
        logger.info("name: " + str(name))
        logger.info("step: " + str(step))
        logger.info(f"-- {str(step[name]['messages'])[:300]}")
        # TODO: did final_step here really get final step and all the messages?
        final_step = step
        if END in step:
            final_step = step
            break  # exit the loop early if END is detected

    if final_step is None:
        raise ValueError("Final step was not captured correctly, possibly due to incorrect END detection.")
    final_state = next(iter(final_step.values()))
    refined_outline = await refine_outline_chain.ainvoke(
        {
            "topic": user_topic,
            "old_outline": initial_outline.as_str,
            "conversations": "\n\n".join(
                f"### {m.name}\n\n{m.content}" for m in final_state["messages"]
            ),
        }
    )
    embeddings = OpenAIEmbeddings(model="text-embedding-3-small")
    reference_docs = [
        Document(page_content=v, metadata={"source": k})
        for k, v in final_state["references"].items()
    ]
    # This really doesn't need to be a vectorstore for this size of data.
    # It could just be a numpy matrix. Or you could store documents
    # across requests if you want.
    vectorstore = SKLearnVectorStore.from_documents(
        reference_docs,
        embedding=embeddings,
    )
    retriever = vectorstore.as_retriever(k=10)

    async def retrieve(inputs: dict):
        docs = await retriever.ainvoke(inputs["topic"] + ": " + inputs["section"])
        formatted = "\n".join(
            [
                f'\n{doc.page_content}\n'
                for doc in docs
            ]
        )
        return {"docs": formatted, **inputs}

    section_writer = (
            retrieve
            | section_writer_prompt
            | llm_writing.with_structured_output(WikiSection)
    )

    async def conduct_interviews(state: ResearchState):
        topic = state["topic"]
        initial_states = [
            {
                "editor": editor,
                "messages": [
                    AIMessage(
                        content=f"So you said you were writing an article on {topic}?",
                        name="Subject_Matter_Expert",
                    )
                ],
            }
            for editor in state["editors"]
        ]
        # We call in to the sub-graph here to parallelize the interviews
        interview_results = await interview_graph.abatch(initial_states)

        return {
            **state,
            "interview_results": interview_results,
        }

    async def index_references(state: ResearchState):
        all_docs = []
        for interview_state in state["interview_results"]:
            reference_docs = [
                Document(page_content=v, metadata={"source": k})
                for k, v in interview_state["references"].items()
            ]
            all_docs.extend(reference_docs)
        await vectorstore.aadd_documents(all_docs)
        return state

    async def write_sections(state: ResearchState):
        outline = state["outline"]
        sections = await section_writer.abatch(
            [
                {
                    "outline": refined_outline.as_str,
                    "section": section.section_title,
                    "topic": state["topic"],
                }
                for section in outline.sections
            ]
        )
        return {
            **state,
            "sections": sections,
        }

    builder_of_storm = StateGraph(ResearchState)
    nodes = [
        ("init_research", initialize_research),
        ("conduct_interviews", conduct_interviews),
        ("refine_outline", refine_outline),
        ("index_references", index_references),
        ("write_sections", write_sections),
        ("write_article", write_article),
    ]
    for i in range(len(nodes)):
        name, node = nodes[i]
        builder_of_storm.add_node(name, node)
        if i > 0:
            builder_of_storm.add_edge(nodes[i - 1][0], name)
    builder_of_storm.add_edge(START, nodes[0][0])
    builder_of_storm.add_edge(nodes[-1][0], END)
    storm = builder_of_storm.compile(checkpointer=MemorySaver())
    ################################################################
    config = {"configurable": {"thread_id": "my-thread-{0}-{1}".format(str(user_id), session_id)}}

    async def process_storm_stream(storm: Runnable, input, config):
        try:
            async for step in storm.astream(input=input, config=config, ):
                # Process step
                name = next(iter(step))
                logger.info(name)
                logger.info(step)
                logger.info(f"-- {str(step[name])[:300]}")
        except asyncio.TimeoutError:
            logger.info("Timeout occurred during storm stream processing")
            # Handle timeout, maybe clean up or retry

    try:
        await asyncio.wait_for(process_storm_stream(storm, {
            "topic": user_topic,
        }, config, ), timeout=600)
    except asyncio.TimeoutError:
        logger.info("Timeout occurred during the entire awrite process")
        # Handle the overall timeout here

    checkpoint = storm.get_state(config)

    # * checkpoint.values contains we need: it is a ResearchState object that contains everything we need
    research_state: ResearchState = ResearchState(checkpoint.values)
    article = checkpoint.values["article"]
    logger.info(article)
    return research_state

# We will down-header the sections to create less confusion in this notebook
# Markdown(article.replace("\n#", "\n##"))
