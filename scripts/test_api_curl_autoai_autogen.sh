# Successful case with all optional fields
curl -X POST "http://localhost:8085/start_workflow" \
     -H "Content-Type: application/json" \
     -d '{
           "agents": [
             {
               "role": "researcher",
               "goal": "compile report",
               "backstory": "Expert in research and data analysis with experience in compiling detailed technical reports.",
               "allow_delegation": true,
               "tools": ["research_tool", "data_analyzer"]
             },
             {
               "role": "writer",
               "goal": "create engaging content",
               "backstory": "Skilled writer known for transforming complex technical information into engaging and accessible content.",
               "allow_delegation": false,
               "tools": []
             }
           ],
           "tasks": [
             {
               "description": "Research and summarize the best practices for integrating Flutter with Firebase for AI chatbots.",
               "expected_output": "Summary of best practices and integration tips for using Flutter with Firebase in AI chatbot development.",
               "agent_role": "researcher"
             },
             {
               "description": "Investigate effective methods for using Python/FastAPI with LangChain in building AI chatbots.",
               "expected_output": "Summary of effective methods and practices for integrating Python/FastAPI with LangChain.",
               "agent_role": "researcher"
             },
             {
               "description": "Compile and organize the research findings into a structured outline for the report.",
               "expected_output": "Detailed outline of the report, including key sections and topics based on research findings.",
               "agent_role": "researcher"
             },
             {
               "description": "Write a comprehensive report incorporating research findings on building AI chatbots using Flutter, Firebase, Python/FastAPI, and LangChain.",
               "expected_output": "Well-structured and engaging report that integrates research findings and provides a thorough overview of the technologies and their impact.",
               "agent_role": "writer"
             }
           ],
           "building_task": "Write a comprehensive report on: Building AI Chatbot using Flutter/Firebase and Python/FastAPI/LangChain, including the integration of various technologies and their impact.",
        "user_id":"8KEKoa6d90dr75AywriRKX3nLIR2"
                 }'

curl -X POST "http://localhost:8085/start_workflow" \
     -H "Content-Type: application/json" \
     -d '{
           "building_task": "Write a comprehensive report on: Building AI Chatbot using Flutter/Firebase and Python/FastAPI/LangChain, including the integration of various technologies and their impact.",
        "user_id":"8KEKoa6d90dr75AywriRKX3nLIR2"
                 }'



## Edge case with minimal input
#curl -X POST "http://localhost:8085/start_autogen" \
#     -H "Content-Type: application/json" \
#     -d '{}'
