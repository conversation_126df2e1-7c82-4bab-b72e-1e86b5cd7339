{"openapi": "3.1.0", "info": {"title": "FastAPI", "version": "0.1.0"}, "paths": {"/create_document": {"post": {"summary": "Create Document", "operationId": "create_document_create_document_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_create_document_create_document_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/update_document": {"put": {"summary": "Update Document", "operationId": "update_document_update_document_put", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_update_document_update_document_put"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/delete_document": {"delete": {"summary": "Delete Document", "operationId": "delete_document_delete_document_delete", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_delete_document_delete_document_delete"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/delete_documents_by_firebase_urls": {"post": {"summary": "Delete Documents By Firebase Urls", "operationId": "delete_documents_by_firebase_urls_delete_documents_by_firebase_urls_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_delete_documents_by_firebase_urls_delete_documents_by_firebase_urls_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/search_document": {"get": {"summary": "Search Document", "operationId": "search_document_search_document_get", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string", "title": "Query"}}, {"name": "original_user_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Original User Id"}}, {"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/document_exists": {"get": {"summary": "Document Exists", "operationId": "document_exists_document_exists_get", "parameters": [{"name": "document_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Document Id"}}, {"name": "original_user_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Original User Id"}}, {"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/search_document_by_embedding": {"get": {"summary": "Search Document By Embedding", "operationId": "search_document_by_embedding_search_document_by_embedding_get", "parameters": [{"name": "embedding_vector", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "number"}, "title": "Embedding Vector"}}, {"name": "original_user_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Original User Id"}}, {"name": "original_bot_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Original Bot Id"}}, {"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/embed_document": {"post": {"summary": "Embed Document", "operationId": "embed_document_embed_document_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_embed_document_embed_document_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/assign_documents_to_bot": {"post": {"summary": "Assign Documents To Bot", "operationId": "assign_documents_to_bot_assign_documents_to_bot_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentBotRequestData"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/remove_documents_from_bot": {"post": {"summary": "Remove Documents From Bot", "operationId": "remove_documents_from_bot_remove_documents_from_bot_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentBotRequestData"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/get_document_content": {"post": {"summary": "Get Document Content", "operationId": "get_document_content_get_document_content_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentContentRequestData"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/get_document_ids_by_firebase_urls": {"post": {"summary": "Get Document Ids By Firebase Urls", "operationId": "get_document_ids_by_firebase_urls_get_document_ids_by_firebase_urls_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentRequestData"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/get_bot_documents": {"get": {"summary": "Get Bot Documents", "operationId": "get_bot_documents_get_bot_documents_get", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_get_bot_documents_get_bot_documents_get"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/writing": {"post": {"summary": "Writing", "description": "This function handles the writing process by either using Lang<PERSON>hain or directly with STORMWikiRunner.\nIt generates an article based on the user's topic and performs research, outline generation, article creation, and polishing.\nThe function also handles the storage of the generated article and updates progress status in Firestore.\n\nParameters:\n- request: The Starlette Request object containing the user's input.\n- db: The asynchronous Firestore client for database operations.\n- storage: The Google Cloud Firebase Storage client for file storage operations.\n\nReturns:\n- A dictionary containing the generated article.", "operationId": "writing_writing_post", "parameters": [{"name": "verify_firebase_token", "in": "query", "required": false, "schema": {"title": "Verify Firebase Token"}}, {"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/writing_sync": {"post": {"summary": "Writing Sync", "description": "This function handles the writing process by either using Lang<PERSON>hain or directly with STORMWikiRunner.\nIt generates an article based on the user's topic and performs research, outline generation, article creation, and polishing.\nThe function also handles the storage of the generated article and updates progress status in Firestore.\n\nParameters:\n- request: The Starlette Request object containing the user's input.\n- db: The asynchronous Firestore client for database operations.\n- storage: The Google Cloud Firebase Storage client for file storage operations.\n\nReturns:\n- A dictionary containing the generated article.", "operationId": "writing_sync_writing_sync_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/start_crew": {"post": {"summary": "Start Crewai Task", "description": "Starts a Celery task for running the CrewAI simulation.\n\nArgs:\n    request (Request): The incoming request containing the agent data.\n\nReturns:\n    dict: A dictionary containing the task ID and session ID.", "operationId": "start_crewai_task_start_crew_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Start Crewai Task Start Crew Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/getToken": {"get": {"summary": "Get Token", "operationId": "get_token_getToken_get", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string", "title": "Name"}}, {"name": "room_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Room Id"}}, {"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/itinerary/": {"post": {"summary": "Create Itinerary", "operationId": "create_itinerary_itinerary__post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_create_itinerary_itinerary__post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/generate_audio": {"post": {"summary": "Generate Audio Api", "operationId": "generate_audio_api_generate_audio_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_generate_audio_api_generate_audio_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/generate-story/": {"post": {"summary": "Generate Story", "operationId": "generate_story_generate_story__post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoryRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/webhooks": {"post": {"summary": "Stripe Webhook", "operationId": "stripe_webhook_webhooks_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/payment": {"post": {"summary": "Handle Payment Request", "operationId": "handle_payment_request_payment_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/create-payment-intent": {"post": {"summary": "Create Payment Intent", "operationId": "create_payment_intent_create_payment_intent_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/payment-sheet": {"post": {"summary": "Create Payment Sheet", "operationId": "create_payment_sheet_payment_sheet_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"type": "string", "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "Index", "description": "Root endpoint for the API.\n\nReturns:\n    str: A greeting message.", "operationId": "index__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "string", "title": "Response Index  Get"}}}}}}, "post": {"summary": "Index", "description": "Root endpoint for the API.\n\nReturns:\n    str: A greeting message.", "operationId": "index__post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "string", "title": "Response Index  Post"}}}}}}}}, "components": {"schemas": {"BalanceRequest": {"properties": {"type": {"type": "string", "title": "Type"}, "user_id": {"type": "string", "title": "User Id"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "default": "usd"}, "amount": {"type": "number", "title": "Amount"}, "payment_method_types": {"items": {"type": "string"}, "type": "array", "title": "Payment Method Types", "default": ["card"]}}, "type": "object", "required": ["type", "user_id"], "title": "BalanceRequest"}, "BalanceResponse": {"properties": {"balance": {"type": "number", "title": "Balance"}, "client_secret": {"type": "string", "title": "Client Secret"}}, "type": "object", "title": "BalanceResponse"}, "Body_create_document_create_document_post": {"properties": {"document_data": {"type": "object", "title": "Document Data"}, "original_user_id": {"type": "string", "title": "Original User Id"}}, "type": "object", "required": ["document_data", "original_user_id"], "title": "Body_create_document_create_document_post"}, "Body_create_itinerary_itinerary__post": {"properties": {"interests": {"type": "string", "title": "Interests"}, "start_location": {"type": "string", "title": "Start Location"}, "end_location": {"type": "string", "title": "End Location"}, "places": {"items": {"type": "string"}, "type": "array", "title": "Places", "default": []}, "order_matters": {"type": "boolean", "title": "Order Matters", "default": false}, "start_date": {"type": "string", "title": "Start Date"}, "end_date": {"type": "string", "title": "End Date"}, "rent_car": {"type": "boolean", "title": "Rent Car", "default": true}, "travel_style": {"type": "string", "title": "Travel Style"}, "travel_focus": {"items": {"type": "string"}, "type": "array", "title": "Travel Focus", "default": []}}, "type": "object", "required": ["interests", "start_location", "end_location", "travel_style"], "title": "Body_create_itinerary_itinerary__post"}, "Body_delete_document_delete_document_delete": {"properties": {"document_id": {"type": "string", "title": "Document Id"}, "original_user_id": {"type": "string", "title": "Original User Id"}}, "type": "object", "required": ["document_id", "original_user_id"], "title": "Body_delete_document_delete_document_delete"}, "Body_delete_documents_by_firebase_urls_delete_documents_by_firebase_urls_post": {"properties": {"firebase_urls": {"items": {"type": "string"}, "type": "array", "title": "Firebase Urls"}, "original_user_id": {"type": "string", "title": "Original User Id"}}, "type": "object", "required": ["firebase_urls", "original_user_id"], "title": "Body_delete_documents_by_firebase_urls_delete_documents_by_firebase_urls_post"}, "Body_embed_document_embed_document_post": {"properties": {"firebase_urls": {"items": {"type": "string"}, "type": "array", "title": "Firebase Urls"}, "user_id": {"type": "string", "title": "User Id"}, "bool_updateOnExist": {"type": "boolean", "title": "Bool Updateonexist"}, "optional_predefined_content_tofill": {"items": {"type": "string"}, "type": "array", "title": "Optional Predefined Content Tofill"}}, "type": "object", "required": ["firebase_urls", "user_id", "bool_updateOnExist", "optional_predefined_content_tofill"], "title": "Body_embed_document_embed_document_post"}, "Body_generate_audio_api_generate_audio_post": {"properties": {"files": {"items": {"type": "string", "format": "binary"}, "type": "array", "title": "Files"}, "text_model": {"type": "string", "title": "Text Model", "default": "gpt-4o-mini"}, "audio_model": {"type": "string", "title": "Audio Model", "default": "tts-1"}, "speaker_1_voice": {"type": "string", "title": "Speaker 1 Voice", "default": "alloy"}, "speaker_2_voice": {"type": "string", "title": "Speaker 2 Voice", "default": "echo"}, "intro_instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Intro Instructions", "default": ""}, "text_instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Text Instructions", "default": ""}, "scratch_pad_instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Pad Instructions", "default": ""}, "prelude_dialog": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prelude Dialog", "default": ""}, "podcast_dialog_instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Podcast Dialog Instructions", "default": ""}, "edited_transcript": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Edited Transcript"}, "user_feedback": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User <PERSON>"}}, "type": "object", "required": ["files"], "title": "Body_generate_audio_api_generate_audio_post"}, "Body_get_bot_documents_get_bot_documents_get": {"properties": {"original_bot_id": {"type": "string", "title": "Original Bot Id"}, "original_user_id": {"type": "string", "title": "Original User Id"}}, "type": "object", "required": ["original_bot_id", "original_user_id"], "title": "Body_get_bot_documents_get_bot_documents_get"}, "Body_update_document_update_document_put": {"properties": {"document_id": {"type": "string", "title": "Document Id"}, "updated_data": {"type": "object", "title": "Updated Data"}, "original_user_id": {"type": "string", "title": "Original User Id"}}, "type": "object", "required": ["document_id", "updated_data", "original_user_id"], "title": "Body_update_document_update_document_put"}, "DocumentBotRequestData": {"properties": {"original_bot_id": {"type": "string", "title": "Original Bot Id"}, "document_firebase_urls": {"items": {"type": "string"}, "type": "array", "title": "Document Firebase Urls"}, "original_user_id": {"type": "string", "title": "Original User Id"}}, "type": "object", "required": ["original_bot_id", "original_user_id"], "title": "DocumentBotRequestData"}, "DocumentContentRequestData": {"properties": {"firebase_urls": {"items": {"type": "string"}, "type": "array", "title": "Firebase Urls"}, "original_user_id": {"type": "string", "title": "Original User Id"}}, "type": "object", "required": ["original_user_id"], "title": "DocumentContentRequestData"}, "DocumentRequestData": {"properties": {"original_user_id": {"type": "string", "title": "Original User Id"}, "document_firebase_urls": {"items": {"type": "string"}, "type": "array", "title": "Document Firebase Urls"}}, "type": "object", "required": ["original_user_id"], "title": "DocumentRequestData"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "StoryRequest": {"properties": {"length": {"type": "string", "title": "Length"}, "genre": {"type": "string", "title": "Genre"}, "characters": {"items": {}, "type": "array", "title": "Characters"}, "age_group": {"type": "string", "title": "Age Group"}, "themes": {"items": {"type": "string"}, "type": "array", "title": "Themes"}, "brief_story": {"type": "string", "title": "Brief Story"}, "image_style": {"type": "string", "title": "Image Style"}, "voice": {"type": "string", "enum": ["alloy", "echo", "fable", "onyx", "nova", "shimmer"], "title": "Voice"}}, "type": "object", "required": ["length", "genre", "characters", "age_group", "themes", "brief_story", "image_style", "voice"], "title": "StoryRequest"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}