curl -X POST http://localhost:8085/start_crew \
-H "Content-Type: application/json" \
-d '{
        "user_id":"8KEKoa6d90dr75AywriRKX3nLIR2",
         "ultimate_goal": "Do a comprehensive research of the latest updates of transformers and diffusion models in AI and develop an research report about it"
    }
'


curl -X POST http://localhost:8085/start_crew \
-H "Content-Type: application/json" \
-d '{
        "agents": [
            {
                "role": "Senior Research Analyst",
                "goal": "Uncover cutting-edge developments in AI and data science",
                "backstory": "You work at a leading tech think tank...",
                "tools": ["search_tool"]
            },
            {
                "role": "Tech Content Strategist",
                "goal": "Craft compelling content on tech advancements",
                "backstory": "You are a renowned Content Strategist...",
                "allow_delegation": false
            }
        ],
        "tasks": [
            {
                "description": "Conduct a comprehensive analysis of the latest advancements in AI in 2024.",
                "expected_output": "Full analysis report in bullet points",
                "agent_role": "Senior Research Analyst"
            },
            {
                "description": "Develop an engaging blog post...",
                "expected_output": "Full blog post of at least 8 paragraphs with enough details",
                "agent_role": "Tech Content Strategist"
            }
        ],
        "user_id":"8KEKoa6d90dr75AywriRKX3nLIR2",
         "ultimate_goal": "Conduct a comprehensive analysis of the latest advancements in AI and Develop an engaging blog post about it"
    }
'
#
#curl -X POST http://localhost:8085/start_crew \
#-H "Content-Type: application/json" \
#-d '{
#        "agents": [
#            {
#                "role": "Healthcare Analyst",
#                "goal": "Analyze the latest research and developments in the healthcare sector",
#                "backstory": "You specialize in healthcare analytics and research...",
#                "tools": ["search_tool", "website_search_tool"]
#            },
#            {
#                "role": "Health Communications Specialist",
#                "goal": "Prepare a summary and presentation of the research findings",
#                "backstory": "You have a background in health communication and public relations...",
#                "allow_delegation": true
#            }
#        ],
#        "tasks": [
#            {
#                "description": "Conduct a thorough review of recent advancements in healthcare technology.",
#                "expected_output": "Detailed report with findings and trends",
#                "agent_role": "Healthcare Analyst"
#            },
#            {
#                "description": "Create a presentation summarizing the research findings for a conference.",
#                "expected_output": "Presentation slides with key points and visuals",
#                "agent_role": "Health Communications Specialist"
#            }
#        ],
#        "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2"
#    }
#'
#curl -X POST http://localhost:8085/start_crew \
#-H "Content-Type: application/json" \
#-d '{
#        "agents": [
#            {
#                "role": "Product Researcher",
#                "goal": "Gather and analyze user feedback on the new software",
#                "backstory": "You are an expert in collecting and interpreting user feedback...",
#                "tools": ["search_tool"]
#            },
#            {
#                "role": "Technical Writer",
#                "goal": "Draft detailed documentation and user guides for the new software",
#                "backstory": "You specialize in creating clear and comprehensive documentation...",
#                "allow_delegation": false
#            }
#        ],
#        "tasks": [
#            {
#                "description": "Compile user feedback and insights on the new software release.",
#                "expected_output": "Comprehensive report on user feedback",
#                "agent_role": "Product Researcher"
#            },
#            {
#                "description": "Write user manuals and FAQs for the new software product.",
#                "expected_output": "Complete user manual and FAQ document",
#                "agent_role": "Technical Writer"
#            }
#        ],
#        "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2"
#    }
#'
#curl -X POST http://localhost:8085/start_crew \
#-H "Content-Type: application/json" \
#-d '{
#        "agents": [
#            {
#                "role": "E-Commerce Analyst",
#                "goal": "Investigate trends in online shopping and consumer behavior",
#                "backstory": "You analyze e-commerce trends and consumer preferences...",
#                "tools": ["search_tool", "code_tool"]
#            },
#            {
#                "role": "Content Creator",
#                "goal": "Develop engaging content based on market research",
#                "backstory": "You create content that resonates with target audiences...",
#                "allow_delegation": true
#            }
#        ],
#        "tasks": [
#            {
#                "description": "Research current trends in e-commerce and online shopping behavior.",
#                "expected_output": "Analysis report with key e-commerce trends",
#                "agent_role": "E-Commerce Analyst"
#            },
#            {
#                "description": "Create engaging blog posts and social media content based on research findings.",
#                "expected_output": "Blog posts and social media content for marketing",
#                "agent_role": "Content Creator"
#            }
#        ],
#        "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2"
#    }
#'
#curl -X POST http://localhost:8085/start_crew \
#-H "Content-Type: application/json" \
#-d '{
#        "agents": [
#            {
#                "role": "Curriculum Developer",
#                "goal": "Design a new curriculum based on educational research",
#                "backstory": "You are an expert in curriculum design and educational methodologies...",
#                "tools": ["search_tool", "code_tool"]
#            },
#            {
#                "role": "Instructional Designer",
#                "goal": "Develop instructional materials and resources for the new curriculum",
#                "backstory": "You create educational materials that enhance learning experiences...",
#                "allow_delegation": true
#            }
#        ],
#        "tasks": [
#            {
#                "description": "Design a comprehensive curriculum framework for a new course.",
#                "expected_output": "Curriculum framework document",
#                "agent_role": "Curriculum Developer"
#            },
#            {
#                "description": "Create instructional materials, including lesson plans and worksheets.",
#                "expected_output": "Set of instructional materials for the new curriculum",
#                "agent_role": "Instructional Designer"
#            }
#        ],
#        "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2"
#    }
#'
#curl -X POST http://localhost:8085/start_crew \
#-H "Content-Type: application/json" \
#-d '{
#        "agents": [
#            {
#                "role": "Market Researcher",
#                "goal": "Analyze market trends to inform the marketing strategy",
#                "backstory": "You research market conditions and competitive landscapes...",
#                "tools": ["search_tool"]
#            },
#            {
#                "role": "Campaign Manager",
#                "goal": "Plan and oversee the execution of the marketing campaign",
#                "backstory": "You manage marketing campaigns and ensure their success...",
#                "allow_delegation": false
#            }
#        ],
#        "tasks": [
#            {
#                "description": "Research and summarize current market trends relevant to the campaign.",
#                "expected_output": "Market trends report",
#                "agent_role": "Market Researcher"
#            },
#            {
#                "description": "Develop a detailed marketing campaign plan, including goals and tactics.",
#                "expected_output": "Marketing campaign plan document",
#                "agent_role": "Campaign Manager"
#            }
#        ],
#        "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2"
#    }
#'
#


#curl -N http://localhost:8085/stream/crew/{task_id}
#curl -N http://localhost:8085/stream/crew/de56b40b-63a5-4d80-9cfe-4507a3a944d4