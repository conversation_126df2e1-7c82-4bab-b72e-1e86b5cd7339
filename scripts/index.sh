export opensearch_url="localhost:9200"
# create index
curl --location --request PUT "http://${opensearch_url}/userdocs" \
--header 'Content-Type: application/json' \
--data-raw '{
    "settings": {
        "knn": true,
        "knn.algo_param.ef_search": 100
    },
    "mappings": {
        "properties": {
            "vector_data": {
                "type": "knn_vector",
                "dimension": 3072,
                "method": {
                    "name": "hnsw",
                    "space_type": "cosinesimil",
                    "engine": "nmslib"
                }
            }
        }
    }
}'

#  index documents using pipeline

curl -X GET "http://<opensearch_host>:<opensearch_port>/<index_name>/_search" -H 'Content-Type: application/json' -d '
{
  "size": 1000,
  "query": {
    "term": {
      "document_firebaseurl.keyword": "<firebase_url>"
    }
  },
  "sort": [
    {
      "document_number": {
        "order": "asc"
      }
    }
  ]
}'
