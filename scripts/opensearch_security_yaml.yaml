plugins.security.ssl.transport.pemcert_filepath: diogenes-opensearch-service-admin.pem
plugins.security.ssl.transport.pemkey_filepath: diogenes-opensearch-service-admin-key.pem
plugins.security.ssl.transport.pemtrustedcas_filepath: diogenes-opensearch-service-root-ca.pem
plugins.security.ssl.transport.enforce_hostname_verification: false
plugins.security.ssl.http.enabled: true
plugins.security.ssl.http.pemcert_filepath: diogenes-opensearch-service-admin.pem
plugins.security.ssl.http.pemkey_filepath: diogenes-opensearch-service-admin-key.pem
plugins.security.ssl.http.pemtrustedcas_filepath: diogenes-opensearch-service-root-ca.pem
plugins.security.authcz.admin_dn:
    - "CN=Simon Dai,O=Diogenes Consulting LLC,L=Bellevue,ST=WA,C=US"

openssl x509 -req -in diogenes_opensearch_old/diogenes-opensearch-service-admin.csr  -CA aws_private_ca_certificate.pem -CAkey diogenes_opensearch_old/diogenes-opensearch-service-root-ca-key.pem -CAcreateserial -sha256 -out diogenes-opensearch-service-admin.pem -days 730