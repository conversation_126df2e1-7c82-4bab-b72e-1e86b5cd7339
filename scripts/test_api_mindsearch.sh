curl -X POST "http://localhost:8085/mindsearch/solve" \
-H "Content-Type: application/json" \
-d '{
  "inputs": "Build AI Chatbot using Flutter + python FastAPI + LangChain/LangGraph + Firebase",
  "agent_cfg": {},
  "lang": "en",
  "model_format": "gpt4"
}'

websocat ws://localhost:8085/ws/mindsearch/solve
{
  "inputs": "Build AI Chatbot using Flutter + python FastAPI + LangChain/LangGraph + Firebase",
  "agent_cfg": {},
  "lang": "en",
  "model_format": "gpt4"
}
