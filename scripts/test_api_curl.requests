

curl -X POST http://localhost:8085/ask \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_urls": ["gs://diogenesaichatbot.appspot.com/documents/hWFl8LDzFcMPU442YdUvrFE75M93/document.txt"],
    "query": "Who is <PERSON>?",
    "token": ""
  }'

curl -X POST http://localhost:8085/embed_document \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_urls": ["https://www.nytimes.com/2023/04/17/opinion/mosquitoes-lawns-pesticides.html"],
    "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
    "bool_updateOnExist": true,
    "optional_predefined_content_tofill":[]
  }'

curl -X POST http://localhost:8085/get_document_content \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_urls": ["https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%********************************%252Fresume_simon_dai_rsde.pdf%3Falt%3Dmedia%26token%3Da70a4b49-04df-4782-897a-d9ff0d348508%22%5D"],
    "original_user_id": "8KEKoa6d90dr75AywriRKX3nLIR2"
  }'

curl -X POST http://localhost:8085/embed_document \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_urls": ["https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FSenior%20Software%20Engineer_AWS.txt?alt=media&token=be1c6c96-fd5d-4c21-a610-dc3b00799a99"],
    "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
    "bool_updateOnExist": true,
    "optional_predefined_content_tofill":[]
  }'

curl -X POST http://localhost:8085/embed_document \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_urls": ["https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FSenior%20Software%20Engineer_AWS.txt?alt=media&token=8142e643-0b4e-49da-a0d3-1117c56897a9"],
    "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
    "bool_updateOnExist": true
  }'

curl -X POST http://localhost:8085/embed_document \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_urls": ["https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FResume_dsx_AC.pdf?alt=media&token=e96be140-1190-40cd-a4b7-9f9f23f128b9"],
    "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
    "bool_updateOnExist": true
  }'

curl -X POST http://localhost:8085/ask \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_urls": ["https://www.nytimes.com/2023/04/17/books/review/the-forgotten-girls-monica-potts.html"],
    "query": "Who is Simon Dai?",
    "token": ""
  }'

curl -X POST "http://localhost:8085/get_document_ids_by_firebase_urls" -H "Content-Type: application/json" -d '{
  "original_user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
  "document_firebase_urls": ["https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FResume_dsx_AC.pdf?alt=media&token=e96be140-1190-40cd-a4b7-9f9f23f128b9"]
}'

curl -X POST "http://localhost:8085/assign_documents_to_bot" -H "Content-Type: application/json" -d '
{
  "original_bot_id": "Kvf7t1XriN2m1lbq1CrG",
  "document_firebase_urls": ["https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FResume_Simon_Dai_Quant.pdf?alt=media&token=8060b692-7969-4466-87f4-1bafe0d98fa5"],
  "original_user_id": "8KEKoa6d90dr75AywriRKX3nLIR2"
}'

curl -X POST "http://localhost:8085/remove_documents_from_bot" -H "Content-Type: application/json" -d '
{
  "original_bot_id": "WELDD3vhJjSKMFxnGyzU",
  "document_firebase_urls": ["https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2Fdocument.txt?alt=media&token=f463fd76-1c31-4411-809b-a388ebb58f5c"],
  "original_user_id": "8KEKoa6d90dr75AywriRKX3nLIR2"
}'

curl -X POST http://localhost:8085/ask \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_urls": ["gs://diogenesaichatbot.appspot.com/documents/hWFl8LDzFcMPU442YdUvrFE75M93/document.txt"],
    "query": "Who is Simon Dai?",
    "bot_id" : "Gsw4hcWaf9nPbwb7YUsp",
    "token": ""
  }'

curl -X POST http://localhost:8085/aask \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_urls": ["https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FResume_Simon_Dai_Quant.pdf?alt=media&token=54c01f01-9567-438c-97c7-2aeecb57a76a"],
    "query": "Who is Simon Dai?",
    "bot_id" : "Kvf7t1XriN2m1lbq1CrG",
    "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
    "conversation_id":"rIuLIZKsFKmDVm1FlIXM",
    "token": ""
  }'

#First POST request:
#arduino
#Copy code
curl -X POST http://localhost:8085/chat \
-H "Content-Type: application/json" \
-d '{
  "bot_id": "6yk32hbRaUVGkjdW3G2L",
  "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
  "conversation_id": "sk80gHqUXne27rPYFZeM",
  "user_input": "Hello, how are you?"
}'
#Second POST request:
#arduino
#Copy code
curl -X POST http://localhost:8085/chat \
-H "Content-Type: application/json" \
-d '{
  "bot_id": "6yk32hbRaUVGkjdW3G2L",
  "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
  "conversation_id": "sk80gHqUXne27rPYFZeM",
  "user_input": "Yes, I am very interested, tell me more details about it."
}'
#Third POST request:
#arduino
#Copy code
curl -X POST http://localhost:8085/chat \
-H "Content-Type: application/json" \
-d '{
  "bot_id": "6yk32hbRaUVGkjdW3G2L",
  "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
  "conversation_id": "sk80gHqUXne27rPYFZeM",
  "user_input": "I have 10 years of software development experience in Java, Python, machine learning, cloud computing, mobile/web development."
}'
#Fourth POST request:
#arduino
#Copy code
curl -X POST http://localhost:8085/chat \
-H "Content-Type: application/json" \
-d '{
  "bot_id": "6yk32hbRaUVGkjdW3G2L",
  "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
  "conversation_id": "sk80gHqUXne27rPYFZeM",
  "user_input": "I am open to relocation and do not need sponsorship."
}'
#Fifth POST request:
#arduino
#Copy code
curl -X POST http://localhost:8085/chat \
-H "Content-Type: application/json" \
-d '{
  "bot_id": "6yk32hbRaUVGkjdW3G2L",
  "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
  "conversation_id": "sk80gHqUXne27rPYFZeM",
  "user_input": "I am flexible with compensation and no concrete numbers for it."
}'

curl -X POST http://localhost:8085/achat \
-H "Content-Type: application/json" \
-d '{
  "bot_id": "6yk32hbRaUVGkjdW3G2L",
  "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
  "conversation_id": "sk80gHqUXne27rPYFZeM",
  "user_input": "Hello, how are you?"
}'

curl -X POST http://localhost:8085/aask \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_urls": ["https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FResume_Simon_Dai_Quant.pdf?alt=media&token=54c01f01-9567-438c-97c7-2aeecb57a76a"],
    "query": "Who is Simon Dai?",
    "bot_id" : "Kvf7t1XriN2m1lbq1CrG",
    "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
    "conversation_id":"AQ5CB3NKfJYy5qk7MIQz",
    "token": ""
  }'

curl -X POST http://localhost:8085/aask \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_urls": ["https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%********************************%252Fresume_simon_dai_rsde.pdf%3Falt%3Dmedia%26token%3Da70a4b49-04df-4782-897a-d9ff0d348508%22%5D"],
    "query": "What is New York Times US headline today?",
    "bot_id" : "s4lJ62EVy8h262Wjreqj",
    "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
    "conversation_id":"dL4OXrR53YwMw2c8JSym",
    "token": ""
  }'

curl -X POST http://localhost:8085/achat \
-H "Content-Type: application/json" \
-d '{
  "bot_id": "6yk32hbRaUVGkjdW3G2L",
  "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
  "conversation_id": "E2IUtvvDbEUZ1Cx2WR4L",
  "user_input": "Hello, how are you? Do you know Simon Dai?"
}'

curl -X POST http://localhost:8085/achat \
-H "Content-Type: application/json" \
-d '{
  "bot_id": "6yk32hbRaUVGkjdW3G2L",
  "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
  "conversation_id": "E2IUtvvDbEUZ1Cx2WR4L",
  "user_input": "Hello, how are you? Do you have a job description of software engineer?"
}'

