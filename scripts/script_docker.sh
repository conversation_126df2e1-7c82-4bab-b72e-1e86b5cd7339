docker build --platform linux/amd64 -f Dockerfile_local  -t diogeneschatbotembeddingserver .

docker build --platform linux/arm64 -f Dockerfile_local  -t diogeneschatbotembeddingserver .

#gunicorn  --bind 0.0.0.0:8085 main:app
#
#
#univcorn --host 0.0.0.0:8085 main:app

source ~/.bashrc
source ~/.bash_profile
conda activate py311
source .env.development
export FIREBASE_SERVICE_ACCOUNT_KEY=$(cat service-account-key.json)

docker run -v /Users/<USER>/git/diogeneschatbotembeddingserver/PDF-Extract-Kit/models:/app/PDF-Extract-Kit/models -p 8085:8080 \
 -e "OPENAI_API_KEY=$OPENAI_API_KEY" \
 -e "FIREBASE_SERVICE_ACCOUNT_KEY=$FIREBASE_SERVICE_ACCOUNT_KEY" \
 -e "AWS_OPENSEARCH_ADMIN_PASSWORD=$AWS_OPENSEARCH_ADMIN_PASSWORD" \
 -e "GOOGLE_CSE_ID=$GOOGLE_CSE_ID" \
  -e "SERPER_API_KEY=$SERPER_API_KEY" \
 -e "GOOGLE_API_KEY=$GOOGLE_API_KEY" \
 -e "GPLACES_API_KEY=$GPLACES_API_KEY" \
 -e "HUGGINGFACE_TOKEN=$HUGGINGFACE_TOKEN" \
 -e "LANGCHAIN_API_KEY=$LANGCHAIN_API_KEY" \
 -e "REPLICATE_TOKEN=$REPLICATE_TOKEN" \
 -e "ALPHAVANTAGE_API_KEY=$ALPHAVANTAGE_API_KEY" \
 -e "WEAVIATE_URL=$WEAVIATE_URL" \
 -e "WEAVIATE_API_KEY=$WEAVIATE_API_KEY" \
 -e "ANTHROPIC_API_KEY=$ANTHROPIC_API_KEY" \
 -e "QDRANT_API_KEY=$QDRANT_API_KEY" \
 -e "QDRANT_ENDPOINT=$QDRANT_ENDPOINT" \
 -e "QDRANT_URL=$QDRANT_ENDPOINT" \
 -e "OAI_CONFIG_LIST=$OAI_CONFIG_LIST" \
 -e "GROQ_API_KEY=$GROQ_API_KEY" \
 -e "JINA_API_KEY=$JINA_API_KEY" \
 -e "COHERE_API_KEY=$COHERE_API_KEY" \
 -e "GOOGLE_MAPS_API_KEY=$GOOGLE_MAPS_API_KEY" \
 -e "DEEPGRAM_API_KEY=$DEEPGRAM_API_KEY" \
 -e "LIVEKIT_API_KEY=$LIVEKIT_API_KEY" \
 -e "LIVEKIT_API_SECRET=$LIVEKIT_API_SECRET" \
 -e "STRIPE_SECRET=$STRIPE_SECRET" \
 -e "STRIPE_WEBHOOK_ENCRIPTION_SECRET=$STRIPE_WEBHOOK_ENCRIPTION_SECRET" \
  -e "TRACELOOP_API_KEY=$TRACELOOP_API_KEY" \
   -e "MW_TARGET=$MW_TARGET" \
    -e "MW_API_KEY=$MW_API_KEY" \
    -e "SENTRY_DSN=$SENTRY_DSN" \
  -e "ENABLE_HEAVY_TASKS=True" \
 diogeneschatbotembeddingserver


 docker run  -p 8085:8080 \
 -e "OPENAI_API_KEY=$OPENAI_API_KEY" \
 -e "FIREBASE_SERVICE_ACCOUNT_KEY=$FIREBASE_SERVICE_ACCOUNT_KEY" \
 -e "AWS_OPENSEARCH_ADMIN_PASSWORD=$AWS_OPENSEARCH_ADMIN_PASSWORD" \
 -e "GOOGLE_CSE_ID=$GOOGLE_CSE_ID" \
  -e "SERPER_API_KEY=$SERPER_API_KEY" \
 -e "GOOGLE_API_KEY=$GOOGLE_API_KEY" \
 -e "GPLACES_API_KEY=$GPLACES_API_KEY" \
 -e "HUGGINGFACE_TOKEN=$HUGGINGFACE_TOKEN" \
 -e "LANGCHAIN_API_KEY=$LANGCHAIN_API_KEY" \
 -e "REPLICATE_TOKEN=$REPLICATE_TOKEN" \
 -e "ALPHAVANTAGE_API_KEY=$ALPHAVANTAGE_API_KEY" \
 -e "WEAVIATE_URL=$WEAVIATE_URL" \
 -e "WEAVIATE_API_KEY=$WEAVIATE_API_KEY" \
 -e "ANTHROPIC_API_KEY=$ANTHROPIC_API_KEY" \
 -e "QDRANT_API_KEY=$QDRANT_API_KEY" \
 -e "QDRANT_ENDPOINT=$QDRANT_ENDPOINT" \
 -e "QDRANT_URL=$QDRANT_ENDPOINT" \
 -e "OAI_CONFIG_LIST=$OAI_CONFIG_LIST" \
 -e "GROQ_API_KEY=$GROQ_API_KEY" \
 -e "JINA_API_KEY=$JINA_API_KEY" \
 -e "COHERE_API_KEY=$COHERE_API_KEY" \
 -e "GOOGLE_MAPS_API_KEY=$GOOGLE_MAPS_API_KEY" \
 -e "DEEPGRAM_API_KEY=$DEEPGRAM_API_KEY" \
 -e "LIVEKIT_API_KEY=$LIVEKIT_API_KEY" \
 -e "LIVEKIT_API_SECRET=$LIVEKIT_API_SECRET" \
 -e "STRIPE_SECRET=$STRIPE_SECRET" \
 -e "STRIPE_WEBHOOK_ENCRIPTION_SECRET=$STRIPE_WEBHOOK_ENCRIPTION_SECRET" \
  -e "TRACELOOP_API_KEY=$TRACELOOP_API_KEY" \
   -e "MW_TARGET=$MW_TARGET" \
    -e "MW_API_KEY=$MW_API_KEY" \
    -e "SENTRY_DSN=$SENTRY_DSN" \
  -e "ENABLE_HEAVY_TASKS=True" \
 diogeneschatbotembeddingserver
