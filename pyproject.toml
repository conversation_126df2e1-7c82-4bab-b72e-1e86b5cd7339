[project]
name = "diogeneschatbotembeddingserver"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = "==3.12.9"
dependencies = [
    "langchain-community==0.2.17",
    "langchain==0.2.16",
    "langchainhub==0.1.21",
    "langchain-core==0.2.40",
    "langchain-openai==0.1.25",
    "langchain-groq==0.1.10",
    "langchain-huggingface==0.0.3",
    "langchain-google-community==1.0.8",
    "langchain-chroma==0.1.4",
    "openai==1.46.1",
    "torch==2.3.1",
    "torchvision==0.18.1",
    "torchaudio==2.3.1",
    "knowledge-storm==1.0.0",
    "goldenverba",
    "semantic-kernel",
    "langgraph==0.2.31",
    "langgraph-checkpoint==1.0.14",
    "langsmith==0.1.123",
    "duckduckgo-search==5.3.1b1",
    "faiss-cpu==1.8.0",
    "beautifulsoup4==4.12.3",
    "google-cloud==0.34.0",
    "fastapi",
    "python-dotenv==1.0.1",
    "google-cloud-storage==2.18.1",
    "google-search-results==2.4.1",
    "tiktoken==0.7.0",
    "firebase-admin==6.5.0",
    "uvicorn",
    "sse_starlette==1.8.2",
    "debugpy==1.8.5",
    "opensearch-py==2.7.1",
    "certifi==2024.8.30",
    "pypdf==4.3.1",
    "requests==2.32.3",
    "html2text==2024.2.26",
    "asyncio==3.4.3",
    "fake_useragent",
    "pexpect",
    "pytest",
    "scrapy==2.11.2",
    "google-api-python-client==2.142.0",
    "libmagic==1.0",
    "python-magic==0.4.27",
    "pytesseract==0.3.13",
    "transformers==4.40.0",
    "sentence-transformers==3.1.1",
    "wikipedia==1.4.0",
    "playwright==1.46.0",
    "lxml",
    "greenlet",
    "nest_asyncio==1.6.0",
    "weaviate-client==4.8.1",
    "langchain-weaviate==0.0.3",
    "redis==5.1.0b7",
    "redisvl==0.3.3",
    "arxiv==2.1.3",
    "pymupdf==1.24.10",
    "yfinance==0.2.43",
    "opencv-python==********",
    "scikit-image==0.24.0",
    "docarray==0.40.0",
    "spacy==3.7.6",
    "alpha_vantage==3.0.0",
    "pandas==2.2.2",
    "pandoc==2.4",
    "httpx==0.27.0",
    "matplotlib==3.9.1",
    "google-cloud-aiplatform==1.67.1",
    "google-cloud-speech==2.27.0",
    "googlemaps==4.10.0",
    "scipy==1.14.1",
    "nltk==3.9.1",
    "anthropic==0.34.2",
    "langserve[all]==0.2.3",
    "promptflow[azure]==1.17.2",
    "youtube_search==2.1.2",
    "pyautogen==0.4.0",
    "autogen",
    "crewai==0.36.1",
    "crewai-tools==0.12.1",
    "scrapegraphai>=1.19.0",
    "pydantic>=2.7.4",
    "opentelemetry-api>=1.27.0",
    "opentelemetry-sdk>=1.27.0",
    "opentelemetry-instrumentation-fastapi>=0.48b0",
    "opentelemetry-instrumentation-celery>=0.48b0",
    "opentelemetry-instrumentation-redis>=0.48b0",
    "opentelemetry-instrumentation-logging>=0.48b0",
    "opentelemetry-instrumentation-langchain>=0.33.7",
    "opentelemetry-exporter-otlp>=1.27.0",
    "opentelemetry-exporter-otlp-proto-http>=1.27.0",
    "opentelemetry-exporter-otlp-proto-grpc>=1.27.0",
    "opentelemetry-distro>=0.48b0",
    "sentry-sdk[fastapi]",
    "traceloop-sdk>=0.33.7",
    "lancedb>=0.5.7",
    "flower>=2.0.1",
    "google_cloud_resource_manager>=1.12.5",
    "google_cloud_speech>=2.27.0",
    "google-cloud-firestore>=2.18.0",
    "grpcio_health_checking",
    "protobuf>=4.25.3",
    "googleapis_common_protos>=1.65.0",
    "e2b-code-interpreter>=1.1.1",
    "e2b>=1.3.1",
    "browserbase>=0.3.0",
    "lagent>=0.2.3",
    "janus>=1.0.0",
    "gpt-researcher>=0.9.6",
    "loguru>=0.7.2",
    "griffe>=1.3.1",
    "unstructured>=0.15.12",
    "onnxruntime>=1.19.2",
    "langchain_anthropic>=0.1.23",
    "langchain-google-vertexai>=1.0.10",
    "burr>=0.30.1",
    "semantic-chunkers==0.0.9",
    "semantic-router>=0.0.65",
    "langchain-text-splitters>=0.2.4",
    "groq>=0.11.0",
    "cohere>=5.6.2",
    "asyncer>=0.0.8",
    "flaml>=2.3.0",
    "xgboost>=2.1.1",
    "embedchain==0.1.121",
    "prompttools>=0.0.46",
    "livekit-api>=0.7.0",
    "livekit>=0.16.3",
    "promptic>=0.7.7",
    "tenacity>=8.3.0",
    "confection>=0.1.5",
    "sqlmodel>=0.0.22",
    "rdagent>=0.2.1",
    "unstructured_inference>=0.7.37",
    "ultralytics>=8.3.0",
    "numpy>=1.26.4",
    "lapx>=0.5.10",
    "moviepy>=1.0.3",
    "torchtext>=0.18.0",
    "fast-langdetect>=0.2.0",
    "wordninja>=2.0.0",
    "boto3",
    "Brotli>=1.1.0",
    # "unimernet",
    "paddleocr>=2.7.3",
    "paddlepaddle>=3.0.0b1",
    "pycocotools>=2.0.8",
    "htmltabletomd>=1.0.0",
    "pypandoc>=1.13",
    "pyopenssl>=24.0.0",
    "struct-eqtable>=0.1.0",
    "statistics>=*******",
    "onnxslim>=0.1.34",
    "gevent>=24.2.1",
    "Pillow>=10.4.0",
    "pi_heif>=0.18.0",
    "docling",
    "stripe>=11.2.0",
    "r2r",
    "portkey-ai",
    "agenta",
    "litellm",
    "openmeter>=1.0.0b188",
    "llama-index-core",
    "llama-index-llms-openai",
    "supabase",
    "sqlalchemy",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-cov",
    "pytest-mock",
    "debugpy>=1.8.5",
]
test = [
    "pytest",
]

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["app"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"

[tool.black]
line-length = 88
target-version = ["py312"]
include = '\.pyi?$'
