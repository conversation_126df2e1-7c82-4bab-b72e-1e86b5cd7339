import os
from langchain.chains import VectorDBQA
from langchain_community.chat_models import ChatOpenAI
from langchain_community.document_loaders import UnstructuredFileLoader
from langchain_community.embeddings import OpenAIEmbeddings
from langchain_community.vectorstores import Chroma
from langchain_text_splitters import CharacterTextSplitter

# Set your OpenAI API key here
os.environ["OPENAI_API_KEY"] = os.environ.get("OPENAI_API_KEY")

# Load the document
loader = UnstructuredFileLoader("tests/document.txt")
documents = loader.load()

# Split the text to chunks of 1000 characters
text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)
texts = text_splitter.split_documents(documents)

# Calculate embeddings for the text
embeddings = OpenAIEmbeddings()

# Create a vector database and index the embeddings
db = Chroma.from_documents(texts, embeddings)

# Initialize the langchain chain for question answering
qa = VectorDBQA.from_chain_type(
    llm=ChatOpenAI(), chain_type="stuff", vectorstore=db, k=1
)

# Ask questions about the data
query = "Who is <PERSON> Dai?"
print(qa.run(query))

query = "Explain CTERA's caching technology in the language of a pirate"
print(qa.run(query))
