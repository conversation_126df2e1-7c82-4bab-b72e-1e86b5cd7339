from app.db.opensearch_crud import OpenSearchVectorDatabaseCRUD
from google.cloud import storage
import json
import firebase_admin
from firebase_admin import credentials
import logging
import time
from opensearchpy import OpenSearch
import base64

import os

from app.utils.document_embedding import DocumentEmbedding
from langchain_community.embeddings import OpenAIEmbeddings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

firebase_service_account_key = json.loads(
    os.environ.get("FIREBASE_SERVICE_ACCOUNT_KEY")
)
cred = credentials.Certificate(firebase_service_account_key)

try:
    default_app = firebase_admin.get_app()
except ValueError:
    firebase_admin.initialize_app(cred)

storage_client = storage.Client.from_service_account_info(firebase_service_account_key)

# Dictionary to store the temporary directories created for each user
user_temp_dirs = {}

def get_user_temp_dir(user_id):
    if user_id in user_temp_dirs:
        return user_temp_dirs[user_id]

    temp_dir = f"/tmp/{user_id}/"
    os.makedirs(temp_dir, exist_ok=True)
    user_temp_dirs[user_id] = temp_dir

    return temp_dir

total_start_time = time.time()
# firebase_urls = [ 'https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2FhWFl8LDzFcMPU442YdUvrFE75M93%2Fdocument.txt?alt=media&token=597468bb-84f1-4455-944c-ef8f8769b699']

# 'https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FApology%20by%20Plato.txt.txt?alt=media&token=8a84dc6e-87a7-4031-8086-7676269b00f2',

# firebase_urls = ['https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FResume_Simon_Dai_RSDE.pdf?alt=media&token=07f9bd9f-656a-494d-bd75-9a87105b2310']
# firebase_urls = ['https://www.nytimes.com/2023/04/17/us/politics/2024-presidential-campaign-trail.html']
firebase_urls = [
    "https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2Fdocument.txt?alt=media&token=ebf7f88e-d626-4a8c-a698-a724159f1d61"
]
query = "who is Simon Dai?"
# query = "What is Plato's Apology about?"

logger.info("Received request with firebase_urls=%s, query=%s", firebase_urls, query)

user_id = "8KEKoa6d90dr75AywriRKX3nLIR2"
# user_id = "hWFl8LDzFcMPU442YdUvrFE75M93"
opensearchUrl = "http://**************:9200"
opensearch_host = "**************"
opensearch_port = "9200"

# For testing only. Don't store credentials in code.
opensearch_auth = (
    "admin",
    os.environ.get("AWS_OPENSEARCH_ADMIN_PASSWORD"),
)  # For testing only. Don't store credentials in code.

all_texts = []

new_documents_count = 0
new_embeddings_count = 0
cached_embeddings_count = 0
dir_path = get_user_temp_dir(user_id)
os_client = OpenSearch(
    hosts=[{"host": opensearch_host, "port": opensearch_port}],
    http_compress=True,  # enables gzip compression for request bodies
    http_auth=opensearch_auth,
    use_ssl=False,
    verify_certs=False,
)
diogenes_search = OpenSearchVectorDatabaseCRUD(os_client)
diogenes_search.create_user_index(user_id)
start_time = time.time()
vector_database_embedding = DocumentEmbedding()
vector_database_embedding.embed_document_from_file_url(firebase_urls, user_id, False)
#  use user id and bot id to limit index
encoded_user_id = (
    base64.b32encode(user_id.encode("utf-8")).decode("utf-8").rstrip("=").lower()
)
embeddings = OpenAIEmbeddings()
query_embeddings = embeddings.embed_query(query)
docs = diogenes_search.search_document_by_embedding(query_embeddings, user_id)
end_time = time.time()
logger.info(f"Created vector database in {end_time - start_time} seconds")

start_time = time.time()
content = diogenes_search.get_document_content_by_firebase_urls(firebase_urls, user_id)
print(content)
end_time = time.time()
logger.info(f"Found answer in {end_time - start_time} seconds")
total_end_time = time.time()
logger.info(f"Total time in {total_end_time - total_start_time} seconds")
