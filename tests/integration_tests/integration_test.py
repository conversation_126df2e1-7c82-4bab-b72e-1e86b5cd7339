import json
import unittest
import requests


import json
import unittest
import requests

import json
import unittest
import requests

class TestFlaskAPI(unittest.TestCase):
    BASE_URL = "http://localhost:8085"

    def test_ask(self):
        url = f"{self.BASE_URL}/ask"
        headers = {"Content-Type": "application/json"}
        payload = {
            "firebase_urls": [
                "gs://diogenesaichatbot.appspot.com/documents/hWFl8LDzFcMPU442YdUvrFE75M93/document.txt"
            ],
            "query": "Who is <PERSON>?",
            "token": "",
        }

        response = requests.post(url, headers=headers, data=json.dumps(payload))

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertIn("answer", response_data)  # Check if the response contains an answer

    def test_embed_document(self):
        url = f"{self.BASE_URL}/embed_document"
        headers = {"Content-Type": "application/json"}
        payload = {
            "firebase_urls": [
                "https://www.nytimes.com/2023/04/17/opinion/mosquitoes-lawns-pesticides.html"
            ],
            "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
            "bool_updateOnExist": True,
            "optional_predefined_content_tofill": []
        }

        response = requests.post(url, headers=headers, data=json.dumps(payload))

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data["status"], "success")  # Check if the embedding was successful

if __name__ == "__main__":
    unittest.main()
    BASE_URL = "http://localhost:8085"

    def test_ask(self):
        url = f"{self.BASE_URL}/ask"
        headers = {"Content-Type": "application/json"}
        payload = {
            "firebase_urls": [
                "gs://diogenesaichatbot.appspot.com/documents/hWFl8LDzFcMPU442YdUvrFE75M93/document.txt"
            ],
            "query": "Who is Simon Dai?",
            "token": "",
        }

        response = requests.post(url, headers=headers, data=json.dumps(payload))

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertIn("answer", response_data)  # Check if the response contains an answer

    def test_embed_document(self):
        url = f"{self.BASE_URL}/embed_document"
        headers = {"Content-Type": "application/json"}
        payload = {
            "firebase_urls": [
                "https://www.nytimes.com/2023/04/17/opinion/mosquitoes-lawns-pesticides.html"
            ],
            "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
            "bool_updateOnExist": True,
            "optional_predefined_content_tofill": []
        }

        response = requests.post(url, headers=headers, data=json.dumps(payload))

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data["status"], "success")  # Check if the embedding was successful

if __name__ == "__main__":
    unittest.main()
    BASE_URL = "http://localhost:8085"

    def test_ask(self):
        url = f"{self.BASE_URL}/ask"
        headers = {"Content-Type": "application/json"}
        payload = {
            "firebase_urls": [
                "gs://diogenesaichatbot.appspot.com/documents/hWFl8LDzFcMPU442YdUvrFE75M93/document.txt"
            ],
            "query": "Who is Simon Dai?",
            "token": "",
        }

        response = requests.post(url, headers=headers, data=json.dumps(payload))

        self.assertEqual(response.status_code, 200)
        # Add more assertions to check the response content

    def test_embed_document(self):
        url = f"{self.BASE_URL}/embed_document"
        headers = {"Content-Type": "application/json"}
        payload = {
            "firebase_urls": [
                "https://www.nytimes.com/2023/04/17/opinion/mosquitoes-lawns-pesticides.html"
            ],
            "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
            "bool_updateOnExist": True,
        }

        response = requests.post(url, headers=headers, data=json.dumps(payload))

        self.assertEqual(response.status_code, 200)
        # Add more assertions to check the response content


if __name__ == "__main__":
    unittest.main()
