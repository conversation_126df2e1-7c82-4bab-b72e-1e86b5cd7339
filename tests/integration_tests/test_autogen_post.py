import pytest
import httpx
from fastapi import FastAP<PERSON>
from fastapi.testclient import <PERSON><PERSON>lient
from main import app  # Adjust the import based on your FastAPI app location

# Create a TestClient for sending requests to your FastAPI app
client = TestClient(app)

@pytest.mark.asyncio
async def test_start_autogen_successful_case():
    response = await client.post(
        "/start_autogen",
        json={
            "agents": [
                {
                    "role": "researcher",
                    "goal": "compile report",
                    "backstory": "Expert in data analysis",
                    "allow_delegation": True,
                    "tools": ["data_analyzer", "chart_creator"]
                }
            ],
            "tasks": [
                {
                    "description": "Analyze dataset",
                    "expected_output": "Summary report",
                    "agent_role": "researcher"
                }
            ],
            "building_task": "Write a report about: Building AI Chatbot using Flutter/Firebase and Python/FastAPI/LangChain"
        }
    )

    assert response.status_code == 200
    data = response.json()
    assert "task_id" in data
    assert isinstance(data["task_id"], str)

@pytest.mark.asyncio
async def test_start_autogen_edge_case():
    response = await client.post(
        "/start_autogen",
        json={}
    )

    assert response.status_code == 200
    data = response.json()
    assert "task_id" in data
    assert isinstance(data["task_id"], str)
