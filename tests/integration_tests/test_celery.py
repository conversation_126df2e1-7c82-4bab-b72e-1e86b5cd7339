from app.queue.celery_app import celery
from app.queue.autogen_tasks import async_kickoff_autogen

# Submit a task
result = async_kickoff_autogen.apply_async(args=[{
           "agents": [
             {
               "role": "researcher",
               "goal": "compile report",
               "backstory": "Expert in data analysis",
               "allow_delegation":True,
               "tools": ["data_analyzer", "chart_creator"]
             }
           ],
           "tasks": [
             {
               "description": "Analyze dataset",
               "expected_output": "Summary report",
               "agent_role": "researcher"
             }
           ],
           "building_task": "Write a report about: Building AI Chatbot using Flutter/Firebase and Python/FastAPI/LangChain"
         }])
print(result.get())
