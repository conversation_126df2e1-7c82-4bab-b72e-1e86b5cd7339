# Set up of your agent
import os

from model.agent_gpt import AgentGPT
from langchain_community.chat_models import ChatOpenAI

os.environ["OPENAI_API_KEY"] = os.environ.get("OPENAI_API_KEY")

# Conversation stages - can be modified
conversation_stages = {   '1': 'Introduction: Start the conversation by introducing yourself and your company. Be polite and respectful while keeping the tone of the conversation professional.',
                          '2': 'Job Description: Provide a brief overview of the job position, including the key responsibilities and requirements. Ensure that the candidate understands the role they are applying for.',
                          '3': 'Experience Inquiry: Ask the candidate about their previous work experience, including their roles, responsibilities, and achievements. Confirm if their experience aligns with the job requirements.',
                          '4': 'Motivation Inquiry: Ask the candidate about their motivation for seeking a new job and why they are interested in the position. Understand their career goals and aspirations.',
                          '5': 'Relocation and Visa: Inquire if the candidate is open to relocation if the job requires it. Ask about their visa status and if they require visa sponsorship.',
                          '6': 'Package Expectation: Ask the candidate about their salary expectations and any other benefits they are looking for. Discuss the compensation package offered by the company.',
                          '7': 'Close: Conclude the conversation by proposing the next steps, such as scheduling an interview or meeting with the hiring manager. Summarize what has been discussed and reiterate the benefits of joining the company.',
                          }

# Agent characteristics - can be modified
config = dict(agent_name="Jeff Bezos",
              agent_role="Senior Technical Agent",
              company_name="Amazon",
              company_business="Amazon is a multinational technology company that focuses on e-commerce, cloud computing, digital streaming, and artificial intelligence. We are one of the world's largest online retailers and a prominent cloud services provider.",
              company_values="At Amazon, our mission is to be Earth's most customer-centric company. We are guided by four principles: customer obsession, passion for invention, commitment to operational excellence, and long-term thinking. We strive to empower our employees and create a diverse and inclusive workplace.",
              conversation_purpose="discuss potential job opportunities and assess the candidate's interest and qualifications for the available positions.",
              conversation_history='Hello, this is Ted Lasso from Amazon. How are you doing today? <END_OF_TURN>\nCandidate: I am well, how are you?<END_OF_TURN>',
              conversation_type="call",
              conversation_stage=conversation_stages.get('1', "Introduction: Start the conversation by introducing yourself and your company. Be polite and respectful while keeping the tone of the conversation professional.")
              )

verbose=True
llm = ChatOpenAI(temperature=0.9)

agent_agent = AgentGPT.from_llm(llm, verbose=False, **config)
# init agent agent
agent_agent.seed_agent()
agent_agent.determine_conversation_stage()
agent_agent.step()
agent_agent.human_step("Yea sure")
agent_agent.determine_conversation_stage()
agent_agent.step()
agent_agent.human_step("Tell me more details")
agent_agent.determine_conversation_stage()
agent_agent.step()
agent_agent.human_step("I have 10 years of full stack development experience in Java, Python, web, mobile, cloud computing.")
agent_agent.determine_conversation_stage()
agent_agent.step()
agent_agent.human_step("I want to find a better position that has better work life balance.")
agent_agent.determine_conversation_stage()
agent_agent.step()
agent_agent.human_step("I am open to relocation and do not need visa sponsorship.")
agent_agent.determine_conversation_stage()
agent_agent.step()
agent_agent.human_step("I am open to relocation and do not need visa sponsorship.")
agent_agent.determine_conversation_stage()
agent_agent.step()
agent_agent.human_step("I work 24 hours a day and 7 days a week and I do not need salary.")
agent_agent.determine_conversation_stage()
agent_agent.step()