"""
Integration test demonstrating how to mock Firebase authentication.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch

# Import the main FastAPI app - adjust import as needed
# from app.main import app


@pytest.fixture
def client_with_mocked_auth():
    """
    Create a test client with mocked Firebase authentication.

    This fixture demonstrates how to patch the authentication functions
    for a whole test suite.
    """
    # Mock user data to return
    mock_user = {
        "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
        "email": "<EMAIL>",
    }

    # Setup the patches
    with patch("app.config.verify_token.verify_firebase_token") as mock_verify:
        # Make it return our mock user
        mock_verify.return_value = mock_user

        # You would normally use the actual app
        # client = TestClient(app)

        # For demonstration purposes only
        mock_client = object()  # Replace with TestClient(app) in real tests
        yield mock_client


def test_protected_endpoint(client_with_mocked_auth):
    """
    Test a protected endpoint with mocked authentication.

    This test demonstrates how to make requests to protected endpoints
    without needing real Firebase credentials.
    """
    # Example of how you would test a protected endpoint
    # response = client_with_mocked_auth.get("/protected-endpoint")
    # assert response.status_code == 200

    # For demonstration only
    assert client_with_mocked_auth is not None


@pytest.mark.parametrize(
    "user_id,email",
    [
        ("test-user-1", "<EMAIL>"),
        ("test-user-2", "<EMAIL>"),
    ],
)
def test_with_different_users(user_id, email):
    """
    Test with different user identities.

    This test demonstrates how to test with different mock users.
    """
    mock_user = {"user_id": user_id, "email": email}

    with patch("app.config.verify_token.verify_firebase_token") as mock_verify:
        mock_verify.return_value = mock_user

        # In a real test you would:
        # client = TestClient(app)
        # response = client.get("/user-specific-endpoint")
        # assert response.status_code == 200

        # For demonstration only
        assert mock_user["user_id"] == user_id
        assert mock_user["email"] == email
