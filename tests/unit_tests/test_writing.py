import unittest
from unittest.mock import AsyncMock, Mock
from fastapi import <PERSON><PERSON><PERSON>, Request
from google.cloud import storage
from google.cloud.firestore_v1.async_client import AsyncClient

from app.api.writing import writing, parse_writing_request_data
from app.config.config import setup_envs
from app.models.model import WritingRequestData
from app.models.writing import ResearchState, Outline, Section


class TestWritingProcess(unittest.TestCase):
    def setUp(self):
        # setup environment variables
        setup_envs()
        self.app = FastAPI()
        self.request = Mock(Request)
        self.db = AsyncMock(AsyncClient)
        self.storage_client = Mock(storage.Client)
        self.storage_bucket = Mock(storage.Bucket)
        self.storage_client.bucket.return_value = self.storage_bucket

    async def test_writing_process(self):
        # Mock the dependencies
        self.request.json.return_value = {"topic": "New Topic"}
        parse_writing_request_data.return_value = WritingRequestData(user_id="user1", topic="New Topic")

        # Mock the async functions
        update_progress_mock = AsyncMock()
        upload_to_firebase_mock = AsyncMock()
        awrite_mock = AsyncMock(return_value=ResearchState(
            article="Article generated using STORMWikiRunner.",
            outline=Outline(page_title="sample title", sections=[
                Section(section_title="Introduction", description="Introduction content")
            ])
        ))

        with self.app.test_request_context():
            # Replace the original functions with the mocks
            writing.update_progress = update_progress_mock
            writing.upload_to_firebase = upload_to_firebase_mock
            writing.awrite = awrite_mock

            # Call the writing function
            response = await writing(request=self.request, db=self.db, storage=self.storage_client)

            # Assert the expected behavior
            update_progress_mock.assert_awaited_once()
            upload_to_firebase_mock.assert_awaited_once()
            awrite_mock.assert_awaited_once()
            self.assertEqual(response, {"session_id": "some_session_id"})

if __name__ == "__main__":
    unittest.main()