import json
import logging
import os

import firebase_admin
import pytest
from firebase_admin import firestore, credentials
from fastapi.testclient import TestClient

from main import app  # Import your FastAPI app
from app.utils.util import create_conversation

# Define test data

logger = logging.getLogger("AgentGPT_test_")

# Define expected response (modify based on your expected response)
expected_response = {"ai_response": "success", "message": "Chat processed successfully"}

firebase_service_account_key = json.loads(
    os.environ.get("FIREBASE_SERVICE_ACCOUNT_KEY")
)
cred = credentials.Certificate(firebase_service_account_key)

try:
    default_app = firebase_admin.get_app()
except ValueError:
    firebase_admin.initialize_app(cred)


@pytest.fixture
def client():
    with TestClient(app) as client:
        yield client


def test_embed_document():
    # Define the test data for the request
    test_data = {
        "firebase_urls": [
            "https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FResume_Simon_Dai_Quant.pdf?alt=media&token=8060b692-7969-4466-87f4-1bafe0d98fa5"  # noqa: E501
        ],
        "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
        "bool_updateOnExist": True,
    }

    # Create a test client for the FastAPI app
    with TestClient(app) as client:
        # Make a POST request to the /embed_document endpoint with the test data
        response = client.post(
            "/embed_document",
            json=test_data,
        )

        # Check the status code of the response
        assert response.status_code == 200

        # Parse the response JSON
        response_json = response.json()

        # Check the content of the response
        assert "status" in response_json
        assert (
            response_json["status"] == "success"
        )  # Update this based on the expected value


def test_aask(client):
    bot_id = "Kvf7t1XriN2m1lbq1CrG"
    user_id = "8KEKoa6d90dr75AywriRKX3nLIR2"
    # Get Firestore client
    db = firestore.client()
    conversation_id = create_conversation(db, user_id, bot_id)
    logger.error(f"conversation_id crated: {str(conversation_id)}")
    # Define the test data for the request
    test_data = {
        "filebase_urls": [
            "https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FResume_Simon_Dai_Quant.pdf?alt=media&token=54c01f01-9567-438c-97c7-2aeecb57a76a"  # noqa: E501
        ],
        "bot_id": bot_id,
        "user_id": user_id,
        "conversation_id": conversation_id,
        "query": "Who is Simon Dai?",
    }

    # Make a POST request to the /aask endpoint with the test data
    response = client.post("/aask", json=test_data)

    # Check the status code of the response
    assert response.status_code == 200

    # Read the streamed data from the response
    streamed_data = response.text

    # Check the content of the streamed data
    assert streamed_data != "Error processing query"
    # assert "Simon Dai" in streamed_data  # Add more specific checks

    # Perform additional checks based on the expected response content


def test_assign_documents_to_bot(client):
    bot_id = "Kvf7t1XriN2m1lbq1CrG"
    # Define the test data for the request
    test_data = {
        "document_firebase_urls": [
            "https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FResume_Simon_Dai_Quant.pdf?alt=media&token=8060b692-7969-4466-87f4-1bafe0d98fa5"  # noqa: E501
        ],
        "original_bot_id": bot_id,
        "original_user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
    }

    # Make a POST request to the /assign_documents_to_bot endpoint
    response = client.post("/assign_documents_to_bot", json=test_data)

    # Check the status code of the response
    assert response.status_code == 200

    # Check the content of the response
    assert response.text != "Error processing query"
    # Add more specific checks based on the expected output


def test_remove_documents_from_bot(client):
    bot_id = "Kvf7t1XriN2m1lbq1CrG"
    # Define the test data for the request
    test_data = {
        "document_firebase_urls": [
            "https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FResume_Simon_Dai_Quant.pdf?alt=media&token=8060b692-7969-4466-87f4-1bafe0d98fa5"  # noqa: E501
        ],
        "original_bot_id": bot_id,
        "original_user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
    }

    # Make a POST request to the /remove_documents_from_bot endpoint
    response = client.post("/remove_documents_from_bot", json=test_data)

    # Check the status code of the response
    assert response.status_code == 200

    # Check the content of the response
    assert response.text != "Error processing query"
    # Add more specific checks based on the expected output


def test_ask(client):
    bot_id = "Kvf7t1XriN2m1lbq1CrG"
    # Define the test data for the request
    test_data = {
        "filebase_urls": [
            "https://firebasestorage.googleapis.com/v0/b/diogenesaichatbot.appspot.com/o/documents%2F8KEKoa6d90dr75AywriRKX3nLIR2%2FResume_Simon_Dai_Quant.pdf?alt=media&token=8060b692-7969-4466-87f4-1bafe0d98fa5"  # noqa: E501
        ],
        "bot_id": bot_id,
        "query": "Who is Simon Dai?",
    }

    # Make a POST request to the /ask endpoint with the test data
    response = client.post("/ask", json=test_data)

    # Check the status code of the response
    assert response.status_code == 200

    # Check the content of the response
    assert response.text != "Error processing query"
    # Add more specific checks based on the expected output

    # Perform additional checks based on the expected response content


def test_chat_route(client):
    # Make a POST request to the /chat route with test data
    bot_id = "s4lJ62EVy8h262Wjreqj"
    user_id = "8KEKoa6d90dr75AywriRKX3nLIR2"
    # Get Firestore client
    db = firestore.client()
    conversation_id = create_conversation(db, user_id, bot_id)
    logger.error(f"conversation_id crated: {str(conversation_id)}")
    test_data = {
        "bot_id": bot_id,
        "user_id": user_id,
        "conversation_id": conversation_id,
        "user_input": "Hello, how are you?",
    }
    response = client.post("/chat", json=test_data)

    # Check the status code of the response
    assert response.status_code == 200

    test_data = {
        "bot_id": bot_id,
        "user_id": user_id,
        "conversation_id": conversation_id,
        "user_input": "Yes, I am very interested, tell me more details.",
    }
    # Add any additional assertions based on your expected behavior
    response = client.post("/chat", json=test_data)

    # Check the status code of the response
    assert response.status_code == 200

    test_data = {
        "bot_id": bot_id,
        "user_id": user_id,
        "conversation_id": conversation_id,
        "user_input": (
            "I have 10 years of software development experience in Java, "
            "Python, machine learning, cloud computing, mobile/web development."
        ),
    }
    # Add any additional assertions based on your expected behavior
    response = client.post("/chat", json=test_data)

    # Check the status code of the response
    assert response.status_code == 200

    test_data = {
        "bot_id": bot_id,
        "user_id": user_id,
        "conversation_id": conversation_id,
        "user_input": "I am open to relocation and do not need sponsorship.",
    }
    # Add any additional assertions based on your expected behavior
    response = client.post("/chat", json=test_data)

    # Check the status code of the response
    assert response.status_code == 200

    test_data = {
        "bot_id": bot_id,
        "user_id": user_id,
        "conversation_id": conversation_id,
        "user_input": "I am flexible with compensation and no concrete numbers.",
    }
    # Add any additional assertions based on your expected behavior
    response = client.post("/chat", json=test_data)

    # Check the status code of the response
    assert response.status_code == 200


# TODO: Add test for intermediate steps when AgentStageAnalyzerChain and
# AgentConversationChain classes are implemented
