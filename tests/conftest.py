import os
import pytest
import firebase_admin
from firebase_admin import credentials as firebase_admin_credentials
from unittest.mock import patch, MagicMock

# Set environment variables before any imports
os.environ["GROQ_API_KEY"] = "test_dummy_api_key"
os.environ["FIREBASE_SERVICE_ACCOUNT_KEY"] = (
    '{"type": "service_account", "project_id": "test", "private_key_id": "test", "private_key": "-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB\\nUikQpTzxDw2NvM/VBBiYXjsUVkwqLSMhAA1VAmLlT_M3arC9VmpvFqUV5dji_Gwl\\n89BbzNx_6Omhzoe7cM0SirfgHxmb3x2pLuBuDiIXGEVlnAOsErq1cUWw\\n-----<PERSON><PERSON> PRIVATE KEY-----\\n", "client_email": "<EMAIL>", "client_id": "test", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token"}'
)


# Mock Firebase at module level before any imports that might use it
class DummyCertificate:
    def __init__(self, *a, **kw):
        pass


class MockCredentials:
    @classmethod
    def from_service_account_info(cls, info):
        return cls()


class MockAsyncClient:
    def __init__(self, *args, **kwargs):
        pass


class MockStorageClient:
    @classmethod
    def from_service_account_info(cls, info):
        return cls()


# Apply Firebase mocks at module level
patch("firebase_admin.initialize_app", lambda *a, **kw: None).start()
patch("firebase_admin.get_app", lambda: True).start()
patch("firebase_admin.credentials.Certificate", DummyCertificate).start()
patch("google.oauth2.service_account.Credentials", MockCredentials).start()
patch("google.cloud.firestore.AsyncClient", MockAsyncClient).start()
patch("google.cloud.storage.Client", MockStorageClient).start()

# Set additional environment variables for vector databases and APIs
os.environ["AWS_OPENSEARCH_ADMIN_PASSWORD"] = "test_password"
os.environ["WEAVIATE_URL"] = "http://localhost:8080"
os.environ["WEAVIATE_API_KEY"] = "test_api_key"
os.environ["OPENAI_API_KEY"] = "test_openai_key"
os.environ["QDRANT_ENDPOINT"] = "http://localhost:6333"
os.environ["QDRANT_API_KEY"] = "test_qdrant_key"
os.environ["COHERE_API_KEY"] = "test_cohere_key"
os.environ["CO_API_KEY"] = "test_cohere_key"
os.environ["GOOGLE_MAPS_API_KEY"] = "AIzaSyDummyTestKeyForPytest123456789012345"


# Mock vector database clients at module level to prevent import-time initialization
class MockWeaviateClient:
    def __init__(self, *args, **kwargs):
        # Add necessary attributes that the real client would have
        self.batch = MockBatch()
        self.collections = MockCollections()

    @classmethod
    def connect_to_weaviate_cloud(cls, *args, **kwargs):
        return cls()


class MockBatch:
    def fixed_size(self, batch_size=100):
        pass


class MockCollections:
    def get(self, name):
        return MockCollection()


class MockCollection:
    def __init__(self):
        pass


class MockOpenSearchClient:
    def __init__(self, *args, **kwargs):
        pass


class MockCollectionsResponse:
    def __init__(self):
        self.collections = []


class MockQdrantClient:
    def __init__(self, *args, **kwargs):
        pass

    def get_collections(self):
        return MockCollectionsResponse()

    def create_collection(self, *args, **kwargs):
        pass

    def recreate_collection(self, *args, **kwargs):
        pass

    def delete_collection(self, *args, **kwargs):
        pass

    def upsert(self, *args, **kwargs):
        pass

    def search(self, *args, **kwargs):
        return []


class MockAsyncQdrantClient:
    def __init__(self, *args, **kwargs):
        pass

    async def get_collections(self):
        return []

    async def create_collection(self, *args, **kwargs):
        pass

    async def delete_collection(self, *args, **kwargs):
        pass

    async def upsert(self, *args, **kwargs):
        pass

    async def search(self, *args, **kwargs):
        return []


# Apply patches at module level
patch(
    "weaviate.connect_to_weaviate_cloud", MockWeaviateClient.connect_to_weaviate_cloud
).start()
patch("weaviate.Client", MockWeaviateClient).start()
patch("opensearchpy.OpenSearch", MockOpenSearchClient).start()
patch("qdrant_client.QdrantClient", MockQdrantClient).start()
patch("qdrant_client.AsyncQdrantClient", MockAsyncQdrantClient).start()


@pytest.fixture(autouse=True)
def mock_firebase(monkeypatch):
    """Mock Firebase services for testing."""
    monkeypatch.setattr(firebase_admin, "initialize_app", lambda *a, **kw: None)
    monkeypatch.setattr(firebase_admin, "get_app", lambda: True)

    class DummyCertificate:
        def __init__(self, *a, **kw):
            pass

    class MockCredentials:
        @classmethod
        def from_service_account_info(cls, info):
            return cls()

    class MockAsyncClient:
        def __init__(self, *args, **kwargs):
            pass

    class MockStorageClient:
        @classmethod
        def from_service_account_info(cls, info):
            return cls()

    monkeypatch.setattr(firebase_admin_credentials, "Certificate", DummyCertificate)

    # Mock Google Cloud services
    monkeypatch.setattr("google.oauth2.service_account.Credentials", MockCredentials)
    monkeypatch.setattr("google.cloud.firestore.AsyncClient", MockAsyncClient)
    monkeypatch.setattr("google.cloud.storage.Client", MockStorageClient)

    # Mock vector database clients
    class MockOpenSearchClient:
        def __init__(self, *args, **kwargs):
            pass

    class MockWeaviateClient:
        def __init__(self, *args, **kwargs):
            pass

        @classmethod
        def connect_to_weaviate_cloud(cls, *args, **kwargs):
            return cls()

    class MockQdrantClient:
        def __init__(self, *args, **kwargs):
            pass

    class MockAsyncQdrantClient:
        def __init__(self, *args, **kwargs):
            pass

    monkeypatch.setattr("opensearchpy.OpenSearch", MockOpenSearchClient)
    monkeypatch.setattr(
        "weaviate.connect_to_weaviate_cloud",
        MockWeaviateClient.connect_to_weaviate_cloud,
    )
    monkeypatch.setattr("weaviate.Client", MockWeaviateClient)
    monkeypatch.setattr("qdrant_client.QdrantClient", MockQdrantClient)
    monkeypatch.setattr("qdrant_client.AsyncQdrantClient", MockAsyncQdrantClient)


"""
Global pytest fixtures for the diogeneschatbotembeddingserver.
"""

import pytest
from unittest.mock import AsyncMock, patch


@pytest.fixture(autouse=True)
def mock_firebase_token_verification():
    """
    Mock Firebase token verification for all tests.

    This fixture automatically applies to all tests and mocks the verify_firebase_token
    function to return a test user without making actual Firebase API calls.
    """
    # Define a mock user that will be returned for all token verifications
    mock_user = {
        "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
        "email": "<EMAIL>",
    }

    # Create an async mock that returns the mock user
    mock_verify = AsyncMock(return_value=mock_user)
    mock_verify_ws = AsyncMock(return_value=mock_user)
    mock_get_auth_header = AsyncMock(return_value="Bearer test-token")

    # Apply the patches
    with patch("app.config.verify_token.verify_firebase_token", mock_verify):
        with patch("app.config.verify_token.verify_firebase_token_ws", mock_verify_ws):
            with patch(
                "app.config.verify_token.get_authorization_header", mock_get_auth_header
            ):
                yield mock_user  # The test will have access to the mock_user
