# Testing with Authentication

This document explains how to properly test authenticated endpoints in the Diogenes Chatbot Embedding Server.

## Overview

Rather than modifying the application code to detect test environments (which adds complexity to production code), we use pytest fixtures to mock the authentication functions during testing.

## How to Use Mock Authentication in Tests

### Unit Tests

For unit tests, the global fixtures in `tests/conftest.py` and module-specific fixtures in `tests/unit/conftest.py` handle authentication mocking.

```python
import pytest
from unittest.mock import patch

# Test a function that requires Firebase authentication
@pytest.mark.asyncio
async def test_my_function_with_auth(mock_verify_firebase):
    # Your test here - auth is already mocked!
    result = await my_function_that_needs_auth()
    assert result == expected_result
```

### Integration Tests

For integration tests, we provide fixtures in `tests/integration_tests/test_auth_integration.py` that show how to mock authentication for API testing:

```python
def test_protected_endpoint(client_with_mocked_auth):
    # Call an endpoint that requires authentication
    response = client_with_mocked_auth.get("/protected-endpoint")
    assert response.status_code == 200
```

### Testing with Different User Identities

You can test with different user identities using parameterization:

```python
@pytest.mark.parametrize("user_id,email", [
    ("test-user-1", "<EMAIL>"),
    ("test-user-2", "<EMAIL>"),
])
def test_with_different_users(user_id, email):
    with patch("app.config.verify_token.verify_firebase_token") as mock_verify:
        mock_verify.return_value = {"user_id": user_id, "email": email}
        # Your test code here
```

## Benefits of This Approach

1. **Clean Separation**: Testing concerns are kept separate from application code
2. **No Test Detection**: Application code doesn't need to detect if it's running in a test environment
3. **Flexibility**: Tests can use different mock users without changing application code
4. **Better Control**: Tests have full control over authentication behavior
5. **No Environment Variables**: No need to set special environment variables for tests

## Examples

See the following files for examples:

-   `tests/unit/test_auth.py` - Unit test examples
-   `tests/integration_tests/test_auth_integration.py` - Integration test examples
