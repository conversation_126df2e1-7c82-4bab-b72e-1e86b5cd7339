import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
import tempfile
import os
from app.utils.util import (
    format_time,
    get_timestamp,
    upload_document_to_firebase,
    parse_markdown,
)


def test_format_time():
    """Test formatting timestamps."""
    now = datetime.now()
    timestamp = int(now.timestamp())

    # Test with seconds
    formatted = format_time(timestamp, format_type="seconds")
    assert isinstance(formatted, str)
    assert ":" in formatted  # Should be HH:MM:SS format

    # Test with date
    formatted_date = format_time(timestamp, format_type="date")
    assert isinstance(formatted_date, str)
    assert "-" in formatted_date  # Should have date separators


def test_get_timestamp():
    """Test getting current timestamp."""
    timestamp = get_timestamp()

    # Should be within 1 second of now
    now = int(datetime.now().timestamp())
    assert abs(timestamp - now) <= 1


@patch("app.utils.util.get_storage")
def test_upload_document_to_firebase(mock_get_storage):
    """Test uploading document to Firebase."""
    # Create a temporary file for testing
    with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as temp_file:
        temp_file.write(b"Test content")
        temp_path = temp_file.name

    try:
        # Setup mocks
        mock_storage = MagicMock()
        mock_bucket = MagicMock()
        mock_blob = MagicMock()
        mock_get_storage.return_value = mock_storage
        mock_storage.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        mock_blob.public_url = "https://test-url.com/file.txt"

        # Call the function
        result = upload_document_to_firebase(
            path=temp_path,
            bucket_name="test-bucket",
            destination_blob_name="uploads/file.txt",
        )

        # Verify the mocks were called correctly
        mock_get_storage.assert_called_once()
        mock_storage.bucket.assert_called_once_with("test-bucket")
        mock_bucket.blob.assert_called_once_with("uploads/file.txt")
        mock_blob.upload_from_filename.assert_called_once_with(temp_path)

        # Verify the result
        assert result == "https://test-url.com/file.txt"

    finally:
        # Clean up
        if os.path.exists(temp_path):
            os.unlink(temp_path)


def test_parse_markdown():
    """Test parsing markdown to HTML."""
    markdown = """# Heading

This is a paragraph with **bold** and *italic* text.

- List item 1
- List item 2

[Link](https://example.com)
"""

    result = parse_markdown(markdown)

    # Check for expected HTML elements
    assert "<h1>Heading</h1>" in result
    assert "<strong>bold</strong>" in result
    assert "<em>italic</em>" in result
    assert "<li>List item 1</li>" in result
    assert '<a href="https://example.com">Link</a>' in result
