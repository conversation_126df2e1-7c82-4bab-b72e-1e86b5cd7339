import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import time
from starlette.requests import Request
from starlette.responses import Response
from app.models.model import RequestLoggerMiddleware


@pytest.mark.asyncio
async def test_request_logger_middleware():
    """Test the RequestLoggerMiddleware logs request information correctly."""
    # Create mocks
    mock_request = MagicMock(spec=Request)
    mock_request.method = "GET"
    mock_request.url = MagicMock()
    mock_request.url.path = "/api/test"
    
    mock_response = MagicMock(spec=Response)
    mock_call_next = AsyncMock(return_value=mock_response)
    
    # Create middleware instance
    middleware = RequestLoggerMiddleware()
    
    # Mock time.time to return predictable values
    original_time = time.time
    time_values = [1000.0, 1002.5]  # Start time and end time (2.5 seconds difference)
    time_mock = MagicMock(side_effect=time_values)
    
    # Patch the logger and time.time
    with patch('app.models.model.logger') as mock_logger, \
         patch('app.models.model.time.time', time_mock):
        
        # Call the middleware
        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Verify the response is correct
        assert response == mock_response
        
        # Verify call_next was called with the request
        mock_call_next.assert_called_once_with(mock_request)
        
        # Verify the logger was called with correct args
        mock_logger.info.assert_any_call("Started GET request to /api/test")
        mock_logger.info.assert_any_call("Ended GET request to /api/test in 2.50 seconds")
        
        # Verify time.time was called twice
        assert time_mock.call_count == 2
