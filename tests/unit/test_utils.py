import pytest
from app.utils.util import parse_firebase_url, filter_string, get_encoded_id, find_last_final_answer
from app.utils.util_markdown import extract_outline


def test_parse_firebase_url_https():
    url = "https://firebasestorage.googleapis.com/v0/b/mybucket.appspot.com/o/path%2Fto%2Ffile.pdf?alt=media&token=abc"
    bucket, blob, name, ext = parse_firebase_url(url)
    assert bucket == "mybucket.appspot.com"
    assert blob == "path/to/file.pdf"
    assert name == "file.pdf"
    assert ext == ".pdf"


def test_parse_firebase_url_gs():
    url = "gs://mybucket/path/to/file.txt"
    bucket, blob, name, ext = parse_firebase_url(url)
    assert bucket == "mybucket"
    assert blob == "path/to/file.txt"
    assert name == "file.txt"
    assert ext == ".txt"


def test_parse_firebase_url_invalid():
    assert parse_firebase_url("https://example.com/file.txt") == (None, None, None, None)


def test_filter_string():
    text = "intro <END_OF_TOKEN> result <END_OF_TURN> ignored"
    assert filter_string(text) == "result"


def test_filter_string_no_markers():
    text = "plain text"
    assert filter_string(text) == "plain text"


def test_get_encoded_id():
    assert get_encoded_id("abc123") == "mfrggmjsgm"


def test_find_last_final_answer():
    resp = "Some text. Final Answer: 42"
    assert find_last_final_answer(resp) == "42"


def test_extract_outline():
    md = "# Title\n## Section1\n## Section2"
    outline = extract_outline(md)
    assert outline == {1: ["Title"], 2: ["Section1", "Section2"]}
