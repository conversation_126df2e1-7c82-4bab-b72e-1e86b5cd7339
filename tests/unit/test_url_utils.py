import pytest
import tempfile
import os
from unittest.mock import patch, MagicMock
from app.utils.util import (
    get_main_content,
    save_main_content_to_temp_file,
    filter_string,
    get_encoded_id,
    find_last_final_answer
)


@patch('app.utils.util.requests.get')
def test_get_main_content_with_article(mock_get):
    """Test extracting main content when an article tag is present."""
    # Create a mock response
    mock_response = MagicMock()
    mock_response.text = """
    <html>
        <body>
            <article>
                <h1>Test Article</h1>
                <p>This is the main content</p>
                <script>alert('hi');</script>
                <style>.hidden { display: none; }</style>
            </article>
        </body>
    </html>
    """
    mock_get.return_value = mock_response
    
    result = get_main_content("https://example.com")
    
    # Assert script and style tags were removed
    assert "<script>" not in result
    assert "<style>" not in result
    assert "Test Article" in result
    assert "This is the main content" in result


@patch('app.utils.util.requests.get')
def test_get_main_content_with_main(mock_get):
    """Test extracting main content when a main tag is present."""
    mock_response = MagicMock()
    mock_response.text = """
    <html>
        <body>
            <main>
                <h1>Test Main</h1>
                <p>This is the main content</p>
            </main>
        </body>
    </html>
    """
    mock_get.return_value = mock_response
    
    result = get_main_content("https://example.com")
    
    assert "Test Main" in result
    assert "This is the main content" in result


@patch('app.utils.util.requests.get')
def test_get_main_content_not_found(mock_get):
    """Test behavior when main content isn't found."""
    mock_response = MagicMock()
    mock_response.text = """
    <html>
        <body>
            <div>Some content</div>
        </body>
    </html>
    """
    mock_get.return_value = mock_response
    
    result = get_main_content("https://example.com")
    
    assert result == "Main content not found"


@patch('app.utils.util.get_main_content')
def test_save_main_content_to_temp_file(mock_get_main_content):
    """Test saving main content to a temporary file."""
    mock_get_main_content.return_value = "<h1>Test Content</h1>"
    
    temp_file_path = save_main_content_to_temp_file("https://example.com")
    
    try:
        # Check that a file was created
        assert os.path.exists(temp_file_path)
        
        # Read the file and check its contents
        with open(temp_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            assert content == "<h1>Test Content</h1>"
    finally:
        # Clean up
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)


def test_filter_string_with_markers():
    """Test filtering text between markers."""
    assert filter_string("pre <END_OF_TOKEN> middle <END_OF_TURN> post") == "middle"
    assert filter_string("<END_OF_TOKEN>middle<END_OF_TURN>") == "middle"
    assert filter_string("text <END_OF_TOKEN> <END_OF_TURN>") == ""


def test_filter_string_with_partial_markers():
    """Test filtering with only one marker."""
    assert filter_string("pre <END_OF_TOKEN> middle") == "middle"
    assert filter_string("middle <END_OF_TURN> post") == "middle"


def test_get_encoded_id_consistency():
    """Test that encoded IDs are consistent."""
    id1 = get_encoded_id("test123")
    id2 = get_encoded_id("test123")
    assert id1 == id2
    
    # Different inputs should produce different encoded IDs
    assert get_encoded_id("test123") != get_encoded_id("test124")


def test_find_last_final_answer_variations():
    """Test finding the final answer with various formats."""
    assert find_last_final_answer("Text\nFinal Answer: Result") == "Result"
    assert find_last_final_answer("Text\nFinal Answer:Result") == "Result"
    assert find_last_final_answer("First Final Answer: Wrong\nFinal Answer: Right") == "Right"
    assert find_last_final_answer("No final answer here") is None
