import pytest
from app.utils.util_markdown import extract_outline


def test_extract_outline_basic():
    """Test basic outline extraction from markdown headings."""
    md = "# Title\n## Section1\n## Section2"
    outline = extract_outline(md)
    assert outline == {1: ["Title"], 2: ["Section1", "Section2"]}


def test_extract_outline_with_nested_headings():
    """Test outline extraction with mixed heading levels."""
    md = """# Main Title
## Section 1
### Subsection 1.1
### Subsection 1.2
## Section 2
#### Deep Nested Section
"""
    outline = extract_outline(md)
    assert outline == {
        1: ["Main Title"],
        2: ["Section 1", "Section 2"],
        3: ["Subsection 1.1", "Subsection 1.2"],
        4: ["Deep Nested Section"]
    }


def test_extract_outline_with_empty_input():
    """Test outline extraction with empty input."""
    assert extract_outline("") is None
    assert extract_outline(None) is None


def test_extract_outline_no_headings():
    """Test outline extraction with text that has no headings."""
    text = "This is just regular text\nwith no headings at all."
    assert extract_outline(text) is None


def test_extract_outline_with_inline_formatting():
    """Test outline extraction with formatted text in headings."""
    md = """# Main *Title* with **Bold**
## Section with `code`
### Subsection with [link](http://example.com)
"""
    outline = extract_outline(md)
    assert outline == {
        1: ["Main *Title* with **Bold**"],
        2: ["Section with `code`"],
        3: ["Subsection with [link](http://example.com)"]
    }


def test_extract_outline_with_spaces():
    """Test outline extraction with irregular spacing in headings."""
    md = """#Title without space
##  Section with extra spaces  
###    Subsection with many spaces    
"""
    outline = extract_outline(md)
    assert outline == {
        1: ["Title without space"],
        2: ["Section with extra spaces"],
        3: ["Subsection with many spaces"]
    }
