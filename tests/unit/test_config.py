import pytest
from unittest.mock import patch, MagicMock
import os
from app.config.config import (
    get_chatgpt_basic,
    setup_envs,
    DEFAULT_MAX_TOKENS
)


def test_get_chatgpt_basic_default_params():
    """Test get_chatgpt_basic with default parameters."""
    with patch('app.config.config.ChatOpenAI') as mock_chat_openai:
        # Call the function
        result = get_chatgpt_basic()
        
        # Verify the ChatOpenAI was instantiated with expected defaults
        mock_chat_openai.assert_called_once_with(
            model=mock_chat_openai.return_value.model,  # Using the mock's return value since we can't access default_model directly
            streaming=True,
            verbose=True,
            temperature=0,
            max_tokens=DEFAULT_MAX_TOKENS,
            stream_usage=True
        )
        
        # Verify the result
        assert result == mock_chat_openai.return_value


def test_get_chatgpt_basic_custom_params():
    """Test get_chatgpt_basic with custom parameters."""
    with patch('app.config.config.ChatOpenAI') as mock_chat_openai:
        # Call the function with custom parameters
        result = get_chatgpt_basic(
            streaming=False,
            verbose=False,
            temperature=0.7,
            max_tokens=1000
        )
        
        # Verify the ChatOpenAI was instantiated with custom parameters
        mock_chat_openai.assert_called_once_with(
            model=mock_chat_openai.return_value.model,
            streaming=False,
            verbose=False,
            temperature=0.7,
            max_tokens=1000,
            stream_usage=True
        )
        
        # Verify the result
        assert result == mock_chat_openai.return_value


def test_setup_envs():
    """Test setup_envs correctly initializes environment variables."""
    # Save original environment
    original_env = os.environ.copy()
    
    try:
        # Clear environment variables we're testing
        for key in [
            "OPENAI_API_KEY", "ANTHROPIC_API_KEY", "OPENAI_APIKEY",
            "ALPHAVANTAGE_API_KEY", "AWS_OPENSEARCH_ADMIN_PASSWORD",
            "KMP_DUPLICATE_LIB_OK", "FIREBASE_SERVICE_ACCOUNT_KEY",
            "GOOGLE_CSE_ID", "GOOGLE_API_KEY", "GPLACES_API_KEY",
            "REPLICATE_API_TOKEN", "LANGCHAIN_TRACING_V2"
        ]:
            if key in os.environ:
                del os.environ[key]
        
        # Set one test value to verify it's preserved
        os.environ["OPENAI_API_KEY"] = "test-key"
        
        # Call the function
        setup_envs()
        
        # Verify environment variables are set
        assert os.environ["OPENAI_API_KEY"] == "test-key"  # Preserved
        assert os.environ["OPENAI_APIKEY"] == "test-key"  # Copied from OPENAI_API_KEY
        assert os.environ["ANTHROPIC_API_KEY"] == ""  # Default empty
        assert os.environ["KMP_DUPLICATE_LIB_OK"] == "TRUE"  # Hard-coded value
        assert os.environ["LANGCHAIN_TRACING_V2"] == "true"  # Hard-coded value
        
    finally:
        # Restore original environment
        os.environ.clear()
        os.environ.update(original_env)
