import pytest
from unittest.mock import <PERSON><PERSON>ock
from fastapi import HTTPException
from starlette.responses import JSONResponse
from app.api.document import (
    create_document,
    update_document,
    delete_document,
    get_documents,
)


@pytest.mark.asyncio
async def test_create_document_success():
    """Test successful document creation."""
    # Create mocks
    mock_diogenes_search = MagicMock()
    mock_diogenes_search.create_document = (
        MagicMock()
    )  # Not async in the implementation
    mock_token = {"user_id": "test-user"}

    # Call the function
    document_data = {"title": "Test Document", "content": "Test content"}
    result = await create_document(
        document_data=document_data,
        original_user_id="test-user",
        diogenes_search=mock_diogenes_search,
        decoded_token=mock_token,
    )

    # Verify the search function was called correctly
    mock_diogenes_search.create_document.assert_called_once_with(
        document_data, "test-user"
    )

    # Verify result
    assert isinstance(result, JSONResponse)
    assert result.body == b'{"success":true}'


@pytest.mark.asyncio
async def test_create_document_failure():
    """Test document creation with an error."""
    # Create mocks
    mock_diogenes_search = MagicMock()
    mock_diogenes_search.create_document = MagicMock(
        side_effect=Exception("Test error")
    )
    mock_token = {"user_id": "test-user"}

    # Call the function and expect an exception
    document_data = {"title": "Test Document", "content": "Test content"}
    with pytest.raises(HTTPException) as excinfo:
        await create_document(
            document_data=document_data,
            original_user_id="test-user",
            diogenes_search=mock_diogenes_search,
            decoded_token=mock_token,
        )

    # Verify the exception details
    assert excinfo.value.status_code == 500
    assert excinfo.value.detail == "Error creating document"


@pytest.mark.asyncio
async def test_update_document_success():
    """Test successful document update."""
    # Create mocks
    mock_diogenes_search = MagicMock()
    mock_diogenes_search.update_document = MagicMock()
    mock_token = {"user_id": "test-user"}

    # Call the function
    document_id = "doc123"
    updated_data = {"title": "Updated Document", "content": "Updated content"}
    result = await update_document(
        document_id=document_id,
        updated_data=updated_data,
        original_user_id="test-user",
        diogenes_search=mock_diogenes_search,
        decoded_token=mock_token,
    )

    # Verify the function was called correctly
    mock_diogenes_search.update_document.assert_called_once_with(
        document_id, updated_data, "test-user"
    )

    # Verify result
    assert result == {"success": True}


@pytest.mark.asyncio
async def test_delete_document_success():
    """Test successful document deletion."""
    # Create mocks
    mock_diogenes_search = MagicMock()
    mock_diogenes_search.delete_documents = MagicMock()
    mock_token = {"user_id": "test-user"}

    # Call the function
    document_id = "doc123"
    result = await delete_document(
        document_id=document_id,
        original_user_id="test-user",
        diogenes_search=mock_diogenes_search,
        decoded_token=mock_token,
    )

    # Verify the function was called correctly (note: implementation calls delete_documents with a list)
    mock_diogenes_search.delete_documents.assert_called_once_with(
        [document_id], "test-user"
    )

    # Verify result
    assert result == {"success": True}


@pytest.mark.asyncio
async def test_get_documents_success():
    """Test successfully getting documents."""
    # Create mocks
    mock_diogenes_search = MagicMock()
    mock_token = {"user_id": "test-user"}

    # Call the function - current implementation returns empty list as placeholder
    result = await get_documents(
        original_user_id="test-user",
        diogenes_search=mock_diogenes_search,
        decoded_token=mock_token,
    )

    # Verify result is empty list (current implementation)
    # TODO: Update this test when get_documents is fully implemented
    assert result == []
