import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import json
from fastapi import WebSocket
from app.api.aask import aask_websocket_endpoint


@pytest.mark.asyncio
async def test_aask_websocket_endpoint():
    """Test the aask websocket endpoint."""
    # Create mocks
    mock_websocket = MagicMock(spec=WebSocket)
    mock_websocket.accept = AsyncMock()
    mock_websocket.receive_text = AsyncMock(return_value=json.dumps({
        "firebase_urls": ["gs://bucket/document.pdf"],
        "query": "What is in this document?",
        "bot_id": "test-bot",
        "user_id": "test-user",
        "conversation_id": "test-conversation"
    }))
    mock_websocket.send_text = AsyncMock()
    mock_websocket.close = AsyncMock()
    
    mock_db = MagicMock()
    mock_diogenes_search = MagicMock()
    
    # Mock verify_firebase_token_ws
    with patch('app.api.aask.verify_firebase_token_ws') as mock_verify_token, \
         patch('app.api.aask.uuid.uuid4') as mock_uuid, \
         patch('app.api.aask.util.get_conversation_history') as mock_get_conversation, \
         patch('app.api.aask.make_agent') as mock_make_agent, \
         patch('app.api.aask.get_bot_property') as mock_get_bot_property, \
         patch('app.api.aask.prepare_retriever') as mock_prepare_retriever, \
         patch('app.api.aask.Chroma') as mock_chroma:
        
        # Setup mock returns
        mock_verify_token.return_value = {"user_id": "test-user"}
        mock_uuid.return_value = "test-session-id"
        mock_agent = MagicMock()
        mock_agent.invoke = AsyncMock(return_value={"output": "Test response"})
        mock_make_agent.return_value = mock_agent
        mock_get_bot_property.return_value = {"name": "Test Bot"}
        
        # Simulate exception during processing to test flow
        mock_websocket.receive_text.side_effect = [
            json.dumps({
                "firebase_urls": ["gs://bucket/document.pdf"],
                "query": "What is in this document?",
                "bot_id": "test-bot",
                "user_id": "test-user",
                "conversation_id": "test-conversation"
            }),
            WebSocketDisconnect()  # Simulate disconnect on second receive
        ]
        
        # Call the endpoint
        try:
            await aask_websocket_endpoint(
                websocket=mock_websocket,
                db=mock_db,
                diogenes_search=mock_diogenes_search,
                token="test-token"
            )
        except WebSocketDisconnect:
            pass  # Expected exception
        
        # Verify websocket methods were called
        mock_websocket.accept.assert_called_once()
        mock_verify_token.assert_called_once_with("test-token")
        
        # Verify at least one message was sent (can't check exact content due to mocks)
        assert mock_websocket.send_text.call_count >= 1
