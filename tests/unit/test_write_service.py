import pytest
from unittest.mock import patch, AsyncMock, MagicMock
import asyncio
from langchain_core.messages import AIMessage, HumanMessage
from app.services.write_service import (
    initialize_research,
    format_conversation,
    refine_outline
)


@pytest.fixture
def research_state():
    """Fixture for a basic research state."""
    return {
        "topic": "Artificial Intelligence Ethics",
        "outline": None,
        "editors": None,
        "interview_results": []
    }


@pytest.fixture
def interview_state():
    """Fixture for a basic interview state."""
    return {
        "editor": <PERSON><PERSON><PERSON>(name="Dr. Ethics Expert"),
        "messages": [
            HumanMessage(content="What are the key ethical concerns in AI?"),
            AIMessage(content="Bias, privacy, and autonomy are major concerns.")
        ]
    }


@patch('app.services.write_service.generate_outline_direct')
@patch('app.services.write_service.survey_subjects')
@pytest.mark.asyncio
async def test_initialize_research(mock_survey, mock_outline, research_state):
    """Test initializing research with a given topic."""
    # Setup mocks
    mock_outline.ainvoke = AsyncMock(return_value=MagicMock(as_str="I. Introduction\nII. Main Points"))
    mock_survey.ainvoke = AsyncMock(return_value=MagicMock(editors=["Editor1", "Editor2"]))
    
    # Call the function
    result = await initialize_research(research_state)
    
    # Verify results
    assert result["topic"] == "Artificial Intelligence Ethics"
    assert result["outline"].as_str == "I. Introduction\nII. Main Points"
    assert result["editors"] == ["Editor1", "Editor2"]
    
    # Verify the mocks were called correctly
    mock_outline.ainvoke.assert_called_once_with({"topic": "Artificial Intelligence Ethics"})
    mock_survey.ainvoke.assert_called_once_with("Artificial Intelligence Ethics")


def test_format_conversation(interview_state):
    """Test formatting a conversation for review."""
    result = format_conversation(interview_state)
    expected = 'Conversation with Dr. Ethics Expert\n\nHuman: What are the key ethical concerns in AI?\nAI: Bias, privacy, and autonomy are major concerns.'
    assert result == expected


@patch('app.services.write_service.refine_outline_chain')
@pytest.mark.asyncio
async def test_refine_outline(mock_refine_chain, research_state, interview_state):
    """Test refining an outline based on interview results."""
    # Setup state
    research_state["outline"] = MagicMock(as_str="I. Introduction\nII. Main Points")
    research_state["interview_results"] = [interview_state]
    
    # Setup mock
    mock_refine_chain.ainvoke = AsyncMock(return_value=MagicMock(as_str="I. Introduction\nII. Ethics Concerns\nIII. Conclusion"))
    
    # Call the function
    result = await refine_outline(research_state)
    
    # Verify the result
    assert "outline" in result
    assert isinstance(result["outline"], MagicMock)
    
    # Verify the mock was called
    mock_refine_chain.ainvoke.assert_called_once()
