import pytest
from unittest.mock import patch, MagicMock
import json
import os
from app.core.db_config import (
    initialize_firebase_app,
    get_firestore,
    get_db,
    get_storage,
)


@pytest.fixture
def mock_service_account():
    """Fixture for a mock service account."""
    return {
        "type": "service_account",
        "project_id": "test-project",
        "private_key_id": "test-key-id",
        "private_key": "-----BEGIN PRIVATE KEY-----\nMIIE...test...key\n-----END PRIVATE KEY-----\n",
        "client_email": "<EMAIL>",
        "client_id": "*********",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/test%40test-project.iam.gserviceaccount.com",
    }


@pytest.fixture
def setup_env(mock_service_account, monkeypatch):
    """Setup environment variables for tests."""
    monkeypatch.setenv("FIREBASE_SERVICE_ACCOUNT_KEY", json.dumps(mock_service_account))


@patch("app.core.db_config.firebase_admin_credentials.Certificate")
@patch("app.core.db_config.firebase_admin.get_app")
@patch("app.core.db_config.firebase_admin.initialize_app")
def test_initialize_firebase_app_first_time(
    mock_init_app, mock_get_app, mock_cert, setup_env
):
    """Test initializing Firebase app for the first time."""
    # Make get_app raise ValueError to simulate first initialization
    mock_get_app.side_effect = ValueError("No app exists")

    # Call the function
    initialize_firebase_app()

    # Verify certificate was created with the correct credentials
    mock_cert.assert_called_once()

    # Verify initialize_app was called
    mock_init_app.assert_called_once()


@patch("app.core.db_config.firebase_admin_credentials.Certificate")
@patch("app.core.db_config.firebase_admin.get_app")
@patch("app.core.db_config.firebase_admin.initialize_app")
def test_initialize_firebase_app_already_initialized(
    mock_init_app, mock_get_app, mock_cert, setup_env
):
    """Test initializing Firebase app when it's already initialized."""
    # Make get_app return successfully to simulate existing app
    mock_get_app.return_value = MagicMock()

    # Call the function
    initialize_firebase_app()

    # Verify certificate was created
    mock_cert.assert_called_once()

    # Verify initialize_app was NOT called again
    mock_init_app.assert_not_called()


@patch("app.core.db_config.firestore.AsyncClient")
def test_get_firestore(mock_client, setup_env):
    """Test getting a Firestore client."""
    # Call the function
    result = get_firestore()

    # Verify AsyncClient was created
    mock_client.assert_called_once()

    # Verify the result
    assert result == mock_client.return_value


@patch("app.core.db_config.get_firestore")
def test_get_db(mock_get_firestore, setup_env):
    """Test the get_db dependency function."""
    # Setup mock
    mock_db = MagicMock()
    mock_get_firestore.return_value = mock_db

    # Call the function
    db = get_db()

    # Verify the result
    assert db == mock_db


@patch("app.core.db_config.storage.Client")
def test_get_storage(mock_client, setup_env):
    """Test getting a Storage client."""
    # Call the function
    result = get_storage()

    # Verify Client.from_service_account_info was called
    mock_client.from_service_account_info.assert_called_once()

    # Verify the result
    assert result == mock_client.from_service_account_info.return_value
