"""
Module-specific pytest fixtures for unit tests.
"""

import pytest
from unittest.mock import AsyncMock, patch


@pytest.fixture
def mock_user():
    """Return a mock user for testing."""
    return {"user_id": "8KEKoa6d90dr75AywriRKX3nLIR2", "email": "<EMAIL>"}


@pytest.fixture
def mock_token():
    """Return a mock token for testing."""
    return "test-token"


@pytest.fixture
def mock_auth_header():
    """Mock the auth header function specifically for tests that need it."""
    with patch("app.config.verify_token.get_authorization_header") as mock:
        mock.return_value = "Bearer test-token"
        yield mock


@pytest.fixture
async def mock_verify_firebase():
    """
    Create an isolated mock for the firebase verification.
    This is useful when tests need to control the return value.
    """

    async def _verify(*args, **kwargs):
        return {
            "user_id": "8KEKoa6d90dr75AywriRKX3nLIR2",
            "email": "<EMAIL>",
        }

    with patch("app.config.verify_token.verify_firebase_token", _verify):
        yield _verify
