import pytest
from datetime import datetime
from app.models.bot_chat_message import BotChatMessage


def test_bot_chat_message_init():
    """Test BotChatMessage initialization."""
    timestamp = datetime.utcnow()
    message = BotChatMessage(
        text="Hello, world!",
        sender_id="user123",
        timestamp=timestamp,
        is_bot=False
    )
    
    assert message.text == "Hello, world!"
    assert message.sender_id == "user123"
    assert message.timestamp == timestamp
    assert message.is_bot is False


def test_bot_chat_message_to_dict():
    """Test converting BotChatMessage to dictionary."""
    timestamp = datetime.utcnow()
    message = BotChatMessage(
        text="Test message",
        sender_id="bot456",
        timestamp=timestamp,
        is_bot=True
    )
    
    message_dict = message.to_dict()
    
    assert message_dict["text"] == "Test message"
    assert message_dict["senderId"] == "bot456"
    assert message_dict["timestamp"] == timestamp
    assert message_dict["isBot"] is True


def test_create_bot_response():
    """Test creating a bot response from response data."""
    response_data = {
        "answer": "I can help with that."
    }
    bot_id = "chatbot_assistant"
    
    message = BotChatMessage.create_bot_response(response_data, bot_id)
    
    assert message.text == "I can help with that."
    assert message.sender_id == "chatbot_assistant"
    assert isinstance(message.timestamp, datetime)
    assert message.is_bot is True
    
    # Test the dictionary conversion
    message_dict = message.to_dict()
    assert message_dict["text"] == "I can help with that."
    assert message_dict["senderId"] == "chatbot_assistant"
    assert message_dict["isBot"] is True
