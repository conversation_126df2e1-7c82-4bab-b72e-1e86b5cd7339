"""
Example test using the mock authentication fixtures.
"""

import pytest
from fastapi import Depends
from fastapi.testclient import Test<PERSON><PERSON>
from unittest.mock import patch

# Import the functions we want to test
from app.config.verify_token import (
    get_authorization_header,
    verify_firebase_token,
    verify_firebase_token_ws,
)


@pytest.mark.asyncio
async def test_verify_firebase_token(mock_verify_firebase, mock_auth_header):
    """Test that the verify_firebase_token function works correctly when mocked."""
    # The mock will automatically be used due to our fixture
    result = await verify_firebase_token("some_token_that_will_be_ignored")

    # Check that the result contains the expected mock user data
    assert result["user_id"] == "8KEKoa6d90dr75AywriRKX3nLIR2"
    assert result["email"] == "<EMAIL>"


def test_api_with_auth_dependency(mock_verify_firebase):
    """
    Test an API endpoint that requires authentication.

    This demonstrates how to test API endpoints that use the verify_firebase_token
    dependency without needing real Firebase credentials.
    """
    # Example of how you would set up a FastAPI test client
    # with patch("app.main.verify_firebase_token", mock_verify_firebase):
    #     client = TestClient(app)
    #     response = client.get("/protected-endpoint")
    #     assert response.status_code == 200

    # For demonstration purposes only
    assert mock_verify_firebase is not None
