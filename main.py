"""
Main application module for the FastAPI server.

This module sets up the FastAPI application, configures middleware,
and includes various API routers.
"""

# Setup environment variables as soon as possible before other imports since they may need it
from app.config.config import setup_envs

setup_envs()
from typing import List
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from gevent import monkey
from gevent.pywsgi import WSGIServer


from app.core.db_config import get_db, get_storage
from app.core.tracing_setup import logger
from app.core.vector_db_config import get_vector_db_crud_static
from app.models.model import RequestLoggerMiddleware
import app.config.config_priority
from app.api import (
    aask,
    achat,
    chatai,
    document,
    payment,
    writing,
    chatroom_ws,
    crew,
    autogen,
    # chat_autoai_hitl,
    gpt_researcher,
    openperplex,
    maps,
    chat_langgraph,
    pdf2audio,
    story,
    # yolo,
    # pdf2markdown,
)


# Configure langchain debugging
import langchain

langchain.debug = True


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: The configured FastAPI application.
    """
    app = FastAPI()

    # Configure CORS
    origins: List[str] = [
        "https://diogenesaichatbot.web.app",
        "https://diogenesaichatbot--staging-w7lsc1fq.web.app/",
        "diogenesaichatbot.firebaseapp.com",
        "http://localhost:49430",
        "http://localhost:63719",
        "https://disceptatio.com",
    ]

    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add custom middleware for logging
    app.add_middleware(RequestLoggerMiddleware)

    # Log application start
    logger.info("Starting application")

    # Include API routers
    app.include_router(
        aask.router, dependencies=[Depends(get_db), Depends(get_vector_db_crud_static)]
    )
    app.include_router(
        achat.router, dependencies=[Depends(get_db), Depends(get_vector_db_crud_static)]
    )
    app.include_router(chatai.router, dependencies=[Depends(get_vector_db_crud_static)])
    app.include_router(document.router)
    app.include_router(
        writing.router, dependencies=[Depends(get_db), Depends(get_storage)]
    )
    app.include_router(crew.router)
    app.include_router(autogen.router)
    app.include_router(chatroom_ws.router)
    # app.include_router(chat_autoai_hitl.router)
    app.include_router(gpt_researcher.router)
    app.include_router(openperplex.router)
    app.include_router(maps.router)
    app.include_router(chat_langgraph.router)
    app.include_router(pdf2audio.router)
    app.include_router(story.router)
    app.include_router(payment.router)
    # app.include_router(yolo.router)
    # app.include_router(pdf2markdown.router)

    return app


app = create_app()


@app.get("/")
@app.post("/")
def index() -> str:
    """
    Root endpoint for the API.

    Returns:
        str: A greeting message.
    """
    return "Hello, world!"


if __name__ == "__main__":
    # Patch the standard library to be compatible with gevent
    monkey.patch_all()

    # Create and start the gevent WSGI server
    server = WSGIServer(("0.0.0.0", 8080), app)
    logger.info("Starting server on http://0.0.0.0:8080")
    server.serve_forever()
