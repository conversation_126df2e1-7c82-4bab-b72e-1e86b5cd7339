[supervisord]
nodaemon=true

[program:redis]
command=redis-server
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr
stdout_logfile_maxbytes=0  ; disable log file rotation
stderr_logfile_maxbytes=0

[program:celery_worker]
command=celery -A app.queue.celery_app worker --loglevel=info  -E --pool=prefork --concurrency=4 --statedb=./data/celery_worker_state.db
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/celery_worker.err.log
stdout_logfile=/var/log/celery_worker.out.log

[program:celery_beat]
command=celery -A app.queue.celery_app beat --loglevel=info 
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/celery_beat.err.log
stdout_logfile=/var/log/celery_beat.out.log

[program:redis]
command=redis-server --bind 0.0.0.0
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/redis.err.log
stdout_logfile=/var/log/redis.out.log

# [program:firecrawl_api]
# command=pnpm run start:production
# directory=/app/firecrawl/apps/api
# autostart=true
# autorestart=true
# stderr_logfile=/var/log/firecrawl_api.err.log
# stdout_logfile=/var/log/firecrawl_api.out.log
# environment=PORT="3002"

# [program:firecrawl_worker]
# command=pnpm run workers
# directory=/app/firecrawl/apps/api
# autostart=true
# autorestart=true
# stderr_logfile=/var/log/firecrawl_worker.err.log
# stdout_logfile=/var/log/firecrawl_worker.out.log

# [program:playwright_service]
# command=pnpm run start 
# directory=/app/firecrawl/apps/playwright-service
# autostart=true
# autorestart=true
# stderr_logfile=/var/log/playwright_service.err.log
# stdout_logfile=/var/log/playwright_service.out.log
# environment=PORT="3000"


[program:fastapi]
command=uvicorn main:app --host 0.0.0.0 --port 8080 --workers=4
directory=/app
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr
stdout_logfile_maxbytes=0  ; disable log file rotation
stderr_logfile_maxbytes=0
