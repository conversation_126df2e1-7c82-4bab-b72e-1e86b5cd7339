# uv pip install -r requirements.txt --prerelease=allow
# uv pip install --no-cache-dir   torch==2.3.1  torchvision==0.18.1 -f https://download.pytorch.org/whl/torch_stable.html
langchain-community==0.2.17
# langchain_experimental
# langchain_fireworks
langchain==0.2.16
langchainhub==0.1.21
langchain-core==0.2.40
langchain-openai==0.1.25
langchain-groq==0.1.10
langchain-huggingface==0.0.3
langchain-google-community==1.0.8
langchain-chroma==0.1.4
openai==1.46.1
torch==2.3.1
torchvision==0.18.1
torchaudio==2.3.1
knowledge-storm==1.0.0
    # langchain-openai 0.1.22 depends on tiktoken<1 and >=0.7
    # goldenverba 2.0.0 depends on tiktoken==0.6.0
goldenverba #==2.1.2
#     langchain-community 0.2.15 depends on numpy<2 and >=1; python_version < "3.12"
#     langchain 0.2.15 depends on numpy<2 and >=1; python_version < "3.12"
#     langchain-chroma 0.1.3 depends on numpy<2 and >=1; python_version < "3.12"
#     semantic-kernel 1.8.2 depends on numpy~=1.25.0; python_version < "3.12"
#     faiss-cpu 1.8.0 depends on numpy
#     langchain-weaviate 0.0.2 depends on numpy<2.0.0 and >=1.26.2
semantic-kernel #==1.26.1
langgraph==0.2.31
langgraph-checkpoint==1.0.14
langsmith==0.1.123
duckduckgo-search==5.3.1b1
# tavily-python
faiss-cpu==1.8.0
beautifulsoup4==4.12.3
google-cloud==0.34.0
#Flask
#  The user requested fastapi==0.112.2
    # langchain-chroma 0.1.3 depends on fastapi<1 and >=0.95.2
    # goldenverba 2.0.0 depends on fastapi==0.111.1
fastapi #==0.115.12
python-dotenv==1.0.1
google-cloud-storage==2.18.1
google-search-results==2.4.1
tiktoken==0.7.0
firebase-admin==6.5.0
#gunicorn
uvicorn #==0.34.0
sse_starlette==1.8.2
debugpy==1.8.5
#flask-cors
opensearch-py==2.7.1
certifi==2024.8.30
# gptcache
pypdf==4.3.1
requests==2.32.3
html2text==2024.2.26
asyncio==3.4.3
fake_useragent
pexpect
# deeplake
pytest
pytest-cov
pytest-mock
# flask-socketio
scrapy==2.11.2
gevent
#langchain-visualizer
#promptwatch
google-api-python-client==2.142.0
#flask-async
libmagic==1.0
#python-magic-bin
python-magic==0.4.27
pytesseract==0.3.13
transformers==4.40.0
# sentence-transformers -> requires torch -> nvidia-cuda
sentence-transformers==3.1.1
# this depends on nvidia
# huggingface_hub
wikipedia==1.4.0
playwright==1.46.0
lxml
greenlet
nest_asyncio==1.6.0
bs4
# lark
weaviate-client==4.8.1
langchain-weaviate==0.0.3
# pymilvus==2.4.6
# langchain-milvus==0.1.4
redis==5.1.0b7
redisvl==0.3.3
chromadb>=0.4.22
arxiv==2.1.3
pymupdf==1.24.10
yfinance==0.2.43
opencv-python==********
# opencv-python-headless==********
scikit-image==0.24.0
docarray==0.40.0
# gradio_tools
spacy==3.7.6
alpha_vantage==3.0.0
pandas==2.2.2
# tensorflow==2.17.0
# pyspark==3.5.3
pandoc==2.4
# apify-client
bibtexparser
httpx==0.27.0
# feast
# featureform
# langchain-experimental
matplotlib==3.9.1
# vowpal_wabbit_next
google-cloud-aiplatform==1.67.1
google-cloud-speech==2.27.0
googlemaps==4.10.0
# pysqlite3==0.5.4 # conda install conda-forge::libsqlite --force-reinstall -y
numexpr
scikit-learn #==1.6.1
scipy==1.14.1
nltk==3.9.1
# jieba
GitPython
# TTS
# openai-whisper
# pyaudio
# SpeechRecognition
anthropic==0.34.2
langserve[all]==0.2.3
# selenium
# libmagic
# python-magic-bin
promptflow[azure]==1.17.2
youtube_search==2.1.2
IPython
# git+ssh://**************/dsx1986/langflow.git@main
# -e ../langflow
# git+ssh://**************/dsx1986/PraisonAI.git@main#egg=praisonai
# -e ../praisonai
# git+ssh://**************/dsx1986/PraisonAI-Tools.git@main#egg=praisonai-tools
# -e ../praisonai-tools
pyautogen==0.4.0
# pyautogen[retrievechat-qdrant]==0.3.0
# autogen-agentchat[autobuild]==0.4.0.dev6
# autogen-ext[openai]==0.4.0.dev6
# autogen-agentchat==0.2.38 #==0.4.0.dev6
# autogen-ext[openai]==0.0.1 #==0.4.0.dev6
# flaml[automl]
# git+ssh://**************/dsx1986/autogen.git@main#egg=autogen
# -e ../autogen
#     The user requested crewai==0.51.1
#     langflow 1.0.17 depends on crewai<0.37.0 and >=0.36.0
# langchain-huggingface 0.0.3 depends on huggingface-hub>=0.23.0
#     langflow 1.0.17 depends on huggingface-hub<0.23.0 and >=0.22.0
# langflow==1.0.19.post2
# autogenstudio==0.1.5
# pezzo
# mem0ai
haystack-ai==2.5.1
qdrant-haystack==6.0.0
# remove weaviate-haystack for now since ocstring-parser<0.12 and >=0.11 -> pydoc-markdown==4.8.2 (from haystack-pydoc-tools->weaviate-haystack==2.2.0) this has conflict
# weaviate-haystack==2.2.0
# milvus-haystack==0.0.10
# newspaper3k-haystack==0.1.1
pdfminer.six==20231228
celery==5.4.0
qdrant-client==1.11.2
langchain-qdrant==0.1.4
crewai==0.36.1
crewai-tools==0.12.1
#langchain-chatchat
scrapegraphai==1.19.0
# firecrawl-py==1.2.3
# phidata
# together
# jina==3.27.2
# agent-protocol-client
# agent-protocol
# agentUniverse==0.0.12
pydantic==2.7.4
# praisonai==0.0.64
# metagpt
# llmware==0.3.5
opentelemetry-api==1.27.0
opentelemetry-sdk==1.27.0
opentelemetry-instrumentation-fastapi==0.48b0
opentelemetry-instrumentation-celery==0.48b0
opentelemetry-instrumentation-redis==0.48b0
opentelemetry-instrumentation-logging==0.48b0
opentelemetry-instrumentation-langchain==0.33.7 #==0.28.2
opentelemetry-exporter-otlp==1.27.0
opentelemetry-exporter-otlp-proto-http==1.27.0
opentelemetry-exporter-otlp-proto-grpc==1.27.0
# opentelemetry-exporter-zipkin==1.27.0
# prometheus_fastapi_instrumentator==7.0.0
# opentelemetry-exporter-prometheus==0.49b1
opentelemetry-distro==0.48b0
# langtrace-python-sdk==2.1.16 
# openlit==1.29.4 #==1.18.2
sentry-sdk[fastapi]
# jaeger
# jaeger-client
# opentelemetry-exporter-jaeger
traceloop-sdk==0.33.7 #==0.33.12
# llama-index==0.10.36
lancedb==0.5.7
flower==2.0.1
# taskiq==0.11.7
# opentelemetry-exporter-gcp-trace==1.7.0
# opentelemetry-exporter-gcp-monitoring==1.7.0a0
# langchain-serve==0.0.61
# google_cloud_trace==1.13.5
google_cloud_resource_manager==1.12.5
google_cloud_speech==2.27.0
google-cloud-firestore==2.18.0
grpcio_health_checking
protobuf==4.25.3
googleapis_common_protos==1.65.0

#    The user requested httpx==0.27.0
#     langsmith 0.1.104 depends on httpx<1 and >=0.23.0
#     weaviate-client 4.7.1 depends on httpx<=0.27.0 and >=0.25.0
#     anthropic 0.34.1 depends on httpx<1 and >=0.23.0
#     langserve 0.2.2 depends on httpx>=0.23.0
#     qdrant-client 1.11.0 depends on httpx>=0.20.0
#     aider-chat 0.54.12 depends on httpx==0.27.2
# aider-chat==0.55.0
# vision-agent==0.2.120
e2b-code-interpreter==1.1.1
e2b==1.3.1
# asknews==0.7.36
# newspaper3k==0.2.8
# newspaper4k==0.9.3.1
# torch
# audiocraft 1.3.0 depends on torch==2.1.0
# audiocraft==1.3.0
#   The user requested pandas==2.2.2
#     yfinance 0.2.43 depends on pandas>=1.3.0
#     haystack-ai 2.5.1 depends on pandas
#     scrapegraphai 1.19.0 depends on pandas>=2.2.2
#     newspaper4k 0.9.3.1 depends on pandas>=2.1.0; python_version >= "3.11"
#     stable-audio-tools 0.0.16 depends on pandas==2.0.2
# stable-audio-tools==0.0.16
# autochain==0.0.5
#     semantic-kernel 1.8.0 depends on pandas<3.0.0 and >=2.2.2
#     haystack-ai 2.4.0 depends on pandas
#     scrapegraphai 1.14.0 depends on pandas>=2.2.2
#     vision-agent 0.2.76 depends on pandas<3.0.0 and >=2.0.0
#     aideml 0.1.4 depends on pandas==2.1.4
# aideml==0.1.4
browserbase==0.3.0
lagent==0.2.3
janus==1.0.0
gpt-researcher==0.9.6
loguru==0.7.2
griffe==1.3.1
# need openai~=1.34.0 conflict
# mle-agent==0.4.0
unstructured==0.15.12
onnxruntime==1.19.2
langchain_anthropic==0.1.23
langchain-google-vertexai==1.0.10
burr==0.30.1
# composio-core==0.5.25
# julep==0.3.9
semantic-chunkers==0.0.9
semantic-router==0.0.65
langchain-text-splitters==0.2.4
groq==0.11.0
cohere==5.6.2
# codefuse-muagent==0.0.5
# auto-coder==0.1.173
# superagent-py==0.2.40
# bentoml==1.3.5

    # The user requested lancedb==0.5.7
    # crewai-tools 0.12.0 depends on lancedb<0.6.0 and >=0.5.4
    # graphrag 0.3.6 depends on lancedb<0.13.0 and >=0.12.0
# graphrag==0.3.6
asyncer==0.0.8
flaml==2.3.0
xgboost==2.1.1
embedchain==0.1.121
# paper-qa==5.0.7
prompttools==0.0.46
livekit-api==0.7.0
livekit==0.16.3
# gradio
promptic==0.7.7
tenacity==8.3.0
# magic-pdf==0.8.1
# git+https://github.com/SichangHe/facebookresearch--nougat.git
# nougat-ocr==0.1.17
confection==0.1.5
sqlmodel==0.0.22
rdagent==0.2.1
# pi_heif==0.18.0
unstructured_inference==0.7.37
ultralytics==8.3.0
numpy==1.26.4
lapx==0.5.10
moviepy==1.0.3
torchtext==0.18.0
fast-langdetect==0.2.0
wordninja==2.0.0
boto3 #==1.37.20
Brotli==1.1.0
# unimernet #==0.2.3
paddleocr==2.7.3
paddlepaddle==3.0.0b1
pycocotools==2.0.8
htmltabletomd==1.0.0
pypandoc==1.13
pyopenssl==24.0.0
struct-eqtable==0.1.0
statistics==*******
onnxslim==0.1.34
# eventlet==0.37.0
gevent==24.2.1
Pillow==10.4.0
pi_heif==0.18.0
# modelscope==1.18.1
# detectron2
# git+https://github.com/facebookresearch/detectron2.git
# https://github.com/opendatalab/PDF-Extract-Kit/raw/main/assets/whl/detectron2-0.6-cp310-cp310-macosx_11_0_arm64.whl
# https://github.com/opendatalab/PDF-Extract-Kit/blob/main/assets/whl/detectron2-0.6-cp310-cp310-linux_x86_64.whl
# librosa==0.10.2
# soundfile==0.12.1
# manimgl==1.6.1
docling #==2.28.2
stripe==11.2.0
r2r # requires python3.12
# recommenders[all]
portkey-ai
agenta
litellm
openmeter>=1.0.0b188
llama-index-core
llama-index-llms-openai
# pipecat-ai
# helicone
supabase
sqlalchemy