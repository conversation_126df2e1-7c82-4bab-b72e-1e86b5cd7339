For newspaper4k:

lxml requirements:
Linux:

$ sudo apt-get install libxml2-dev libxslt-dev
For PIL to recognize .jpg images:

$ sudo apt-get install libjpeg-dev zlib1g-dev libpng12-dev
NOTE: If you find problem installing libpng12-dev, try installing libpng-dev.

Install the distribution via pip:

$ pip3 install newspaper4k
If you are on OSX, install using the following, you may use both homebrew or macports:

$ brew install libxml2 libxslt

$ brew install libtiff libjpeg webp little-cms2

$ pip3 install newspaper4k
