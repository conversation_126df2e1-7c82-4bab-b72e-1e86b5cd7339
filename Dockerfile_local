FROM python:3.11


ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Install dependencies
RUN apt-get update && \
    apt-get install -y build-essential git curl gnupg nodejs redis-server supervisor && \
    # curl -fsSL https://deb.nodesource.com/setup_21.x | bash - && \
    curl -s https://packagecloud.io/install/repositories/github/git-lfs/script.deb.sh | bash && \
    apt-get install git-lfs && \
    apt-get install nodejs npm -y &&  \
    npm install -g pnpm && apt-get install -y \
    libxml2-dev \
    libxslt-dev \
    libjpeg-dev \
    zlib1g-dev \
    libpng-dev \
    --no-install-recommends && \
    apt-get install -y --no-install-recommends \
    tar \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy Python requirements
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip 
# RUN pip install --no-cache-dir --timeout=3600 torch==2.3.1+cpu  torchvision==0.18.1+cpu -f https://download.pytorch.org/whl/torch_stable.html

RUN pip install --no-cache-dir --timeout=3600 https://download.pytorch.org/whl/cpu/torch-2.3.1%2Bcpu-cp311-cp311-linux_x86_64.whl

RUN pip install --no-cache-dir --timeout=3600 https://download.pytorch.org/whl/cpu/torchvision-0.18.1%2Bcpu-cp311-cp311-linux_x86_64.whl
# RUN pip install --no-cache-dir --timeout=120 torchtext==0.18.0
# Clone torchtext from the release/0.18.0 branch
RUN git config --global http.postBuffer 21524288000   
# RUN git clone -b release/0.18.0 https://github.com/pytorch/text torchtext && \
#     cd torchtext && \
#     git submodule init && \
# git submodule update -- third_party/double-conversion  && \
# git submodule update -- third_party/re2  && \
# git submodule update -- third_party/sentencepiece && \
# git submodule update -- third_party/utf8proc  && \
#     python setup.py clean install && \
#     cd .. && \
#     rm -rf torchtext   

RUN pip install --timeout=3600 https://files.pythonhosted.org/packages/13/0a/0d1e5426dbab2171551fe1b53e3ed80f42dbcadf7c28cc3676f0e311dc2f/torchtext-0.18.0-cp311-cp311-manylinux1_x86_64.whl
# Clear pip cache to avoid hash issues
RUN pip cache purge
RUN  pip install --no-cache-dir --timeout=4800 -r requirements.txt
RUN playwright install
RUN playwright install-deps

# Verify Celery installation
RUN celery --version


# # Download and extract Weaviate
# RUN mkdir -p /root/.cache/weaviate-embedded


# RUN curl -L -o /root/.cache/weaviate-embedded/tmp_weaviate.tar.gz https://github.com/weaviate/weaviate/releases/download/v1.24.22/weaviate-v1.24.22-Linux-arm64.tar.gz
# RUN tar -xvzf /root/.cache/weaviate-embedded/tmp_weaviate.tar.gz -C /root/.cache/weaviate-embedded  --strip-components=1
# RUN ls -alh /root/.cache/weaviate-embedded/
# RUN rm -rf /root/.cache/weaviate-embedded/tmp_weaviate.tar.gz /root/.cache/weaviate-embedded/weaviate-v1.24.22-linux-arm64
# download again to keep it there for code to load
# RUN curl -L -o /root/.cache/weaviate-embedded/tmp_weaviate.tar.gz https://github.com/weaviate/weaviate/releases/download/v1.24.22/weaviate-v1.24.22-Linux-arm64.tar.gz


# RUN ls -alh /root/.cache/weaviate-embedded/
# RUN chmod +x /root/.cache/weaviate-embedded/weaviate
 
# Create the data directory for SQLite
RUN mkdir -p /app/data
# Copy your application code
COPY *.py ./
COPY app/ /app/app/
# COPY magic-pdf_docker.json /app/magic-pdf.json
# COPY magic-pdf_docker.json /root/magic-pdf.json

# Initialize Git LFS
RUN git lfs install

# # Create a directory for the PDF-Extract-Kit models
# RUN mkdir -p /app/PDF-Extract-Kit/models

# # Declare a volume for the PDF-Extract-Kit models
# VOLUME /app/PDF-Extract-Kit/models

# RUN git clone https://huggingface.co/opendatalab/PDF-Extract-Kit \
# && ls -alh /app/PDF-Extract-Kit/models

# # Clone Firecrawl repository
# RUN git clone https://github.com/mendableai/firecrawl.git /app/firecrawl

# # Install Firecrawl dependencies
# WORKDIR /app/firecrawl/apps/api
# RUN pnpm install 

# # Copy Firecrawl environment variables
# COPY ./.env.firecrawl /app/firecrawl/apps/api/.env

# Return to the original working directory
WORKDIR /app

# Expose necessary ports
EXPOSE 8080 6666 8079 50051 19530 9091 3002 3000

# Copy the supervisord configuration file
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Run Supervisor, which will start all the services
CMD ["supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]




