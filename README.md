technology/tools to try

1. RAG(verba,GraphRAG https://github.com/severian42/GraphRAG-Local-UI https://github.com/TheAiSingularity/graphrag-local-ollama https://github.com/karthik-codex/Autogen_GraphRAG_Ollama, RAGflow https://github.com/infiniflow/ragflow, anything-llm, haystack: https://github.com/deepset-ai/haystack kotaemon https://github.com/Cinnamon/kotaemon controllable RAG agent https://github.com/NirDiamant/Controllable-RAG-Agent AutoRAG https://github.com/Marker-Inc-Korea/AutoRAG)
2. Digital twins (https://github.com/anliyuan/Ultralight-Digital-Human)
3. Multimodal(audio(https://github.com/facebookresearch/audiocraft, TTS: https://github.com/NirDiamant/GenAI_Agents/blob/main/all_agents_tutorials/tts_poem_generator_agent_langgraph.ipynb),music(Suno API, midi: https://github.com/NirDiamant/GenAI_Agents/blob/main/all_agents_tutorials/music_compositor_agent_langgraph.ipynb),image(image edit: https://python.langchain.com/v0.2/docs/integrations/tools/google_imagen/), video (https://python.langchain.com/v0.2/api_reference/google_vertexai/chat_models/langchain_google_vertexai.chat_models.ChatVertexAI.html), vision-agent https://github.com/landing-ai/vision-agent serve model: https://github.com/bentoml/BentoML)
4. Local models offline(Gemma2, ollama, etc) https://github.com/Mobile-Artificial-Intelligence/maid
5. agent flow UI(langflow https://github.com/langflow-ai/langflow) dify: https://github.com/langgenius/dify autogen studio: https://github.com/microsoft/autogen/tree/main/samples/apps/autogen-studio LangGraph Studio, InvokeAI(Apache license) https://github.com/invoke-ai/InvokeAI
6. Multi-Agent/Planner(Langgraph/LangServe, autogen, multi agent, agent automation, planning, etc) auto agent/AGI ( autogen: https://github.com/microsoft/autogen + RAG, crewai: https://github.com/crewAIInc/crewAI, agent universe: https://github.com/alipay/agentUniverse internet of Agents https://github.com/OpenBMB/IoA agentK https://github.com/mikekelly/AgentK agent zero: https://github.com/frdel/agent-zero AutoGPT: https://github.com/Significant-Gravitas/AutoGPT https://github.com/superagent-ai/superagent use LangGraph to control flow, auto generate roles, task etc yaml. need streaming & user input/approval https://github.com/langchain-ai/langchain/issues/8095 https://langchain-ai.github.io/langgraph/how-tos/human_in_the_loop/wait-user-input/ https://github.com/microsoft/RD-Agent)
7. fine tuning: LLaMA-Factor: https://github.com/hiyouga/LLaMA-Factory
8. add sandbox (e2b, azure https://python.langchain.com/v0.2/docs/integrations/tools/azure_dynamic_sessions/)
9. add crawler
10. add location and map service(plan, recommendation, etc)
11. add natural voice/voice to voice(openai realtime api https://openai.com/index/introducing-the-realtime-api/ https://platform.openai.com/docs/guides/realtime)
12. add newspaper4k as content provider
13. fitness
14. meditation
15. advanced reasoning for STEM(COT, gpt o1 model, https://github.com/diagram-of-thought/diagram-of-thought https://github.com/bklieger-groq/g1 )
16. workflow (comfyUI https://github.com/comfyanonymous/ComfyUI InvokeAI https://github.com/invoke-ai/InvokeAI apache license)

areas of functions:

1. writing(storm): need to support: 1. change writing output style(not only Wikipedia, could be novel, etc) 2. bring your own documents 3. rewrite part of the final results again 4. task management
2. auto code (auto-coder-rover https://github.com/nus-apr/auto-code-rover) DSPy https://github.com/stanfordnlp/dspy or team coding for project aider-coder https://github.com/paul-gauthier/aider OpenHands(OpenDevin) https://github.com/All-Hands-AI/OpenHands
3. education/tutor(from a book/page, parse and get a flow/graph/plan to learn from it step by step. could be a skill/book/programming language/spoken language etc. learning flow could be: question and answer, selection,games, talk, listen, writing, reading comprehension, exam, drawing etc until user passed some level test. The whole progress is personalized. https://shepherd.study)
4. personalized search (mind search https://github.com/InternLM/MindSearch)
5. scientist: https://github.com/SakanaAI/AI-Scientist OpenResearcher https://github.com/GAIR-NLP/OpenResearcher https://github.com/binary-husky/gpt_academic https://github.com/microsoft/RD-Agent
6. AI generated story + image + audio(bark + audiocraft(background sound, music)) https://github.com/HVision-NKU/StoryDiffusion https://research.nvidia.com/labs/par/consistory/ https://github.com/RedAIGC/StoryMaker
7. reading assistant(arxiv, help parse book https://github.com/opendatalab/MinerU , answer questions(equations, code in books), personalized experience https://github.com/deep-diver/paper-reviewer)
8. debate
9. research literature review(https://github.com/assafelovic/gpt-researcher https://github.com/DAMO-NLP-SG/CoI-Agent https://github.com/Future-House/paper-qa https://github.com/starpig1129/ai-data-analysis-MulitAgent https://github.com/deep-diver/paper-reviewer)
10. llm driven data analysis(data source: https://github.com/airbytehq/airbyte https://python.langchain.com/v0.2/docs/integrations/tools/e2b_data_analysis/ https://github.com/Stephen-SMJ/LAMBDA) and machine learning(https://github.com/WecoAI/aideml https://github.com/MLSysOps/MLE-agent **************:DeepInsight-AI/DeepBI.git https://github.com/Wilson-ZheLin/Streamline-Analyst)
11. advanced reasoning for STEM
12. story teller(audio,script, images together)

Need independent services:

1. RAG(kotaemon)
2. workflow
3. crawler
4. stripe payments and charge balance https://github.com/songquanpeng/one-api
5. https://github.com/deep-diver/paper-reviewer https://github.com/microsoft/RD-Agent
6. add better task queue support
7. simulation: https://github.com/microsoft/TinyTroupe
8. cpu llm: https://github.com/microsoft/BitNet
9. GUI agent: https://github.com/microsoft/OmniParser
10. agent: Microsoft Magnetic One: https://www.microsoft.com/en-us/research/articles/magentic-one-a-generalist-multi-agent-system-for-solving-complex-tasks/ https://github.com/microsoft/autogen/tree/main/python/packages/autogen-magentic-one
11. multi agent: https://microsoft.github.io/autogen/dev/user-guide/agentchat-user-guide/index.html

infra/(model as a service)

1. beam https://docs.beam.cloud/v2/topics/public-endpoints

TODO items

# TODO: prepare for multimodal embedding(weaviate for image embedding,etc)

# TODO: multimodal input/output (text/image/audio/video/file/map info/ etc), UI change, parse and rendering

# TODO: Add Bard

# TODO: Add AutoGPT

# TODO: use GPT's new function call

# TODO: add more sources format for data retrieval

# TODO: add crawler from apify and crawlee

# TODO: more powerful data analysis tool(picture output, code run in sandbox etc.)

# TODO: use SelfQueryRetriever for better vector store filter results(add filter from query) https://python.langchain.com/docs/modules/data_connection/retrievers/self_query/

# TODO: use Langchain indexing https://python.langchain.com/docs/modules/data_connection/indexing to:

# TODO: allow uer to upload document include with query so user can query about this doc(data analysis, etc)

# TODO: allow uer to query document without bot_id, so this case no bot_id filter needed

# TODO: allow response contain multiple pictures, need to render pictures if included in response(change in client side)

# TODO: add Finance part using alpha-vantage data and library

# TODO: add location and map service(plan, recommendation, etc)

# TODO: add natural voice

# TODO: add airbyte for ETL https://docs.airbyte.com/quickstart/set-up-a-connection

# TODO: use better create retrieval tool and agent: from langchain.agents.agent_toolkits import create_conversational_retrieval_agent

# TODO: add graph database Neo4j for better databbase support

# TODO: use autogen for data analysis or even more complex task: https://github.com/microsoft/autogen

# TODO: use langserve

# TODO: use LangGraph

# TODO: support image vision query

# TODO: use Claude 2 100k from AWS Bedrock https://python.langchain.com/docs/integrations/llms/bedrock
