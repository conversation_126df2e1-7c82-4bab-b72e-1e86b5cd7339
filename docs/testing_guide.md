# Testing in Diogenes Chatbot Embedding Server

This document outlines the testing approach and best practices for the Diogenes Chatbot Embedding Server project.

## Testing Framework

This project uses **pytest** for running unit tests. We follow these guidelines for all test development:

-   Place unit tests under `tests/unit/` and integration tests under `tests/integration_tests/`.
-   Keep tests independent and avoid external network calls by mocking dependencies.
-   Name test functions starting with `test_`.
-   Provide clear assertions so failures are easy to diagnose.
-   Run `pytest` before opening a pull request to ensure all tests pass.

## Running Tests

To run all unit tests:

```bash
python -m pytest tests/unit/
```

To run with coverage reporting:

```bash
python -m pytest tests/unit/ --cov=app --cov-report=term-missing
```

To run specific test files:

```bash
python -m pytest tests/unit/test_util.py
```

## Test Organization

-   **Unit Tests**: Test individual functions and methods in isolation
-   **Integration Tests**: Test the interaction between components
-   **API Tests**: Test API endpoints and websocket connections

## Mocking

Use mocking to isolate the unit being tested:

```python
from unittest.mock import patch, MagicMock

@patch('app.module.some_dependency')
def test_function(mock_dependency):
    mock_dependency.return_value = "mocked_value"
    # Test code here
```

For async functions, use AsyncMock:

```python
from unittest.mock import AsyncMock

@patch('app.module.async_dependency', new_callable=AsyncMock)
async def test_async_function(mock_async_dependency):
    mock_async_dependency.return_value = "mocked_value"
    # Test code here
```

## Test Coverage

We aim for high test coverage, but prioritize meaningful tests over coverage percentages. Pay special attention to complex business logic, error handling, and edge cases.

## Fixtures

Use pytest fixtures to set up common test dependencies:

```python
@pytest.fixture
def sample_data():
    return {
        "key": "value",
        "nested": {
            "data": "test"
        }
    }

def test_with_fixture(sample_data):
    # Test uses sample_data fixture
```

## Continuous Integration

Tests are automatically run in the CI pipeline for pull requests and merge to main branch.

## Troubleshooting Common Issues

1. **Mocked dependencies not being used**: Ensure the mocked path exactly matches the import path in the file under test.
2. **Test hanging**: Check for unhandled async functions or infinite loops.
3. **Environment variable issues**: Use monkeypatch to set environment variables in tests.

## Adding New Tests

When adding new features, create corresponding tests that:

1. Verify the feature works as expected
2. Test edge cases and error conditions
3. Are properly isolated from external dependencies

For questions or clarifications about testing, consult the project maintainers.
