# Project Best Practices

This document outlines guidelines for maintaining a clean and maintainable codebase. Following these practices will ensure new contributions align with the repository architecture.

## Directory Layout

-   `app/` contains application modules, grouped by domain. Shared utilities live in `app/utils`.
-   `app/api/` holds FastAPI route handlers.
-   `app/db/` stores database abstractions and vector-store CRUD implementations.
-   `app/models/` encapsulates language models and dataclasses.
-   `docs/` holds documentation.
-   `tests/` contains unit and integration tests.
-   `data/` is reserved for small sample datasets only.

## Coding Style

-   Conform to **PEP 8** and format code with `black`.
-   Provide **type hints** and **docstrings** for public APIs.
-   Use descriptive names for variables and functions.
-   Keep functions short and focused on a single task.
-   Prefer composition over inheritance.
-   Avoid global mutable state.
-   Use the built-in `logging` package for diagnostics.
-   Always specify variable and parameter types using type hints (e.g., `def function(param: str) -> bool:`).
-   Include return type annotations for all functions and methods.

## Data Management

-   Read configuration values from environment variables with `os.environ.get`.
-   Do not commit secrets or large binary files.
-   Place reusable dataset files under `data/` and version them when practical.
-   Provide a small CRUD wrapper under `app/db/` for each database or vector store.
-   Document schema changes in the `docs/` directory.

## Model Usage

-   Keep reusable model logic in `app/models`.
-   Use factory functions to create models from configuration.
-   Document expected inputs and outputs for each model.
-   Cache heavy model initialisation outside request handlers.

## Services

-   Each API route lives in its own module under `app/api`.
-   Use dependency injection (`Depends`) to provide DB and storage clients.
-   Offload long running tasks to Celery or other async workers.
-   Validate incoming request data with Pydantic models.
-   Return structured error messages on failures.

## Databases

-   Keep database access behind interfaces in `app/db`.
-   Use typed dataclasses for documents or records.
-   Prefer async drivers where available.
-   Add indexes to frequently queried fields.

## Logging and Monitoring

-   Use the root logger from `app.core.tracing_setup`.
-   Include context such as user id or request id in log messages.
-   Add timing around expensive operations.

## Testing

-   Place new tests in `tests/`.
-   Aim for unit tests on pure functions and integration tests for services.
-   Mocks should be used to isolate external dependencies.

## Environment and Configuration

-   Provide a sample `.env.example` describing required variables.
-   Use helper functions like `setup_envs` to load defaults.
-   Avoid hard coded API keys or URLs.

## Git Workflow

-   Keep pull requests small and focused.
-   Include a summary of changes and testing steps in the PR description.
-   Do not commit generated files or virtual environment artifacts.

## TODO Management

-   Add `# TODO:` comments for incomplete implementations, temporary solutions, or known issues.
-   Include context in TODO items (e.g., `# TODO: Replace mock data with real API call when endpoint is ready`).
-   Add your initials and date to TODOs (e.g., `# TODO(JS 2025-05-23): Implement proper error handling`).
-   Review and remove TODOs regularly as part of maintenance.
-   Specify issue tracking references if applicable (e.g., `# TODO(#123): Fix performance issue in search algorithm`).
-   Avoid committing code with TODOs that lack sufficient context.

## UI Design Guidelines

-   Provide informative error messages that guide users to solutions.
-   Include tooltips or helper text for complex UI elements.
-   Display loading indicators during asynchronous operations.
-   Use consistent design patterns throughout the application.
-   Design responsive UI components that work across different screen sizes.
-   Add proper form validation with clear feedback on issues.
-   Ensure accessible design with proper ARIA attributes and keyboard navigation.
-   Follow a clear visual hierarchy to guide user attention.

## Data Presentation

-   Implement data filtering capabilities for lists and tables.
-   Add search functionality to help users find specific items quickly.
-   Include sorting options for tabular data (by column, ascending/descending).
-   Implement pagination for large datasets.
-   Show progress indicators for long-running operations.
-   Display empty states and fallback UIs when no data is available.
-   Allow users to customize views when practical (columns shown, items per page).
-   Provide reasonable defaults for all data presentation options.
-   Consider data export options for tables when applicable.

 