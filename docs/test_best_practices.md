# Testing Best Practices

This project uses **pytest** for running unit tests. Follow these guidelines when adding new tests:

- Place unit tests under `tests/unit/` and integration tests under `tests/integration_tests/`.
- Keep tests independent and avoid external network calls by mocking dependencies.
- Name test functions starting with `test_`.
- Provide clear assertions so failures are easy to diagnose.
- Run `pytest` before opening a pull request to ensure all tests pass.
