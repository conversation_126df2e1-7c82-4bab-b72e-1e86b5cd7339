# diogenesaichatbotserver (EXPERIMENTAL)
No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

This Dart package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 0.1.0
- Generator version: 7.12.0
- Build package: org.openapitools.codegen.languages.DartDioClientCodegen

## Requirements

* Dart 2.15.0+ or Flutter 2.8.0+
* Dio 5.0.0+ (https://pub.dev/packages/dio)

## Installation & Usage

### pub.dev
To use the package from [pub.dev](https://pub.dev), please include the following in pubspec.yaml
```yaml
dependencies:
  diogenesaichatbotserver: 1.0.0
```

### Github
If this Dart package is published to Github, please include the following in pubspec.yaml
```yaml
dependencies:
  diogenesaichatbotserver:
    git:
      url: https://github.com/GIT_USER_ID/GIT_REPO_ID.git
      #ref: main
```

### Local development
To use the package from your local drive, please include the following in pubspec.yaml
```yaml
dependencies:
  diogenesaichatbotserver:
    path: /path/to/diogenesaichatbotserver
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```dart
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';


final api = Diogenesaichatbotserver().getDefaultApi();
final DocumentBotRequestData documentBotRequestData = ; // DocumentBotRequestData | 
final String authorization = authorization_example; // String | 

try {
    final response = await api.assignDocumentsToBotAssignDocumentsToBotPost(documentBotRequestData, authorization);
    print(response);
} catch on DioException (e) {
    print("Exception when calling DefaultApi->assignDocumentsToBotAssignDocumentsToBotPost: $e\n");
}

```

## Documentation for API Endpoints

All URIs are relative to *http://localhost*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
[*DefaultApi*](doc/DefaultApi.md) | [**assignDocumentsToBotAssignDocumentsToBotPost**](doc/DefaultApi.md#assigndocumentstobotassigndocumentstobotpost) | **POST** /assign_documents_to_bot | Assign Documents To Bot
[*DefaultApi*](doc/DefaultApi.md) | [**createDocumentCreateDocumentPost**](doc/DefaultApi.md#createdocumentcreatedocumentpost) | **POST** /create_document | Create Document
[*DefaultApi*](doc/DefaultApi.md) | [**createItineraryItineraryPost**](doc/DefaultApi.md#createitineraryitinerarypost) | **POST** /itinerary/ | Create Itinerary
[*DefaultApi*](doc/DefaultApi.md) | [**createPaymentIntentCreatePaymentIntentPost**](doc/DefaultApi.md#createpaymentintentcreatepaymentintentpost) | **POST** /create-payment-intent | Create Payment Intent
[*DefaultApi*](doc/DefaultApi.md) | [**createPaymentSheetPaymentSheetPost**](doc/DefaultApi.md#createpaymentsheetpaymentsheetpost) | **POST** /payment-sheet | Create Payment Sheet
[*DefaultApi*](doc/DefaultApi.md) | [**deleteDocumentDeleteDocumentDelete**](doc/DefaultApi.md#deletedocumentdeletedocumentdelete) | **DELETE** /delete_document | Delete Document
[*DefaultApi*](doc/DefaultApi.md) | [**deleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost**](doc/DefaultApi.md#deletedocumentsbyfirebaseurlsdeletedocumentsbyfirebaseurlspost) | **POST** /delete_documents_by_firebase_urls | Delete Documents By Firebase Urls
[*DefaultApi*](doc/DefaultApi.md) | [**documentExistsDocumentExistsGet**](doc/DefaultApi.md#documentexistsdocumentexistsget) | **GET** /document_exists | Document Exists
[*DefaultApi*](doc/DefaultApi.md) | [**embedDocumentEmbedDocumentPost**](doc/DefaultApi.md#embeddocumentembeddocumentpost) | **POST** /embed_document | Embed Document
[*DefaultApi*](doc/DefaultApi.md) | [**generateAudioApiGenerateAudioPost**](doc/DefaultApi.md#generateaudioapigenerateaudiopost) | **POST** /generate_audio | Generate Audio Api
[*DefaultApi*](doc/DefaultApi.md) | [**generateStoryGenerateStoryPost**](doc/DefaultApi.md#generatestorygeneratestorypost) | **POST** /generate-story/ | Generate Story
[*DefaultApi*](doc/DefaultApi.md) | [**getBotDocumentsGetBotDocumentsGet**](doc/DefaultApi.md#getbotdocumentsgetbotdocumentsget) | **GET** /get_bot_documents | Get Bot Documents
[*DefaultApi*](doc/DefaultApi.md) | [**getDocumentContentGetDocumentContentPost**](doc/DefaultApi.md#getdocumentcontentgetdocumentcontentpost) | **POST** /get_document_content | Get Document Content
[*DefaultApi*](doc/DefaultApi.md) | [**getDocumentIdsByFirebaseUrlsGetDocumentIdsByFirebaseUrlsPost**](doc/DefaultApi.md#getdocumentidsbyfirebaseurlsgetdocumentidsbyfirebaseurlspost) | **POST** /get_document_ids_by_firebase_urls | Get Document Ids By Firebase Urls
[*DefaultApi*](doc/DefaultApi.md) | [**getTokenGetTokenGet**](doc/DefaultApi.md#gettokengettokenget) | **GET** /getToken | Get Token
[*DefaultApi*](doc/DefaultApi.md) | [**handlePaymentRequestPaymentPost**](doc/DefaultApi.md#handlepaymentrequestpaymentpost) | **POST** /payment | Handle Payment Request
[*DefaultApi*](doc/DefaultApi.md) | [**indexGet**](doc/DefaultApi.md#indexget) | **GET** / | Index
[*DefaultApi*](doc/DefaultApi.md) | [**indexPost**](doc/DefaultApi.md#indexpost) | **POST** / | Index
[*DefaultApi*](doc/DefaultApi.md) | [**removeDocumentsFromBotRemoveDocumentsFromBotPost**](doc/DefaultApi.md#removedocumentsfrombotremovedocumentsfrombotpost) | **POST** /remove_documents_from_bot | Remove Documents From Bot
[*DefaultApi*](doc/DefaultApi.md) | [**searchDocumentByEmbeddingSearchDocumentByEmbeddingGet**](doc/DefaultApi.md#searchdocumentbyembeddingsearchdocumentbyembeddingget) | **GET** /search_document_by_embedding | Search Document By Embedding
[*DefaultApi*](doc/DefaultApi.md) | [**searchDocumentSearchDocumentGet**](doc/DefaultApi.md#searchdocumentsearchdocumentget) | **GET** /search_document | Search Document
[*DefaultApi*](doc/DefaultApi.md) | [**startCrewaiTaskStartCrewPost**](doc/DefaultApi.md#startcrewaitaskstartcrewpost) | **POST** /start_crew | Start Crewai Task
[*DefaultApi*](doc/DefaultApi.md) | [**stripeWebhookWebhooksPost**](doc/DefaultApi.md#stripewebhookwebhookspost) | **POST** /webhooks | Stripe Webhook
[*DefaultApi*](doc/DefaultApi.md) | [**updateDocumentUpdateDocumentPut**](doc/DefaultApi.md#updatedocumentupdatedocumentput) | **PUT** /update_document | Update Document
[*DefaultApi*](doc/DefaultApi.md) | [**writingSyncWritingSyncPost**](doc/DefaultApi.md#writingsyncwritingsyncpost) | **POST** /writing_sync | Writing Sync
[*DefaultApi*](doc/DefaultApi.md) | [**writingWritingPost**](doc/DefaultApi.md#writingwritingpost) | **POST** /writing | Writing


## Documentation For Models

 - [BalanceRequest](doc/BalanceRequest.md)
 - [BalanceResponse](doc/BalanceResponse.md)
 - [BodyCreateDocumentCreateDocumentPost](doc/BodyCreateDocumentCreateDocumentPost.md)
 - [BodyCreateItineraryItineraryPost](doc/BodyCreateItineraryItineraryPost.md)
 - [BodyDeleteDocumentDeleteDocumentDelete](doc/BodyDeleteDocumentDeleteDocumentDelete.md)
 - [BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost](doc/BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost.md)
 - [BodyEmbedDocumentEmbedDocumentPost](doc/BodyEmbedDocumentEmbedDocumentPost.md)
 - [BodyGetBotDocumentsGetBotDocumentsGet](doc/BodyGetBotDocumentsGetBotDocumentsGet.md)
 - [BodyUpdateDocumentUpdateDocumentPut](doc/BodyUpdateDocumentUpdateDocumentPut.md)
 - [DocumentBotRequestData](doc/DocumentBotRequestData.md)
 - [DocumentContentRequestData](doc/DocumentContentRequestData.md)
 - [DocumentRequestData](doc/DocumentRequestData.md)
 - [HTTPValidationError](doc/HTTPValidationError.md)
 - [StoryRequest](doc/StoryRequest.md)
 - [ValidationError](doc/ValidationError.md)
 - [ValidationErrorLocInner](doc/ValidationErrorLocInner.md)


## Documentation For Authorization

Endpoints do not require authorization.


## Author



