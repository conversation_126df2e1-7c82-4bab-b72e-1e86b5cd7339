# diogenesaichatbotserver.model.StoryRequest

## Load the model package
```dart
import 'package:diogenesaichatbotserver/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**length** | **String** |  | 
**genre** | **String** |  | 
**characters** | [**BuiltList&lt;JsonObject&gt;**](JsonObject.md) |  | 
**ageGroup** | **String** |  | 
**themes** | **BuiltList&lt;String&gt;** |  | 
**briefStory** | **String** |  | 
**imageStyle** | **String** |  | 
**voice** | **String** |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


