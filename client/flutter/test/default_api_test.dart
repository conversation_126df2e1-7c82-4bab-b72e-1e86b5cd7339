import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';


/// tests for DefaultApi
void main() {
  final instance = Diogenesaichatbotserver().getDefaultApi();

  group(DefaultApi, () {
    // Assign Documents To Bot
    //
    //Future<JsonObject> assignDocumentsToBotAssignDocumentsToBotPost(DocumentBotRequestData documentBotRequestData, { String authorization }) async
    test('test assignDocumentsToBotAssignDocumentsToBotPost', () async {
      // TODO
    });

    // Create Document
    //
    //Future<JsonObject> createDocumentCreateDocumentPost(BodyCreateDocumentCreateDocumentPost bodyCreateDocumentCreateDocumentPost, { String authorization }) async
    test('test createDocumentCreateDocumentPost', () async {
      // TODO
    });

    // Create Itinerary
    //
    //Future<JsonObject> createItineraryItineraryPost(BodyCreateItineraryItineraryPost bodyCreateItineraryItineraryPost, { String authorization }) async
    test('test createItineraryItineraryPost', () async {
      // TODO
    });

    // Create Payment Intent
    //
    //Future<BalanceResponse> createPaymentIntentCreatePaymentIntentPost(BalanceRequest balanceRequest, { String authorization }) async
    test('test createPaymentIntentCreatePaymentIntentPost', () async {
      // TODO
    });

    // Create Payment Sheet
    //
    //Future<BalanceResponse> createPaymentSheetPaymentSheetPost(BalanceRequest balanceRequest, { String authorization }) async
    test('test createPaymentSheetPaymentSheetPost', () async {
      // TODO
    });

    // Delete Document
    //
    //Future<JsonObject> deleteDocumentDeleteDocumentDelete(BodyDeleteDocumentDeleteDocumentDelete bodyDeleteDocumentDeleteDocumentDelete, { String authorization }) async
    test('test deleteDocumentDeleteDocumentDelete', () async {
      // TODO
    });

    // Delete Documents By Firebase Urls
    //
    //Future<JsonObject> deleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost(BodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost bodyDeleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost, { String authorization }) async
    test('test deleteDocumentsByFirebaseUrlsDeleteDocumentsByFirebaseUrlsPost', () async {
      // TODO
    });

    // Document Exists
    //
    //Future<JsonObject> documentExistsDocumentExistsGet(String documentId, String originalUserId, { String authorization }) async
    test('test documentExistsDocumentExistsGet', () async {
      // TODO
    });

    // Embed Document
    //
    //Future<JsonObject> embedDocumentEmbedDocumentPost(BodyEmbedDocumentEmbedDocumentPost bodyEmbedDocumentEmbedDocumentPost, { String authorization }) async
    test('test embedDocumentEmbedDocumentPost', () async {
      // TODO
    });

    // Generate Audio Api
    //
    //Future<JsonObject> generateAudioApiGenerateAudioPost(BuiltList<MultipartFile> files, { String authorization, String textModel, String audioModel, String speaker1Voice, String speaker2Voice, String introInstructions, String textInstructions, String scratchPadInstructions, String preludeDialog, String podcastDialogInstructions, String editedTranscript, String userFeedback }) async
    test('test generateAudioApiGenerateAudioPost', () async {
      // TODO
    });

    // Generate Story
    //
    //Future<JsonObject> generateStoryGenerateStoryPost(StoryRequest storyRequest, { String authorization }) async
    test('test generateStoryGenerateStoryPost', () async {
      // TODO
    });

    // Get Bot Documents
    //
    //Future<JsonObject> getBotDocumentsGetBotDocumentsGet(BodyGetBotDocumentsGetBotDocumentsGet bodyGetBotDocumentsGetBotDocumentsGet, { String authorization }) async
    test('test getBotDocumentsGetBotDocumentsGet', () async {
      // TODO
    });

    // Get Document Content
    //
    //Future<JsonObject> getDocumentContentGetDocumentContentPost(DocumentContentRequestData documentContentRequestData, { String authorization }) async
    test('test getDocumentContentGetDocumentContentPost', () async {
      // TODO
    });

    // Get Document Ids By Firebase Urls
    //
    //Future<JsonObject> getDocumentIdsByFirebaseUrlsGetDocumentIdsByFirebaseUrlsPost(DocumentRequestData documentRequestData, { String authorization }) async
    test('test getDocumentIdsByFirebaseUrlsGetDocumentIdsByFirebaseUrlsPost', () async {
      // TODO
    });

    // Get Token
    //
    //Future<JsonObject> getTokenGetTokenGet(String name, String roomId, { String authorization }) async
    test('test getTokenGetTokenGet', () async {
      // TODO
    });

    // Handle Payment Request
    //
    //Future<BalanceResponse> handlePaymentRequestPaymentPost(BalanceRequest balanceRequest, { String authorization }) async
    test('test handlePaymentRequestPaymentPost', () async {
      // TODO
    });

    // Index
    //
    // Root endpoint for the API.  Returns:     str: A greeting message.
    //
    //Future<String> indexGet() async
    test('test indexGet', () async {
      // TODO
    });

    // Index
    //
    // Root endpoint for the API.  Returns:     str: A greeting message.
    //
    //Future<String> indexPost() async
    test('test indexPost', () async {
      // TODO
    });

    // Remove Documents From Bot
    //
    //Future<JsonObject> removeDocumentsFromBotRemoveDocumentsFromBotPost(DocumentBotRequestData documentBotRequestData, { String authorization }) async
    test('test removeDocumentsFromBotRemoveDocumentsFromBotPost', () async {
      // TODO
    });

    // Search Document By Embedding
    //
    //Future<JsonObject> searchDocumentByEmbeddingSearchDocumentByEmbeddingGet(BuiltList<num> embeddingVector, String originalUserId, String originalBotId, { String authorization }) async
    test('test searchDocumentByEmbeddingSearchDocumentByEmbeddingGet', () async {
      // TODO
    });

    // Search Document
    //
    //Future<JsonObject> searchDocumentSearchDocumentGet(String query, String originalUserId, { String authorization }) async
    test('test searchDocumentSearchDocumentGet', () async {
      // TODO
    });

    // Start Crewai Task
    //
    // Starts a Celery task for running the CrewAI simulation.  Args:     request (Request): The incoming request containing the agent data.  Returns:     dict: A dictionary containing the task ID and session ID.
    //
    //Future<JsonObject> startCrewaiTaskStartCrewPost({ String authorization }) async
    test('test startCrewaiTaskStartCrewPost', () async {
      // TODO
    });

    // Stripe Webhook
    //
    //Future<JsonObject> stripeWebhookWebhooksPost() async
    test('test stripeWebhookWebhooksPost', () async {
      // TODO
    });

    // Update Document
    //
    //Future<JsonObject> updateDocumentUpdateDocumentPut(BodyUpdateDocumentUpdateDocumentPut bodyUpdateDocumentUpdateDocumentPut, { String authorization }) async
    test('test updateDocumentUpdateDocumentPut', () async {
      // TODO
    });

    // Writing Sync
    //
    // This function handles the writing process by either using LangChain or directly with STORMWikiRunner. It generates an article based on the user's topic and performs research, outline generation, article creation, and polishing. The function also handles the storage of the generated article and updates progress status in Firestore.  Parameters: - request: The Starlette Request object containing the user's input. - db: The asynchronous Firestore client for database operations. - storage: The Google Cloud Firebase Storage client for file storage operations.  Returns: - A dictionary containing the generated article.
    //
    //Future<JsonObject> writingSyncWritingSyncPost({ String authorization }) async
    test('test writingSyncWritingSyncPost', () async {
      // TODO
    });

    // Writing
    //
    // This function handles the writing process by either using LangChain or directly with STORMWikiRunner. It generates an article based on the user's topic and performs research, outline generation, article creation, and polishing. The function also handles the storage of the generated article and updates progress status in Firestore.  Parameters: - request: The Starlette Request object containing the user's input. - db: The asynchronous Firestore client for database operations. - storage: The Google Cloud Firebase Storage client for file storage operations.  Returns: - A dictionary containing the generated article.
    //
    //Future<JsonObject> writingWritingPost({ JsonObject verifyFirebaseToken, String authorization }) async
    test('test writingWritingPost', () async {
      // TODO
    });

  });
}
