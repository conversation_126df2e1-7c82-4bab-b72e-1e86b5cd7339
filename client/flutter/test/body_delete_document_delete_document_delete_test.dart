import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for BodyDeleteDocumentDeleteDocumentDelete
void main() {
  final instance = BodyDeleteDocumentDeleteDocumentDeleteBuilder();
  // TODO add properties to the builder and call build()

  group(BodyDeleteDocumentDeleteDocumentDelete, () {
    // String documentId
    test('to test the property `documentId`', () async {
      // TODO
    });

    // String originalUserId
    test('to test the property `originalUserId`', () async {
      // TODO
    });

  });
}
