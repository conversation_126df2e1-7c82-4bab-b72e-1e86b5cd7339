import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for BodyEmbedDocumentEmbedDocumentPost
void main() {
  final instance = BodyEmbedDocumentEmbedDocumentPostBuilder();
  // TODO add properties to the builder and call build()

  group(BodyEmbedDocumentEmbedDocumentPost, () {
    // BuiltList<String> firebaseUrls
    test('to test the property `firebaseUrls`', () async {
      // TODO
    });

    // String userId
    test('to test the property `userId`', () async {
      // TODO
    });

    // bool boolUpdateOnExist
    test('to test the property `boolUpdateOnExist`', () async {
      // TODO
    });

    // BuiltList<String> optionalPredefinedContentTofill
    test('to test the property `optionalPredefinedContentTofill`', () async {
      // TODO
    });

  });
}
