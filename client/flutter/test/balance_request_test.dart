import 'package:test/test.dart';
import 'package:diogenesaichatbotserver/diogenesaichatbotserver.dart';

// tests for BalanceRequest
void main() {
  final instance = BalanceRequestBuilder();
  // TODO add properties to the builder and call build()

  group(BalanceRequest, () {
    // String type
    test('to test the property `type`', () async {
      // TODO
    });

    // String userId
    test('to test the property `userId`', () async {
      // TODO
    });

    // String currency (default value: 'usd')
    test('to test the property `currency`', () async {
      // TODO
    });

    // num amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // BuiltList<String> paymentMethodTypes (default value: ListBuilder())
    test('to test the property `paymentMethodTypes`', () async {
      // TODO
    });

  });
}
