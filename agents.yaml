framework: autogen
topic: create a frontend for current folder, it contains backend code
roles:
  frontend_architect:
    backstory: Expert in frontend technologies with a deep understanding of integrating
      with backend systems.
    goal: Plan and design the frontend architecture based on the backend code
    role: Frontend Architect
    tasks:
      architecture_design:
        description: Read and analyze the backend code to design a compatible frontend
          architecture.
        expected_output: Detailed document outlining the frontend architecture, including
          the components, state management, and integration points with backend.
    tools:
    - ''
  ui_ux_designer:
    backstory: Creative professional with extensive experience in designing intuitive
      and engaging user interfaces.
    goal: Create user-friendly and visually appealing designs for the frontend
    role: UI/UX Designer
    tasks:
      design_creation:
        description: Use the architecture document to design wireframes and mockups
          for the frontend.
        expected_output: High-fidelity wireframes and mockups illustrating the user
          interface design.
    tools:
    - ''
  frontend_developer:
    backstory: Seasoned frontend developer skilled in converting designs into functional
      user interfaces.
    goal: Implement the frontend based on designs and architecture
    role: Frontend Developer
    tasks:
      frontend_implementation:
        description: Translate the wireframes and mockups into a working frontend
          application.
        expected_output: Fully functional frontend application that integrates seamlessly
          with the backend.
    tools:
    - ''
dependencies: []
