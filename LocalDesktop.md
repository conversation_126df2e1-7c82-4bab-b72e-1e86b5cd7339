### **Updated System Design (with Cloud Queue Messaging)**

#### **1. General Architecture**:

-   **Cloud-Based FastAPI Application**: The FastAPI application is running in Google Cloud Run or similar service and will act as the task producer.
-   **Local Desktop with 2 GPUs**: The desktop is the task consumer, processing GPU-intensive tasks locally.
-   **Cloud-Based Queue Messaging**: Use **Google Cloud Pub/Sub** as the message queue between the FastAPI application and the local desktop. This allows the FastAPI application to submit long-running tasks to the queue, which will then be consumed by the local desktop.

---

#### **2. Components**:

1. **FastAPI Application (Cloud)**:

    - **Task Submission**: The FastAPI app exposes an API endpoint to submit tasks. These tasks are sent to a cloud-based queue (Google Cloud Pub/Sub).
    - **Task Status**: The FastAPI app exposes another API endpoint for querying the status of the task (which will be tracked in a central storage, e.g., Firestore or Redis).

2. **Google Cloud Pub/Sub (Task Queue)**:

    - **Topic/Subscription**: FastAPI submits tasks to a **Pub/Sub topic**. The local desktop will subscribe to this topic and pull tasks when GPUs are available.
    - **Message Payload**: The task data (e.g., type of task, input files, etc.) will be serialized and sent via Pub/Sub as messages.

3. **Local Desktop (Task Consumer)**:

    - **Celery Workers**: The local desktop runs **Celery workers** that listen for new tasks from Pub/Sub. These workers will handle task execution using the available GPUs.
    - **Task Queueing and Execution**: Tasks are managed by Celery and executed when a GPU becomes available, with load balancing across the two GPUs.

4. **GPU Resource Monitoring**:
    - **GPUtil**: Local desktop uses **GPUtil** to monitor GPU load and memory usage to allocate tasks only when a GPU is available.
5. **Task Status Tracking**:
    - **Firestore/Redis**: Task statuses (pending, in-progress, completed, failed) are stored in **Firestore** or **Redis**, allowing the FastAPI application to check the progress of tasks.

---

#### **3. Task Flow**:

1. **Task Submission (Cloud)**:

    - The FastAPI application receives a request to perform a task (e.g., image segmentation, music generation).
    - FastAPI validates the request and submits the task details (input files, task type, etc.) to Google Cloud Pub/Sub.

2. **Task Queueing (Pub/Sub)**:

    - The task is added to a **Pub/Sub topic**, which serves as a message queue.
    - The task includes information such as the task ID, input files, task type, user ID, and other configurations.

3. **Task Consumption (Local Desktop)**:

    - The local desktop (Celery workers) subscribes to the Pub/Sub topic.
    - When a new task arrives, a worker pulls the task from the queue.
    - The worker checks GPU availability via **GPUtil**.
    - If a GPU is available, the task is executed. If not, the task remains queued in Celery until a GPU is free.

4. **Model Loading/Execution**:

    - Based on the task type, the appropriate model (e.g., AudioCraft for music generation, OmniParse for OCR) is loaded onto the available GPU.
    - The task is executed asynchronously.

5. **Task Completion**:

    - Once the task is completed, the result (e.g., processed file, output data) is stored locally or uploaded to cloud storage (e.g., Firebase Storage, Google Cloud Storage).
    - The task status is updated in **Firestore/Redis** to reflect completion, and the result URL is returned for later retrieval.

6. **Task Status Query**:
    - Users can query the FastAPI endpoint to check the status of their task by providing a `task_id`.
    - FastAPI queries Firestore/Redis to return the current task status (e.g., pending, in-progress, completed, failed).

---

#### **4. Detailed Design of Key Components**:

##### **FastAPI Task Submission Endpoint**:

```python
from google.cloud import pubsub_v1
from fastapi import FastAPI

app = FastAPI()

publisher = pubsub_v1.PublisherClient()
topic_path = publisher.topic_path("your-project-id", "task-queue")

@app.post("/submit_task")
async def submit_task(task_type: str, input_data: dict):
    # Prepare the message
    task_id = str(uuid.uuid4())
    message_data = {
        "task_id": task_id,
        "task_type": task_type,
        "input_data": input_data
    }
    # Publish to Pub/Sub
    future = publisher.publish(topic_path, json.dumps(message_data).encode("utf-8"))
    message_id = future.result()
    return {"task_id": task_id, "message_id": message_id, "status": "task_submitted"}
```

##### **Local Desktop Task Consumer (Celery Worker)**:

```python
from celery import Celery
import GPUtil
import time
import json
from google.cloud import pubsub_v1

app = Celery('tasks', broker='redis://localhost:6379/0')

subscriber = pubsub_v1.SubscriberClient()
subscription_path = subscriber.subscription_path("your-project-id", "task-subscription")

def get_available_gpu():
    gpus = GPUtil.getAvailable(order="memory", limit=1, maxLoad=0.9, maxMemory=0.9)
    if gpus:
        return gpus[0]
    return None

@app.task
def process_task(message_data):
    task = json.loads(message_data)
    task_id = task["task_id"]
    task_type = task["task_type"]
    input_data = task["input_data"]

    gpu_id = get_available_gpu()
    if gpu_id is None:
        print(f"No GPU available for task {task_id}. Task is queued.")
        time.sleep(5)  # Retry in a few seconds
        process_task.delay(message_data)
    else:
        print(f"Processing task {task_id} on GPU {gpu_id}")
        if task_type == 'audio_generation':
            # Load AudioCraft model and process
            pass
        elif task_type == 'ocr':
            # Load OmniParse model and process
            pass
        # Update task status in Firestore or Redis
        print(f"Task {task_id} completed.")

# Start the subscriber to pull messages from Pub/Sub
def callback(message):
    process_task.delay(message.data)
    message.ack()

subscriber.subscribe(subscription_path, callback=callback)
```

---

#### **5. Additional Considerations**:

1. **Task Timeout and Retries**:

    - Configure timeouts and retries for tasks. If a task fails (e.g., GPU is not available after several retries), it should be marked as failed.

2. **Task Prioritization**:

    - Implement task prioritization if necessary. Some tasks (e.g., critical video processing) can be given higher priority and processed sooner.

3. **Model Loading Optimization**:

    - Frequently used models should be cached to minimize the loading time.
    - Models should only be unloaded if a different task type is scheduled, reducing time spent on offloading/loading models.

4. **Error Handling**:

    - Handle errors such as `ConnectionResetError`, `GPU out-of-memory`, and others, ensuring tasks are retried or properly logged.

5. **Task Monitoring and Alerts**:
    - Monitor task execution and GPU usage using system metrics. Alerts can be set up if the queue is overloaded or if a task is stuck.

---

### **Conclusion**:

-   **Cloud Queue Messaging**: Google Cloud Pub/Sub is used to decouple task submission (from the FastAPI app in the cloud) and task execution (on the local desktop).
-   **Local GPU Management**: Tasks are processed on the local desktop, with Celery workers managing the task queue and GPUtil ensuring tasks are only executed when GPUs are available.
-   **Task Status and Result Handling**: Task statuses are tracked in Firestore or Redis, allowing users to query task progress, and results can be uploaded to cloud storage for easy access.

This architecture ensures that long-running GPU tasks can be efficiently submitted from the cloud and processed on your local desktop in an asynchronous and scalable manner.
